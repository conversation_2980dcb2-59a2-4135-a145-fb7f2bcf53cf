import Request from 'luch-request';
import store from '../store';

let downLoadTimer = true;
const request = new Request();
/* 设置全局配置 */
request.setConfig(config => {
  config.baseURL = store.state.common.baseHost;
  config.header = {
    'error-message': 0,
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'x-requested-with': 'XMLHttpRequest',
    ...config.header
  };
  config.custom.showLoading = false; //是否显示请求中的loading
  config.custom.loadingText = '加载中...'; // 请求loading中的文字提示
  config.custom.loadingMask = true; //展示loading的时候，是否给一个透明的蒙层，防止触摸穿透
  config.custom.loadingTime = 800; // 在此时间内，请求还没回来的话，就显示加载中动画，单位ms
  return config;
});

/* 请求之前拦截器 */
request.interceptors.request.use(
  async config => {
    // let noCache = new Date().getTime();
    // config.headers['startTimer'] = `${noCache}`; //请求前记录时间
    config.header['token'] = store.state.common.token;
    if (config.custom.showLoading) {
      uni.showLoading({
        title: config.custom.loadingText
      });
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

/* 请求之后拦截器 */
request.interceptors.response.use(
  async response => {
    //  let endTimer = Date.parse(new Date());
    // let endTotalTimer = endTimer - response.config.headers.startTimer;
    const data = response.data;
    // if (Object.prototype.toString.call(data) === '[object Object]') {
    // 	data.endTotalTimer = endTotalTimer;
    // }
    if (response.config.custom.showLoading) {
      uni.hideLoading();
    }
    if (data.statusCode === 21000) {
      uni.showToast({
        icon: 'none',
        title: '登录失效'
      });
      setTimeout(() => {
        // store.dispatch('common/gotoLogin');
        window.location.href = './login';
      });
    } else if (
      data.success === false &&
      data.statusCode === 999 &&
      data.message
    ) {
      if (response.config.header['error-message'] != 1) {
        //如果请求头里面messageErrorAlert为1说明自己处理错误消息
        if (downLoadTimer) {
          downLoadTimer = false;
          uni.showToast({
            icon: 'none',
            title: data.message
          });
          setTimeout(() => {
            downLoadTimer = true;
          }, 1500);
        }
      }
      return Promise.reject(response.data);
    }
    return data;
  },
  error => {
    // 请求错误做点什么。
    if (error.response) {
      if (error.response.config.custom.showLoading) {
        uni.hideLoading();
      }
      if (error.response.status === 401) {
        store.dispatch('common/gotoLogin');
      } else {
        try {
          if (downLoadTimer) {
            downLoadTimer = false;
            uni.showToast({
              icon: 'none',
              title: error.response.data
            });
            setTimeout(() => {
              downLoadTimer = true;
            }, 1500);
          }
        } catch (error) {}
      }
      return Promise.reject(error.response.data);
    } else {
      return error;
    }
  }
);

export default request;
