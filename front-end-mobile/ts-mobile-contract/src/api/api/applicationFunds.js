import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 申请配套经费表格数据 */
  getSupportingFundData(params) {
    return request.get(`${apiConfig.oa()}/api/fundSupportingApply/list`, {
      params
    });
  },
  /**@desc 获取项目负责人 负责的项目 */
  getLeaderUserProjectDataList(params) {
    return request.get(`${apiConfig.oa()}/api/fundEntry/getByleaderUserlist`, {
      params
    });
  },
  /**@desc 申请配套经费 */
  handleAddSupportingFundData(data) {
    return request.post(
      `${apiConfig.oa()}/api/fundSupportingApply/save`,
      data,
      {
        header: {
          'Content-Type': 'application/json'
        }
      }
    );
  },
  /**@desc 编辑配套经费 */
  handleEditSupportingFundData(data) {
    return request.post(
      `${apiConfig.oa()}/api/fundSupportingApply/update`,
      data,
      {
        header: {
          'Content-Type': 'application/json'
        }
      }
    );
  },
  /**@desc 删除配套经费申请 */
  handleDeleteApprovalSupportingFundData(id) {
    return request.post(
      `${apiConfig.oa()}/api/fundSupportingApply/delete/${id}`,
      {},
      {
        header: {
          'Content-Type': 'application/json'
        }
      }
    );
  },
  /**@desc 获取详情 */
  handleGetSupportinFundDataDetail(id) {
    return request.get(`${apiConfig.oa()}/api/fundSupportingApply/${id}`);
  },
  /**@desc 获取 经费详情 -审批专用 */
  handleGetSupportinFundDataDetailSpecial(params) {
    return request.get(
      `${apiConfig.oa()}/api/fundSupportingApply/selectApprovalDetails`,
      {
        params
      }
    );
  },
  /**@desc 审批 */
  handleApprovalSupportingFundData(data) {
    return request.post(
      `${apiConfig.oa()}/api/fundSupportingApply/approval`,
      data,
      {
        header: {
          'Content-Type': 'application/json'
        },
        custom: {
          showLoading: true
        }
      }
    );
  }
};
