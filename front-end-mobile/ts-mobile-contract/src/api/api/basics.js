import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取人员 */
  getPersonListFullPathPost(api, method = 'GET', params) {
    return request.request({
      method: method.toUpperCase(), // 请求方法必须大写
      url: api || `${apiConfig.oa()}/employee/getEmployeeList`,
      data: params
    });
  },

  /**@desc 通过 ID 获取文件 */
  getFileListByBusinessId(businessId) {
    return request.get(
      `${apiConfig.basics()}/fileAttachment/getFileAttachmentByBusinessId?businessId=${businessId}`
    );
  },

  /**@desc 通过 ID 文件上传 */
  handleUploadFile(data) {
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: `${apiConfig.basics()}/fileAttachment/upload?moduleName=contract`,
        file: data.file,
        formData: {
          businessId: data.businessId
        },
        success(res) {
          if (res.statusCode == 200) {
            resolve(JSON.parse(res.data));
          }
        },
        fail(res) {
          resolve(JSON.parse(res.data));
        }
      });
    });
  },
  /**@desc 删除 文件 */
  handleDeleteFile(fileId) {
    return request.post(
      `${apiConfig.basics()}/fileAttachment/deleteFileId?fileid=${fileId}`,
      null,
      {
        header: {
          'Content-Type': 'application/json'
        }
      }
    );
  },
  /**@desc 上传文件**/
  uploadImageBase64(datas) {
    return request.post(
      `${apiConfig.basics()}/fileAttachment/attachment/imageBase64Upload?moduleName=contract`,
      datas
    );
  }
};
