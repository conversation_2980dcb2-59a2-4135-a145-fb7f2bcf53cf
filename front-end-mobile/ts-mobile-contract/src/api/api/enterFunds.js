import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 经费数据表格数据 */
  getFundsTableDataList(params) {
    return request.get(`${apiConfig.oa()}/api/fundEntry/list`, {
      params
    });
  },
  /**@desc 新增经费数据 */
  handleEnterFundsData(data) {
    return request.post(`${apiConfig.oa()}/api/fundEntry/save`, data, {
      header: {
        'Content-Type': 'application/json'
      }
    });
  },
  /**@desc 编辑经费数据 */
  handleEditFundData(data) {
    return request.post(`${apiConfig.oa()}/api/fundEntry/update`, data, {
      header: {
        'Content-Type': 'application/json'
      }
    });
  },
  /**@desc 删除 */
  handleDeleteFundTableData(id) {
    return request.post(`${apiConfig.oa()}/api/fundEntry/delete/${id}`);
  },
  /**@desc 查阅经费数据 */
  hanleConsultFundData(data) {
    return request.post(`${apiConfig.oa()}/api/fundEntry/consult`, data, {
      header: {
        'Content-Type': 'application/json'
      }
    });
  },
  /**@desc 获取 经费数据 详情 */
  handleGetFundDataDetail(id) {
    return request.get(`${apiConfig.oa()}/api/fundEntry/${id}`);
  },
  getAchiveFundProjectDataList(params) {
    return request.get(`${apiConfig.oa()}/api/fundEntry/getByleaderUserlist`, {
      params
    });
  },
  /**@desc 绩效考核 */
  handleAddAchiveFundData(data) {
    return request.post(`${apiConfig.oa()}/api/fundCheck/save`, data, {
      header: {
        'Content-Type': 'application/json'
      }
    });
  },
  /**@desc 获取绩效考核详情 */
  getAchiveFundDataDetail(id) {
    return $api({
      url: `${apiConfig.oa()}/api/fundCheck/getByEntryItemId/${id}`,
      method: 'get'
    });
  },
  /**@desc 获取绩效统计 */
  getFundsStatisticTableDataList(params) {
    return $api({
      url: `${apiConfig.oa()}/api/fundEntry/selectFundStatistics`,
      method: 'get',
      params
    });
  },
  /**@desc 获取绩效统计详情 */
  getFundsStatisticDataDetail(params) {
    return $api({
      url: `${apiConfig.oa()}/api/fundEntry/getStatisticsDetailed`,
      method: 'get',
      params
    });
  },
  /**@desc 获取详情 -查阅专用*/
  handleGetFundDataDetailSpecial(params) {
    return request.get(
      `${apiConfig.oa()}/api/fundEntry/selectApprovalDetails`,
      {
        params
      }
    );
  },
  /**@desc 获取详情 --查阅必备 */
  handleGetFundDataDetailSpecial(params) {
    return request.get(
      `${apiConfig.oa()}/api/fundEntry/selectApprovalDetails`,
      {
        params
      }
    );
  }
};
