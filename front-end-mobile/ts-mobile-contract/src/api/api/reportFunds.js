import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  getReportFundsTableDatas(params) {
    return request.get(`${apiConfig.oa()}/api/fundItemEscalation/list`, {
      params
    });
  },

  /**@desc 获取经费类型 */
  getFundTypeTreeData() {
    return request.get(`${apiConfig.oa()}/api/fundType/getFundTypeTree`);
  },

  /**@desc 新增经费数据 */
  handleAddReportFundsData(data) {
    return request.post(`${apiConfig.oa()}/api/fundItemEscalation/save`, data, {
      header: {
        'Content-Type': 'application/json'
      }
    });
  },
  /**@desc 编辑经费数据 */
  handleEditReportFundsData(data) {
    return request.post(
      `${apiConfig.oa()}/api/fundItemEscalation/update`,
      data,
      {
        header: {
          'Content-Type': 'application/json'
        }
      }
    );
  },
  /**@desc 删除经费数据 */
  handleDeleteReportFundData(id) {
    return request.post(
      `${apiConfig.oa()}/api/fundItemEscalation/delete/${id}`
    );
  },
  /**@desc 获取经费详情 */
  handleGetFundDataDetail(id) {
    return request.get(`${apiConfig.oa()}/api/fundEntry/${id}`);
  }
};
