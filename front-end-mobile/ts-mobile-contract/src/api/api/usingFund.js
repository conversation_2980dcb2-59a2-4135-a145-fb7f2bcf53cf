import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取使用经费表格数据 */
  getUsingFundsDataList(params) {
    return request.get(`${apiConfig.oa()}/api/fundUse/list`, {
      params
    });
  },
  /**@desc 获取经费 单数据 详情 */
  getUsingFundsDataDetail(id) {
    return request.get(`${apiConfig.oa()}/api/fundUse/${id}`);
  },
  /**@desc 获取经费使用 全部 详情 */
  handleGetAllUsingFundData(params) {
    return request.get(`${apiConfig.oa()}/api/fundUse/getdetailed`, {
      params
    });
  },
  /**@desc 申请使用项目经费 */
  handleAddFundUsingData(data) {
    return request.post(`${apiConfig.oa()}/api/fundUse/save`, data, {
      header: {
        'Content-Type': 'application/json'
      }
    });
  },
  /**@desc 编辑 使用经费 */
  handleUpdateUsingFundData(data) {
    return request.post(`${apiConfig.oa()}/api/fundUse/update`, data, {
      header: {
        'Content-Type': 'application/json'
      }
    });
  },
  /**@desc 删除 */
  handleDeleteUsingFundData(id) {
    return request.post(`${apiConfig.oa()}/api/fundUse/delete/${id}`);
  },
  /**@desc 审批 */
  handleApprovalUsingFundData(data) {
    return request.post(`${apiConfig.oa()}/api/fundUse/approval`, data, {
      header: {
        'Content-Type': 'application/json'
      }
    });
  },
  /**@desc 获取详情 -审批专用 */
  getUsingFundsDataDetailSpecial(params) {
    return request.get(`${apiConfig.oa()}/api/fundUse/selectApprovalDetails`, {
      params
    });
  },
  /**@desc 绩效考核详情 */
  getAchiveFundDataDetail(id) {
    return request.get(
      `${apiConfig.oa()}/api/fundCheck/getByEntryItemId/${id}`
    );
  }
};
