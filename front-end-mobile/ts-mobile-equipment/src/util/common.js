import { Base64 } from 'js-base64';

export default {
  isDoc: function(str) {
    return /\.(doc|docx|xls|xlsx|pdf|ofd|ppt|pptx|txt|mp4|zip|rar|7z|)$/.test(
      str.toLowerCase()
    );
  },
  isImg: function(str) {
    return /\.(gif|jpg|jpeg|png|GIF|JPG|PNG|bmp|BMP)$/.test(str.toLowerCase());
  },
  random4: function() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  },
  createUUID: function() {
    return (
      this.random4() +
      this.random4() +
      this.random4() +
      this.random4() +
      this.random4() +
      this.random4()
    );
  },
  viewerDocBase: function(path, filename) {
    var url = '';
    if (path.indexOf('http') >= 0) {
      url = path + '?fullfilename=' + filename;
    } else {
      url = `${this.$documentPreviewHost}${path}?fullfilename=${filename}`;
    }
    let a = document.createElement('a');
    a.target = '_blank';
    a.href =
      location.origin +
      '/ts-preview/onlinePreview?url=' +
      encodeURIComponent(Base64.encode(url));
    a.click();
    return false;
  },
  //获取路由及参数
  getCurPage: function() {
    let routes = getCurrentPages(); // 获取当前打开过的页面路由数组
    let curRoute = routes[routes.length - 1].route; //获取当前页面路由
    //在微信小程序或是app中，通过curPage.options；如果是H5，则需要curPage.$route.query（H5中的curPage.options为undefined，所以刚好就不需要条件编译了）
    let curParam = routes[routes.length - 1].options; //获取路由参数
    // 拼接参数
    let param = '';
    if (curParam) {
      for (let key in curParam) {
        param += `${key}=${curParam[key]}&`;
      }
      param = `?${param.substring(0, param.length - 1)}`;
    }
    return `/${curRoute}${param}`;
  }
};
