<template>
  <view class="general-hospitalization-application-box">
    <mescroll
      ref="mescroll"
      :searchInput="true"
      @getDatas="getListData"
      @setDatas="setListData"
      @datasInit="datasInit"
    >
      <view
        class="application-item"
        v-for="item in applicationList"
        :key="item.id"
        @click="handleCheckDetail(item)"
      >
        <view class="item-field">
          <view class="item-type-text">{{ item.applyType }}</view>
        </view>
        <view class="item-field">
          <view class="item-apply-dept-text">
            科室：{{ item.applyDeptName }}
          </view>
          <view class="item-apply-user-text">
            姓名：{{ item.applyUserName }}
          </view>
        </view>
        <view class="item-field">
          <view class="item-tenure-dept-text ellipsis">
            申请任职科室：{{ item.tenureDeptName }}
          </view>
          <view class="item-tenure-status-text">
            状态：<text
              :style="{ color: tenureStatus[item.tenureStatus].color }"
              >{{ item.tenureStatusText }}</text
            >
          </view>
        </view>
        <view class="item-field">
          <view class="item-date-text">
            任职时间：{{ item.tenureStartDate }} 至
            {{ item.tenureEndDate }}
          </view>
        </view>
      </view>
    </mescroll>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    mescroll
  },
  props: {
    authorizeType: {
      type: String,
      defualt: ''
    }
  },
  data() {
    return {
      applicationList: [],
      tenureStatus: {
        1: {
          label: '正常',
          color: '#19be6b'
        },
        2: {
          label: '过期',
          color: '#fa3534'
        }
      }
    };
  },
  watch: {
    authorizeType: {
      handler() {
        this.datasInit();
        this.$refs.mescroll.downCallback();
      }
    }
  },
  methods: {
    async getListData(page, successCallback, errorCallback, keywords) {
      await this.ajax
        .getMedicineDoctorLevelList({
          pageSize: page.size,
          pageNo: page.num,
          authorizeType: this.authorizeType,
          applyUserName: keywords,
          sord: 'desc',
          sidx: 'create_date'
        })
        .then(res => {
          let rows = res.rows.map(row => {
            row.tenureStatusText = this.tenureStatus[row.tenureStatus].label;
            return row;
          });
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows) {
      this.applicationList = this.applicationList.concat(rows);
    },
    datasInit() {
      this.applicationList = [];
    },
    handleCheckDetail(item) {
      uni.setStorageSync('checkDetails', JSON.stringify(item));
      uni.navigateTo({
        url: `/pages/resident-physician/application-detail`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.general-hospitalization-application-box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}
.application-item {
  background: #fff;
  padding: 16rpx;
  position: relative;
  &:not(:last-child)::after {
    content: ' ';
    position: absolute;
    bottom: 0;
    height: 1px;
    background-color: #eee;
    left: 16rpx;
    right: 0;
  }
}
.item-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
  view {
    font-size: 24rpx;
    color: #333;
  }
  .item-type-text {
    color: #666;
  }
  .item-tenure-dept-text {
    flex: 1;
    color: $theme-color;
  }
  .item-apply-dept-text,
  .item-apply-user-text,
  .item-tenure-dept-text {
    font-size: 28rpx;
  }
}
.ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>
