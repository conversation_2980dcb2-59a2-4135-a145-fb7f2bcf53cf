export default {
  downloadFile: function(filePath) {
    // #ifdef H5
    location.href = filePath;
    // #endif
    // #ifndef H5
    uni.downloadFile({
      url: filePath,
      success: data => {
        uni.saveFile({
          tempFilePath: data.tempFilePath,
          success: res => {
            uni.showToast({
              icon: 'none',
              mask: true,
              title: `文件已保存：${res.savedFilePath}`, //保存路径
              duration: 3000
            });
            setTimeout(() => {
              //打开文档查看
              uni.openDocument({
                filePath: res.savedFilePath,
                success: e => {}
              });
            }, 3000);
          },
          fail: () => {
            uni.showToast({
              icon: 'none',
              mask: true,
              title: '下载失败,请重新下载'
            });
          }
        });
      },
      fail: err => {
        uni.showToast({
          icon: 'none',
          mask: true,
          title: '下载失败,请重新下载'
        });
      }
    });
    // #endif
  }
};
