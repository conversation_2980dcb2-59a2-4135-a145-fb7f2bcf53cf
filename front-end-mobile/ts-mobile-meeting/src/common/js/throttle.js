/**
 * 节流
 * @param {Function} func 要执行的回调函数
 * @param {Number} wait 延时的时间
 * @param {Object} options: immediate表示是否立即执行;last表示是否在停止触发后回调
 * @return null
 */
export function throttle(func, wait, options) {
  let timeout,
    remaining,
    context,
    args,
    previous = 0;
  // timeout等于null,代表定时器已经完成
  // 如果没有传options默认为空
  if (!options) {
    options = {}; // 虽然没有声明options, 相当于window.options={}
  }
  let later = function() {
    // previous = +new Date();
    previous = options.immediate == false ? 0 : new Date().getTime(); // +new Date() 等同于:new Date().getTime()
    timeout = null;
    func.call(context, args);
  };
  let throttled = function() {
    context = this;
    args = arguments;
    let now = +new Date();
    if (!previous && options.immediate === false) {
      previous = now;
    }
    // 下次触发 func 剩余的时间
    remaining = wait - (now - previous);
    // 代表定时器执行完了,那么就执行n秒后(比如：3~6秒)的事件操作
    // 如果没有剩余的时间了
    if (remaining <= 0) {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      previous = now;
      // 立即执行
      func.apply(context, args);
    } else if (!timeout && options.last !== false) {
      timeout = setTimeout(later, remaining);
    }
  };
  return throttled;
}
