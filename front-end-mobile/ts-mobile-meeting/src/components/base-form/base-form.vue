<template>
  <view class="form">
    <view class="form-container">
      <u-form
        class="form-box"
        ref="uForm"
        :label-width="labelWidth || formLabelWidth"
        :label-style="labelStyle"
        :model="formData"
        :error-type="errorType"
      >
        <u-form-item
          :label-style="item.labelStyle"
          :class="[
            item.align === 'right' ? 'ts_form_item' : '',
            item.className
          ]"
          v-for="(item, index) in formList"
          :key="index"
          :required="!readOnly && item.required"
          :label-width="item.labelWidth || $formLabelWidth"
          :label-position="item.labelPosition || 'left'"
          :label="item.title"
          :prop="item.prop"
        >
          <template #left>
            <text
              v-if="item.labelSlot"
              :class="item.labelSlotClass"
              :style="item.labelSlotStyle"
              @click="labelSlotClick(item)"
            >
              {{ item.labelSlot }}
            </text>
          </template>
          <template
            #right
            v-if="
              item.type == 'radio' ||
                item.type == 'rate' ||
                item.rightSlot ||
                item.domSlot
            "
          >
            <u-radio-group
              :class="[readOnly ? 'is_readonly' : '']"
              v-if="item.type == 'radio'"
              v-model="formData[item.prop]"
              @change="radioGroupChange($event, item)"
              :wrap="item.radioCheckWrap"
            >
              <u-radio
                shape="circle"
                v-for="(radioItem, radioIndex) in item.radioList"
                :key="radioIndex"
                :name="radioItem.value"
              >
                {{ radioItem.name }}
              </u-radio>
            </u-radio-group>
            <u-rate
              :class="[readOnly ? 'is_readonly' : '']"
              v-else-if="item.type == 'rate'"
              :count="item.count"
              v-model="formData[item.prop]"
              :inactive-color="item.inactiveColor"
              :active-color="item.activeColor"
              :size="item.size"
              :active-icon="item.activeIcon"
              :inactive-icon="item.inactiveIcon"
              :custom-prefix="item.customPrefix"
            >
            </u-rate>
            <text
              v-if="item.rightSlot"
              :class="item.rightSlotClass"
              :style="item.rightSlotStyle"
              @click="rightSlotClick(item)"
            >
              {{ item.rightSlot }}
            </text>
            <slot v-if="item.domSlot" :name="item.domSlot"> </slot>
          </template>
          <template
            #default
            v-if="
              item.type == 'file' ||
                item.type == 'select' ||
                item.type == 'select-multiple' ||
                item.type == 'text' ||
                item.type == 'number' ||
                item.type == 'textarea' ||
                item.type == 'switch'
            "
          >
            <view v-if="item.type == 'switch'" class="switch_box">
              <text v-if="item.switchLabel" class="switch_label">
                {{ item.switchLabel }}
              </text>
              <u-switch
                size="34"
                :class="[readOnly ? 'is_readonly' : '']"
                v-model="formData[item.prop]"
                @change="switchChange($event, item.prop)"
              ></u-switch>
            </view>

            <u-upload
              :class="[readOnly ? 'is_readonly' : '']"
              v-else-if="item.type == 'file'"
              :max-count="item.maxCount || 9"
              width="160"
              height="160"
              :show-progress="false"
              :deletable="!readOnly"
              type="select"
              :action="item.action"
              :index="item.prop"
              :form-data="item.formData"
              :header="item.header"
              :name="item.name"
              :fileList="formData[item.propVal]"
              @on-success="uploadedFile"
              @on-remove="removeFile"
            ></u-upload>
            <u-input
              v-else
              :showRightIcon="!readOnly"
              :class="inputClass(item)"
              :key="item.props"
              :border="$formInputBorder"
              :height="item.height"
              :type="item.type"
              :placeholder="item.placeholder"
              :input-align="
                item.type
                  | inputAlignFilter($formInputAlign, $formTextareaAlign)
              "
              :maxlength="item.maxlength"
              v-model="formData[item.prop]"
              trim
              @input="item.callback ? changeInputVal($event, item) : ''"
              @click="
                !readOnly &&
                  (item.type == 'select' && item.mode == 'select'
                    ? changeSelectShow(item, index)
                    : item.type == 'select' && item.mode == 'time'
                    ? changePickerShow(item)
                    : item.type == 'select' && item.mode == 'range-picker'
                    ? chooseRangePicker(item)
                    : item.type == 'select' && item.mode == 'person'
                    ? choosePerson(item)
                    : item.type == 'select' && item.mode == 'dept'
                    ? chooseDept(item)
                    : item.type == 'select' && item.mode == 'select-device'
                    ? chooseDeviceShow(item)
                    : item.type == 'select' &&
                      item.mode == 'select-secondary-page'
                    ? chooseSecondaryPage(item)
                    : item.type === 'select' &&
                      item.mode === 'select-participants'
                    ? selectParticipantsShow(item)
                    : '')
              "
            ></u-input>
          </template>
        </u-form-item>
      </u-form>
      <view class="button-box" v-if="showSubmitButton">
        <u-button type="primary" @click="submit">{{ submitTitle }}</u-button>
      </view>
    </view>
    <u-select
      mode="single-column"
      :list="selctAllObj[clickProp]"
      :default-value="selectDefaultValue"
      v-model="selectShow"
      @confirm="selectConfirm"
    ></u-select>
    <u-picker
      :mode="clickMode"
      v-model="pickerShow"
      :params="clickParams"
      @confirm="pickerConfirm"
    ></u-picker>
    <device-select
      v-model="selectDeviceShow"
      :defaultValue="defaultValue"
      @confirm="selectDeviceConfirm"
    ></device-select>
    <!-- 时间区间 -->
    <base-time-range-picker
      v-model="rangePickerShow"
      :defaultValue="rangePickerValue"
      :format="rangePickerFormat"
      :rangeDate="rangePickerRangeDate"
      @confirm="timeRangePickerConfirm"
    ></base-time-range-picker>
    <!-- 人员选择(第一版) -->
    <!--<personnl-select-->
    <!--v-if="personnlSelectShow"-->
    <!--v-model="personnlSelectShow"-->
    <!--:value="personnlSelectArr"-->
    <!--:type="personnlSelectFormType"-->
    <!--:personnlSelectParams="personnlSelectParams"-->
    <!---</personnl-select>-->
    <!-- 人员选择组件 -->
    <select-participants
      v-if="selectParticipantsDialog"
      v-model="selectParticipantsDialog"
      :value="participantsValue"
      v-bind="participantsPamas"
    ></select-participants>
  </view>
</template>

<script>
import DeviceSelect from '@/pages/common/device-select/index.vue';
import BaseTimeRangePicker from '@/components/base-time-range-picker/index';
export default {
  name: 'base-form',
  components: { DeviceSelect, BaseTimeRangePicker },
  props: {
    formList: {
      type: Array,
      default() {
        return [];
      }
    },
    formData: {
      type: Object,
      default() {
        return {};
      }
    },
    readOnly: {
      type: Boolean,
      default: true
    },
    submitTitle: {
      type: String,
      default: '提交'
    },
    rules: {
      type: Object,
      default() {
        return {};
      }
    },
    showSubmitButton: {
      type: Boolean,
      default: true
    },
    formLabelWidth: {
      type: [String, Number],
      default: 190
    },
    labelStyle: {
      type: Object,
      default() {
        return {
          fontSize: '28rpx'
        };
      }
    },
    labelWidth: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      errorType: ['toast'],
      selctAllObj: {},
      clickProp: null,
      clickPropVal: null,
      relationProp: [],
      clickMode: null,
      clickParams: null,
      clickField: null,
      selectShow: false,

      // personnlSelectShow: false, // 人员组件弹出框
      // personnlSelectFormType: '', // 人员组件类型
      // personnlSelectArr: [], // 人员组件选中数据对象
      // personnlSelectParams: {}, // 人员组件传参

      selectParticipantsDialog: false,
      participantsPamas: {},
      participantsValue: [], // 回显

      selectDeviceShow: false,
      rangePickerShow: false,
      pickerShow: false,
      personLabel: {},
      defaultValue: [],
      rangePickerValue: [],
      rangePickerFormat: 'YYYY-MM-DD HH:mm:ss',
      rangePickerRangeDate: [],
      selectDefaultValue: [],
      deptLabel: {}
    };
  },
  filters: {
    labelPositionFilter(type, inputLable, textareaLable) {
      if (type == 'textarea' || type == 'file') {
        return textareaLable;
      } else {
        return inputLable;
      }
    },
    inputAlignFilter(type, inputAlign, textareaAlign) {
      if (type == 'textarea') {
        return textareaAlign;
      } else {
        return inputAlign;
      }
    }
  },
  watch: {
    formList: {
      handler(newVal, oldVal) {
        this.init(newVal || [], oldVal || []);
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    //绑定验证规则，解决通过props传递变量时，微信小程序会过滤掉对象中的方法，导致自定义验证规则无效
    this.$refs.uForm.setRules(this.rules);
  },
  methods: {
    //初始化选择项、校验规则
    init(newFormList, oldFormList) {
      let selctAllObj = {},
        personLabel = {},
        deptLabel = {};
      newFormList.forEach(item => {
        let prop = item.prop;
        if (item.optionList) {
          selctAllObj[prop] = item.optionList;
        }
        if (item.type == 'select' && item.mode == 'person') {
          let oldItemIndex = oldFormList.findIndex(
            oldItem =>
              oldItem.type == 'select' &&
              oldItem.mode == 'person' &&
              oldItem.prop == prop
          );
          if (oldItemIndex >= 0) {
            personLabel[prop] = JSON.parse(
              JSON.stringify(this.personLabel[prop] || [])
            );
          } else {
            personLabel[prop] = [];
          }
        } else if (item.type == 'select' && item.mode == 'dept') {
          let oldItemIndex = oldFormList.findIndex(
            oldItem =>
              oldItem.type == 'select' &&
              oldItem.mode == 'dept' &&
              oldItem.prop == prop
          );
          if (oldItemIndex >= 0) {
            deptLabel[prop] = JSON.parse(
              JSON.stringify(this.personLabel[prop] || [])
            );
          } else {
            deptLabel[prop] = [];
          }
        }
      });
      this.selctAllObj = JSON.parse(JSON.stringify(selctAllObj));
      this.personLabel = JSON.parse(JSON.stringify(personLabel));
      this.deptLabel = JSON.parse(JSON.stringify(deptLabel));

      //抛出初始化完成事件，用以解决初始化数据清空问题, 或者初始化赋值问题
      this.$emit('init-finished');
    },
    //单选
    radioGroupChange(e, item) {
      this.$set(this.formData, item.prop, e);
      if (item.callback) {
        item.callback(e);
      }
    },
    changeInputVal(e, item) {
      this.$nextTick(() => {
        this.$set(this.formData, item.prop, item.callback(e));
      });
    },
    //打开列选择器
    async changeSelectShow(e, index) {
      this.relationProp = e.relationProp || [];
      if (e.searchParams && e.searchParams.length > 0) {
        let param = [];
        for (var i = 0; i < e.searchParams.length; i++) {
          if (!this.formData[e.searchParams[i].value]) {
            this.$u.toast(e.searchParams[i].message);
            return false;
          }
          param.push(
            `${e.searchParams[i].name}=${
              this.formData[e.searchParams[i].value]
            }`
          );
        }
        let optionList = await this.getDatas(
          `${e.searchApi}?${param.join('&')}`
        );
        this.$set(this.selctAllObj, e.prop, optionList);
      }
      const idx = e.optionList.findIndex(i => {
        return i.value === this.formData[e.propVal];
      });
      this.$nextTick(() => {
        this.selectShow = true;
        this.selectDefaultValue = idx !== -1 ? [idx] : undefined;
        this.clickProp = e.prop;
        this.clickPropVal = e.propVal;
      });
    },
    // 打开设备选择器
    chooseDeviceShow(e) {
      this.defaultValue = this.formData[e.propVal] || [];
      this.$nextTick(() => {
        this.selectDeviceShow = true;
        this.clickProp = e.prop;
        this.clickPropVal = e.propVal;
      });
    },
    // 打开时间区间选择器
    chooseRangePicker(e) {
      this.rangePickerValue = this.formData[e.propVal];
      this.rangePickerFormat = e.format || this.rangePickerFormat;
      this.rangePickerRangeDate = e.rangeDate || this.rangePickerRangeDate;
      this.$nextTick(() => {
        this.rangePickerShow = true;
        this.clickProp = e.prop;
        this.clickPropVal = e.propVal;
      });
    },
    //列选择器确认事件
    selectConfirm(e) {
      if (this.formData[this.clickPropVal] !== e[0].value) {
        this.$set(this.formData, this.clickProp, e[0].label);
        this.$set(this.formData, this.clickPropVal, e[0].value);
        if (this.relationProp.length > 0) {
          for (var i = 0; i < this.relationProp.length; i++) {
            this.$set(this.formData, this.relationProp[i].prop, '');
            if (this.relationProp[i].propVal) {
              this.$set(this.formData, this.relationProp[i].propVal, '');
            }
          }
        }
      }
    },
    // 设备选择器确认事件
    selectDeviceConfirm(e) {
      if (e) {
        let names = [];
        let ids = [];
        e.forEach(item => {
          ids.push(item.id);
          names.push(item.name);
        });
        this.$set(this.formData, this.clickProp, names.join(','));
        this.$set(this.formData, this.clickPropVal, ids);
      }
    },
    // 时间区间选择器回调
    timeRangePickerConfirm(e) {
      if (e) {
        this.$set(this.formData, this.clickProp, e.join('-'));
        this.$set(this.formData, this.clickPropVal, e);
      }
    },
    //打开选择器
    changePickerShow(e) {
      this.pickerShow = true;
      this.clickMode = e.mode;
      this.clickProp = e.prop;
      this.clickParams = e.params;
      this.clickField = e.field;
    },
    // 打开人员选择组件 （第一版本）
    // handlePersonnlSelectShow(item) {
    // const { componentParams, prop, personnlVal, organizationVal } = item;
    // // 是否显示 全院按钮与外部联系人
    // this.personnlSelectParams = {
    //   ...componentParams
    // };
    //
    // // 如果有值 则回显 将数据传递 拼接为回显的格式
    // if (this.formData[personnlVal] && this.formData[personnlVal].length > 0) {
    //   this.personnlSelectArr = this.$_.cloneDeep(this.formData[personnlVal]);
    //   this.personnlSelectFormType = 'personnl';
    // }
    //
    // if (
    //   this.formData[organizationVal] &&
    //   this.formData[organizationVal].length > 0
    // ) {
    //   this.personnlSelectArr = this.$_.cloneDeep(
    //     this.formData[organizationVal]
    //   );
    //   this.personnlSelectFormType = 'organizational';
    // }
    //
    // this.personnlSelectShow = true;
    // // 设置展示名称
    // uni.$on('personSelectSetValue', obj => {
    //   this.$set(
    //     this.formData,
    //     prop,
    //     obj['data'].map(item => item[obj.name]).join(',')
    //   );
    //   let valueKey = obj.type ? organizationVal : personnlVal;
    //   this.$set(this.formData, valueKey, obj['data']);
    //   this.$set(this, 'personnlSelectArr', obj['data']);
    //   //清除监听，不清除会消耗资源
    //   uni.$off('personSelectSetValue');
    // });
    // },
    selectParticipantsShow(item) {
      this.participantsValue = this.formData[item.propVal] || []; // 回显

      this.selectParticipantsDialog = true;
      // 组件参数
      this.participantsPamas = item.params;

      uni.$on('selectParticipantsConfirm', obj => {
        if (obj.all) {
          this.$set(this.formData, [item.prop], '全院人员');
          this.$set(this.formData, [item.propVal], true);
        } else {
          this.$set(
            this.formData,
            [item.prop],
            obj['data']
              .map(formItem => formItem[item.params.dataKey.name])
              .join(',')
          );

          this.$set(this.formData, [item.propVal], obj['data']);
        }

        uni.$off('selectParticipantsConfirm');
      });
    },
    async getDatas(api) {
      let datas = await this.ajax.getDatas(api);
      return datas.object.map(item => {
        return {
          label: item.name,
          value: item.id
        };
      });
    },
    //时间确认事件
    pickerConfirm(e) {
      if (this.clickMode == 'time') {
        this.$set(
          this.formData,
          this.clickProp,
          this.timePickerConfirm(e, this.clickField)
        );
      }
    },
    //格式化时间
    timePickerConfirm(e, field = 'yy-MM-dd') {
      if (field == 'YY') {
        return `${e.year}年`;
      } else if (field == 'yy-MM') {
        return `${e.year}-${e.month}`;
      } else if (field == 'yy-MM-dd') {
        return `${e.year}-${e.month}-${e.day}`;
      } else if (field == 'HH:mm') {
        return `${e.hour}:${e.minute}`;
      } else if (field == 'yy-MM-dd HH:mm') {
        return `${e.year}-${e.month}-${e.day} ${e.hour}:${e.minute}`;
      } else if (field == 'yy-MM-dd HH:mm:ss') {
        return `${e.year}-${e.month}-${e.day} ${e.hour}:${e.minute}:${e.second}`;
      }
    },
    //选择人员
    choosePerson(item) {
      let personList = this.personLabel[item.prop];
      uni.setStorageSync('person_list', JSON.stringify(personList));
      let personPageParams = {
        title: item.name
      };
      if (item.getListType == 'scollSearch' || item.getListType == 'search') {
        let params = {
          api: item.searchApi
        };
        //获取查询参数
        for (var i = 0; i < item.searchParams.length; i++) {
          if (!this.formData[item.searchParams[i].value]) {
            this.$u.toast(item.searchParams[i].message);
            return false;
          }
          params[item.searchParams[i].name] = this.formData[
            item.searchParams[i].value
          ];
        }
        personPageParams = { ...personPageParams, ...params };
      }
      uni.setStorageSync('personPageParams', JSON.stringify(personPageParams));
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('trasenPerson', data => {
        this.$set(this.personLabel, item.prop, data);
        let personName = [],
          personId = [];

        data.map(i => {
          personName.push(
            i.empPhoto ? `${i.empName}(${i.empPhoto})` : i.empName
          );
          personId.push(i.empCode);
        });
        this.$set(this.formData, item.prop, personName.join(','));
        this.$set(this.formData, item.propVal, personId);
        uni.removeStorageSync('personPageParams');
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('trasenPerson');
      });
      uni.navigateTo({
        url: `/pages/choose-person/choose-person?chooseType=${item.chooseType}&getListMode=${item.getListMode}`
      });
    },
    chooseDept(item) {
      let deptList = this.deptLabel[item.prop] || [];
      uni.setStorageSync('dept_list', JSON.stringify(deptList));
      let deptPageParams = {
        title: item.name
      };
      if (item.getListType == 'scollSearch' || item.getListType == 'search') {
        let params = {};
        //获取查询参数
        for (var i = 0; i < item.searchParams.length; i++) {
          if (!this.form[item.searchParams[i].value]) {
            this.$u.toast(item.searchParams[i].message);
            return false;
          }
          params[item.searchParams[i].name] = this.form[
            item.searchParams[i].value
          ];
        }
        deptPageParams = { ...deptPageParams, params, api: item.searchApi };
      }
      uni.setStorageSync('deptPageParams', JSON.stringify(deptPageParams));
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('trasenDept', data => {
        this.$set(this.deptLabel, item.prop, data);
        let deptName = [],
          deptId = [];
        data.map(i => {
          deptName.push(i.name);
          deptId.push(i.id);
        });
        this.$set(this.formData, item.prop, deptName.join(','));
        this.$set(this.formData, item.propVal, deptId);
        uni.removeStorageSync('deptPageParams');
        uni.removeStorageSync('dept_list');
        //清除监听，不清除会消耗资源
        uni.$off('trasenDept');
      });

      uni.navigateTo({
        url: `/pages/choose-dept/choose-dept?chooseType=${
          item.chooseType
        }&getListMode=${item.getListMode}&mode=${item.selectMode || 'scroll'}`
      });
    },
    // 次级页面选择器
    chooseSecondaryPage(e) {
      let secondaryPageList = this.selctAllObj[e.prop];
      uni.setStorageSync(
        'secondary_page_list',
        JSON.stringify(secondaryPageList)
      );
      let config = {
        title: e.title,
        selectKeys: [this.formData[e.propVal]]
      };
      uni.setStorageSync('secondary_page_config', JSON.stringify(config));
      uni.$on('chooseSecondaryPage', data => {
        let names = [],
          ids = [];
        data.map(i => {
          names.push(i.label);
          ids.push(i.value);
        });
        this.$set(this.formData, e.prop, names.join(','));
        this.$set(this.formData, e.propVal, ids.join(','));

        uni.removeStorageSync('secondary_page_list');
        uni.removeStorageSync('secondary_page_config');
        //清除监听，不清除会消耗资源
        uni.$off('chooseSecondaryPage');
      });
      uni.navigateTo({
        url: `/pages/choose-secondary-page/index`
      });
    },
    //上传文件
    uploadedFile(data, index, lists, name) {
      if (data.success && data.statusCode == 200) {
        let list = data.object.map(item => {
          return {
            fileUrl: item.filePath,
            fkFileId: item.fileId,
            fkFileName: item.fileRealName
          };
        });
        this.formData[name] = list;
      }
    },
    //删除文件
    removeFile(index, lists, name) {
      this.formData[name].splice(index, 1);
    },
    //label右侧控件点击事件
    labelSlotClick(e) {
      if (e.labelSlotCallback) {
        e.labelSlotCallback(e);
      }
    },
    rightSlotClick(e) {
      if (e.labelSlotCallback) {
        e.rightSlotClick(e);
      }
    },
    validate() {
      let validVal = false;
      this.$refs.uForm.validate(valid => {
        validVal = valid;
      });
      return validVal;
    },
    //提交表单
    submit() {
      this.$refs.uForm.validate(valid => {
        if (valid) {
          this.$emit('submit', valid);
        }
      });
    },
    switchChange(val, field) {
      this.$emit('switchChange', { field, value: val });
    },
    inputClass(item) {
      let classList = [];
      if (item.type == 'select') {
        classList.push('is_select');
      }
      if (this.readOnly) {
        classList.push('is_readonly');
      }
      return classList.concat(item.class);
    }
  }
};
</script>

<style lang="scss" scoped>
.form-container {
  margin-top: $uni-spacing-col-base;
}
.form-box {
  padding: 0 $uni-spacing-row-lg;
  background-color: #ffffff;
}
.button-box {
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
}
/deep/.ts_form_item {
  .u-form-item--right__content__slot {
    display: flex;
    justify-content: flex-end;
  }
}
.switch_box {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  .switch_label {
    font-size: 28rpx;
    font-weight: 400;
    color: #bbb;
    margin-right: 16rpx;
  }
}
.is_readonly,
/deep/.is_select input {
  pointer-events: none;
}
/deep/.text_red .uni-input-input {
  color: #e24242;
}
</style>
