<template>
  <view>
    <u-popup
      class="base_screen"
      v-model="visible"
      mode="right"
      z-index="989"
      width="608"
    >
      <view class="base_screen_container">
        <scroll-view scroll-y="true" style="height: calc(100% - 360rpx)">
          <view class="content">
            <view
              class="screen_item"
              v-for="(screenItem, screenIdx) in screenList"
              :key="screenIdx"
            >
              <view class="screen_item_label">
                {{ screenItem.label }}
              </view>
              <!-- 单选 -->
              <ts-radio
                :key="screenItem.prop"
                v-if="screenItem.mode === 'radio'"
                v-model="formData[screenItem.prop]"
                :option="screenItem.option"
              />
              <!-- 复选 -->
              <ts-checkbox
                v-else-if="screenItem.mode === 'checkbox'"
                :key="screenItem.prop"
                v-model="formData[screenItem.prop]"
                :option="screenItem.option"
              />
              <!-- 输入框 -->
              <u-input
                v-else
                :key="screenItem.prop"
                :border="$formInputBorder"
                :height="screenItem.height"
                :type="screenItem.type"
                :placeholder="screenItem.placeholder"
                :input-align="screenItem.inputAlign"
                :maxlength="screenItem.maxlength"
                v-model="formData[screenItem.prop]"
                trim
                @input="
                  screenItem.callback ? changeInputVal($event, screenItem) : ''
                "
                @click="
                  !screenItem.readOnly &&
                    (screenItem.type == 'select' &&
                    screenItem.mode == 'dateRange'
                      ? chooseRangePicker(screenItem, screenIdx)
                      : screenItem.type == 'select' &&
                        screenItem.mode == 'person'
                      ? choosePerson(screenItem)
                      : screenItem.type == 'select' && screenItem.mode == 'dept'
                      ? chooseDept(screenItem)
                      : '')
                "
              ></u-input>
            </view>
          </view>
        </scroll-view>
        <view class="footer">
          <u-button class="footer_btn" @click="handleReset">
            重置
          </u-button>
          <view style="width:16rpx"></view>
          <u-button class="footer_btn" @click="handleSave">
            确定
          </u-button>
        </view>
      </view>
    </u-popup>
    <!-- 时间区间 -->
    <base-time-range-picker
      v-model="rangePickerShow"
      :defaultValue="formData[clickData.propVal]"
      :format="clickData.format"
      :rangeDate="clickData.rangeDate"
      @confirm="timeRangePickerConfirm"
    ></base-time-range-picker>
  </view>
</template>

<script>
import tsRadio from './radio.vue';
import tsCheckbox from './checkbox.vue';
import BaseTimeRangePicker from '@/components/base-time-range-picker/index';
export default {
  components: { tsRadio, tsCheckbox, BaseTimeRangePicker },
  props: {
    screenList: {
      type: Array,
      default: () => []
    },
    screenData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      visible: false,
      formData: this.screenData,
      rangePickerShow: false,
      clickData: {}, // 当前操作对象
      clickIdx: ''
    };
  },
  methods: {
    open() {
      this.visible = true;
    },
    // 确定
    handleSave() {
      this.$emit('save', this.formData);
      this.visible = false;
    },
    // 重置
    handleReset() {
      this.$emit('reset');
      this.visible = false;
    },
    changeInputVal(e, item) {
      this.$nextTick(() => {
        this.$set(this.formData, item.prop, item.callback(e));
      });
    },
    //打开选择器
    chooseRangePicker(e, idx) {
      this.$nextTick(() => {
        this.clickData = e;
        this.clickIdx = idx;
        this.rangePickerShow = true;
      });
    },
    // 时间区间选择器回调
    timeRangePickerConfirm(e) {
      if (e) {
        this.$set(this.formData, this.clickData.prop, e.join('-'));
        this.$set(this.formData, this.clickData.propVal, e);
      }
    },
    //选择人员
    choosePerson(item) {
      let personList = [];
      const personIdList = this.formData[item.propVal] || [];
      if (personIdList.length > 0) {
        const personNameList = this.formData[item.prop]
          ? this.formData[item.prop].split(',')
          : [];
        for (let index = 0; index < personIdList.length; index++) {
          personList.push({
            empCode: personIdList[index],
            empName: personNameList[index]
          });
        }
      }
      uni.setStorageSync('person_list', JSON.stringify(personList));
      let personPageParams = {
        title: item.label
      };
      if (item.getListType == 'scollSearch' || item.getListType == 'search') {
        let params = {
          api: item.searchApi
        };
        //获取查询参数
        for (var i = 0; i < item.searchParams.length; i++) {
          if (!this.formData[item.searchParams[i].value]) {
            this.$u.toast(item.searchParams[i].message);
            return false;
          }
          params[item.searchParams[i].name] = this.formData[
            item.searchParams[i].value
          ];
        }
        personPageParams = { ...personPageParams, ...params };
      }
      uni.setStorageSync('personPageParams', JSON.stringify(personPageParams));
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('trasenPerson', data => {
        let personName = [],
          personId = [];

        data.map(i => {
          personName.push(
            i.empPhoto ? `${i.empName}(${i.empPhoto})` : i.empName
          );
          personId.push(i.empCode);
        });
        this.$set(this.formData, item.prop, personName.join(','));
        this.$set(this.formData, item.propVal, personId);
        uni.removeStorageSync('personPageParams');
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('trasenPerson');
      });
      uni.navigateTo({
        url: `/pages/choose-person/choose-person?chooseType=${item.chooseType}&getListMode=${item.getListMode}`
      });
    },
    chooseDept(item) {
      let deptList = [];
      const deptIdList = this.formData[item.propVal] || [];
      if (deptIdList.length > 0) {
        const deptNameList = this.formData[item.prop]
          ? this.formData[item.prop].split(',')
          : [];
        for (let index = 0; index < deptIdList.length; index++) {
          deptList.push({
            id: deptIdList[index],
            name: deptNameList[index]
          });
        }
      }
      uni.setStorageSync('dept_list', JSON.stringify(deptList));
      let deptPageParams = {
        title: item.name
      };
      if (item.getListType == 'scollSearch' || item.getListType == 'search') {
        let params = {};
        //获取查询参数
        for (var i = 0; i < item.searchParams.length; i++) {
          if (!this.formData[item.searchParams[i].value]) {
            this.$u.toast(item.searchParams[i].message);
            return false;
          }
          params[item.searchParams[i].name] = this.formData[
            item.searchParams[i].value
          ];
        }
        deptPageParams = { ...deptPageParams, params, api: item.searchApi };
      }
      uni.setStorageSync('deptPageParams', JSON.stringify(deptPageParams));
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('trasenDept', data => {
        let deptName = [],
          deptId = [];
        data.map(i => {
          deptName.push(i.name);
          deptId.push(i.id);
        });
        this.$set(this.formData, item.prop, deptName.join(','));
        this.$set(this.formData, item.propVal, deptId);
        uni.removeStorageSync('deptPageParams');
        uni.removeStorageSync('dept_list');
        //清除监听，不清除会消耗资源
        uni.$off('trasenDept');
      });

      uni.navigateTo({
        url: `/pages/choose-dept/choose-dept?chooseType=${
          item.chooseType
        }&getListMode=${item.getListMode}&mode=${item.selectMode || 'scroll'}`
      });
    }
  },
  watch: {
    screenData: {
      handler(newValue) {
        this.formData = newValue;
      },
      deep: true,
      immediate: true
    }
  }
};
</script>
<style lang="scss" scoped>
.base_screen {
  .base_screen_container {
    height: 100%;
    padding: 0 32rpx;
    padding-top: 90rpx;
    .content {
      .screen_item {
        .screen_item_label {
          font-size: 32rpx;
          font-weight: 400;
          color: #333333;
          margin-bottom: 16rpx;
        }
      }
      .screen_item + .screen_item {
        margin-top: 32rpx;
      }
    }
    .footer {
      margin-top: 32rpx;
      display: flex;
      .footer_btn {
        flex: 1;
        font-size: 18px;
        font-weight: 400;
        color: #333333;
        border-radius: 4px;
      }
      & > .footer_btn:first-child {
        border: 1px solid #eee;
      }
      & > .footer_btn:first-child:after {
        border: 0;
      }
      & > .footer_btn:last-child {
        background: #005bac;
        border-radius: 4px;
        color: #ffffff;
        border: 0;
      }
    }
  }
}
</style>
