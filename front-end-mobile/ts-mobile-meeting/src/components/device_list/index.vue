<template>
  <view class="device_list" ref="deviceList">
    <view
      class="device_item"
      v-for="(deviceItem, index) in options"
      :key="index"
    >
      <view v-if="index < length">
        {{ deviceItem.name }}
      </view>
    </view>
    <view v-if="options && length != options.length">...</view>
  </view>
</template>

<script>
export default {
  props: {
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      deviceListWidth: 0,
      itemTotalWidth: 0,
      length: 0
    };
  },
  created() {
    this.length;
  },
  mounted() {
    this.getDeviceList();
  },
  methods: {
    getDeviceList() {
      uni
        .createSelectorQuery()
        .in(this)
        .select('.device_list')
        .boundingClientRect()
        .exec(res => {
          if (res[0]) {
            this.deviceListWidth = res[0].width;
            this.getDeviceItem();
          } else {
            this.$nextTick(() => {
              this.getDeviceList();
            });
          }
        });
    },
    getDeviceItem() {
      uni
        .createSelectorQuery()
        .in(this)
        .selectAll('.device_item')
        .boundingClientRect()
        .exec(res => {
          let ItemList = res[0];
          let length = 0;
          for (let i = 0; i < ItemList.length; i++) {
            const e = ItemList[i];
            this.itemTotalWidth = this.itemTotalWidth + e.width + 8;
            if (this.itemTotalWidth > this.deviceListWidth) {
              break;
            } else {
              length++;
            }
          }
          this.length = length;
        });
    }
  },
  watch: {
    options: {
      handler(newVal, oldVal) {
        this.length = this.options ? this.options.length : 0;
      },
      immediate: true,
      deep: true
    }
  }
};
</script>
<style lang="scss" scoped>
.device_list {
  display: flex;
  align-items: center;
  .device_item {
    & > view {
      height: 40rpx;
      line-height: 36rpx;
      padding: 0 20rpx;
      background: #fafafa;
      border-radius: 20rpx;
      border: 2rpx solid #e4e4e4;
      font-size: 22rpx;
      font-weight: 400;
      color: #333333;
      white-space: nowrap;
    }
  }
  .device_item + .device_item {
    margin-left: 16rpx;
  }
}
</style>
