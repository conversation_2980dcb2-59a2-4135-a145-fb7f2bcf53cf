/* 下拉刷新区域 */
.mescroll-downwarp {
	position: absolute;
	top: -100%;
	left: 0;
	width: 100%;
	height: 100%;
	text-align: center;
}

/* 下拉刷新--内容区,定位于区域底部 */
.mescroll-downwarp .downwarp-content {
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	min-height: 60rpx;
	padding: 20rpx 0;
	text-align: center;
}

/* 下拉刷新--提示文本 */
.mescroll-downwarp .downwarp-tip {
	display: inline-block;
	font-size: 28rpx;
	vertical-align: middle;
	margin-left: 16rpx;
	/* color: gray; 已在style设置color,此处删去*/
}

/* 下拉刷新--旋转进度条 */
.mescroll-downwarp .downwarp-progress {
	display: inline-block;
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	border: 2rpx solid gray;
	border-bottom-color: transparent !important; /*已在style设置border-color,此处需加 !important*/
	vertical-align: middle;
}

/* 旋转动画 */
.mescroll-downwarp .mescroll-rotate {
	animation: mescrollDownRotate 0.6s linear infinite;
}

@keyframes mescrollDownRotate {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}