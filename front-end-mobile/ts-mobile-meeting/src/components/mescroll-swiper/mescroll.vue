<template>
  <!--
	swiper中的transfrom会使fixed失效,此时用height="100%"固定高度; 
	swiper中无法触发mescroll-mixins.js的onPageScroll和onReachBottom方法,只能用mescroll-uni,不能用mescroll-body
	-->
  <view class="swiper-item-content">
    <view v-if="searchInput" class="search">
      <uni-search-bar
        radius="100"
        placeholder="搜索"
        borderColor="transparent"
        bgColor="#F4F4F4"
        cancelButton="none"
        @confirm="search"
      />
    </view>
    <view class="scroll-wrap">
      <mescroll-uni
        ref="mescrollRef"
        @init="mescrollInit"
        :down="downOption"
        @down="downCallback"
        :up="upOption"
        :fixed="false"
        @up="upCallback"
        @emptyclick="emptyClick"
      >
        <!-- 数据列表 -->
        <slot></slot>
      </mescroll-uni>
    </view>
  </view>
</template>

<script>
import MescrollMixin from './js/mescroll-mixins.js';
import MescrollUni from './mescroll-uni.vue';
import MescrollMoreItemMixin from './js/mescroll-more-item.js';
/**
 * mescroll 下拉刷新上拉加载组件
 * @description 主要用于下拉刷新上拉加载
 * @property {Boolean} searchInput = [true|false] 是否显示搜索框
 * @event {Boolean} down 是否自动加载
 * @event {String|Number} mescrollIndex mescroll色索引
 * @event {Function} search 键盘点击搜索时触发
 * @event {Function} downCallback 下拉时点击时触发
 * @event {Function} upCallback 上拉时点击时触发
 * @event {Function} getListData 联网获取数据
 * @event {Function} setListData 设置数据
 */
export default {
  mixins: [MescrollMixin, MescrollMoreItemMixin], // 注意此处还需使用MescrollMoreItemMixin (必须写在MescrollMixin后面)
  components: {
    MescrollUni
  },
  props: {
    searchInput: {
      type: Boolean,
      default() {
        return false;
      }
    },
    down: {
      type: Boolean,
      default() {
        return true;
      }
    },
    mescrollIndex: {
      type: [Number, String],
      default() {
        return 0;
      }
    },
    upUse: {
      type: Boolean,
      default() {
        return true;
      }
    },
    page: {
      type: Object,
      default() {
        return {
          num: 0, // 当前页码,默认0,回调之前会加1,即callback(page)会从1开始
          size: 15 // 每页数据的数量
        };
      }
    }
  },
  computed: {},
  data() {
    return {
      downOption: {
        auto: this.down, // 自动加载 (mixin已处理第一个tab触发downCallback)
        use: true
      },
      upOption: {
        use: this.upUse,
        auto: false, // 不自动加载
        isBounce: false, //禁止IOS橡皮回弹效果
        page: this.page,
        noMoreSize: 4, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
        empty: {
          tip: '暂无相关数据', // 提示
          use: true
        }
      },
      keyword: '' //搜索关键字
    };
  },
  methods: {
    /*下拉刷新的回调 */
    downCallback() {
      // 下拉刷新的回调,默认重置上拉加载列表为第一页 (自动执行 page.num=1, 再触发upCallback方法 )
      this.mescroll.resetUpScroll();
    },
    /*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
    upCallback(page) {
      let _self = this;
      //联网加载数据
      setTimeout(function() {
        //联网加载数据
        _self.getListData(
          page,
          function(data, totalCount) {
            //联网成功的回调,隐藏下拉刷新和上拉加载的状态;
            if (page.num == 1)
              _self.$emit('datasInit', _self.keyword, _self.mescrollIndex);

            _self.mescroll.endSuccess(data.length); //传参:数据的总数; mescroll会自动判断列表如果无任何数据,则提示空;列表无下一页数据,则提示无更多数据;
            //设置列表数据
            _self.setListData(data, totalCount);
          },
          function() {
            //联网失败的回调,隐藏下拉刷新和上拉加载的状态;
            _self.mescroll.endErr();
          }
        );
      }, 400);
    },
    //点击空布局按钮的回调
    emptyClick() {},
    search(res) {
      let _self = this;
      _self.keyword = res.value;
      _self.$emit('datasInit', _self.keyword, _self.mescrollIndex);
      _self.$nextTick(() => {
        _self.mescroll.resetUpScroll();
      });
    },
    getListData(page, successCallback, errorCallback) {
      let _self = this;
      _self.$emit(
        'getDatas',
        page,
        successCallback,
        errorCallback,
        _self.keyword,
        _self.mescrollIndex
      );
    },
    setListData(rows, totalCount) {
      let _self = this;
      _self.$emit('setDatas', rows, totalCount, _self.mescrollIndex);
    }
  }
};
</script>

<style lang="scss" scoped>
.swiper-item-content {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  flex-direction: column;
}
.search {
  z-index: 10;
}
.scroll-wrap {
  flex: 1;
  overflow: hidden;
}
</style>
