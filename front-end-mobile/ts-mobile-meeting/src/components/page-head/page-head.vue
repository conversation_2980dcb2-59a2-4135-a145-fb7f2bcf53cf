<template>
  <view class="uni-navbar">
    <view
      :class="{
        'uni-navbar--fixed': fixed,
        'uni-navbar--shadow': shadow,
        'uni-navbar--border': border
      }"
      :style="{ 'background-color': backgroundColor }"
      class="uni-navbar__content"
    >
      <uni-status-bar v-if="statusBar" />
      <view
        :style="{ color: leftColor, backgroundColor: backgroundColor }"
        class="uni-navbar__header uni-navbar__content_view"
      >
        <view
          @tap="onClickLeft"
          class="uni-navbar__header-btns uni-navbar__header-btns-left uni-navbar__content_view"
        >
          <view
            class="uni-navbar__content_view"
            v-if="leftIcon.length && isleft"
          >
            <uni-icons :color="leftColor" :type="leftIcon" size="48" />
          </view>
          <view
            :class="{ 'uni-navbar-btn-icon-left': !leftIcon.length }"
            class="uni-navbar-btn-text uni-navbar__content_view"
            v-if="leftText.length"
          >
            <text :style="{ color: leftColor, fontSize: '28rpx' }">{{
              leftText
            }}</text>
          </view>
          <slot name="left" />
        </view>
        <view class="uni-navbar__header-container uni-navbar__content_view">
          <view
            class="uni-navbar__header-container-inner uni-navbar__content_view"
            v-if="title.length"
          >
            <text class="uni-nav-bar-text" :style="{ color: titleColor }">{{
              title
            }}</text>
          </view>
          <!-- 标题插槽 -->
          <slot />
        </view>
        <!-- <view :class="title.length ? 'uni-navbar__header-btns-right' : ''" @tap="onClickRight" class="uni-navbar__header-btns uni-navbar__content_view"> -->
        <view
          @tap="onClickRight"
          class="uni-navbar__header-btns uni-navbar__content_view uni-navbar__header-btns-right"
        >
          <view class="uni-navbar__content_view" v-if="rightIcon.length">
            <uni-icons :color="rightColor" :type="rightIcon" size="48" />
          </view>
          <view
            class="uni-navbar-btn-text uni-navbar__content_view"
            v-if="rightText.length && !rightIcon.length"
          >
            <text
              class="uni-nav-bar-right-text"
              :style="{ color: rightColor }"
              >{{ rightText }}</text
            >
          </view>
          <slot name="right" />
        </view>
      </view>
    </view>
    <view
      class="uni-navbar__placeholder"
      :style="{ backgroundColor: backgroundColor }"
      v-if="fixed"
    >
      <uni-status-bar v-if="statusBar" />
      <view class="uni-navbar__placeholder-view" />
    </view>
  </view>
</template>
<script>
import uniStatusBar from '../uni-status-bar/uni-status-bar.vue';
import uniIcons from '../uni-icons/uni-icons.vue';
/**
 * NavBar 自定义导航栏
 * @description 导航栏组件，主要用于头部导航
 * @property {String} title 标题文字
 * @property {String} leftText 左侧按钮文本
 * @property {String} rightText 右侧按钮文本
 * @property {String} leftIcon 左侧按钮图标
 * @property {String} rightIcon 右侧按钮图标
 * @property {String} color 图标和文字颜色
 * @property {String} backgroundColor 导航栏背景颜色
 * @property {Boolean} fixed = [true|false] 是否固定顶部
 * @property {Boolean} statusBar = [true|false] 是否包含状态栏
 * @property {Boolean} shadow = [true|false] 导航栏下是否有阴影
 * @property {Boolean} border = [true|false] 导航栏下是否有边框
 * @event {Function} clickLeft 左侧按钮点击时触发
 * @event {Function} clickRight 右侧按钮点击时触发
 */
export default {
  name: 'page-head',
  components: {
    uniStatusBar,
    uniIcons
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    isleft: {
      type: [Boolean, String],
      default: true
    },
    leftText: {
      type: String,
      default: ''
    },
    rightText: {
      type: String,
      default: ''
    },
    leftIcon: {
      type: String,
      default: 'arrowleft'
    },
    rightIcon: {
      type: String,
      default: ''
    },
    fixed: {
      type: [Boolean, String],
      default: true
    },
    leftColor: {
      type: String,
      default: '#000000'
    },
    titleColor: {
      type: String,
      default: '#000000'
    },
    rightColor: {
      type: String,
      default: '#005BAC'
    },
    backgroundColor: {
      type: String,
      default: '#FFFFFF'
    },
    statusBar: {
      type: [Boolean, String],
      default: false
    },
    shadow: {
      type: [Boolean, String],
      default: false
    },
    border: {
      type: [Boolean, String],
      default: false
    }
  },
  methods: {
    onClickLeft() {
      this.$emit('clickLeft');
    },
    onClickRight() {
      this.$emit('clickRight');
    }
  }
};
</script>
<style scoped>
.uni-nav-bar-text {
  /* #ifdef APP-PLUS */
  font-size: 34rpx;
  /* #endif */
  /* #ifndef APP-PLUS */
  font-size: 32rpx;
  /* #endif */
  font-weight: bold;
}

.uni-nav-bar-right-text {
  font-size: 32rpx;
}

.uni-navbar__content {
  position: relative;
  background-color: #ffffff;
  overflow: hidden;
  width: 100%;
  box-shadow: 0 1px 6px #cccccc;
}

.uni-navbar__content_view {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  align-items: center;
  flex-direction: row;
}

.uni-navbar__header {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}

.uni-navbar__header-btns {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-wrap: nowrap;
  width: 120rpx;
  padding: 0 20rpx;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.uni-navbar__header-btns-left {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  width: auto;
  justify-content: flex-start;
}

.uni-navbar__header-btns-right {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  width: auto;
  padding-right: 20rpx;
  justify-content: flex-end;
  height: 100%;
}

.uni-navbar__header-container {
  flex: 1;
}

.uni-navbar__header-container-inner {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex: 1;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.uni-navbar__placeholder-view {
  height: 44px;
}

.uni-navbar--fixed {
  position: fixed;
  z-index: 889;
}

.uni-navbar--shadow {
  /* #ifndef APP-NVUE */
  box-shadow: 0 1px 6px #ccc;
  /* #endif */
}

.uni-navbar--border {
  border-bottom-width: 1rpx;
  border-bottom-style: solid;
  border-bottom-color: #eeeeee;
}
</style>
