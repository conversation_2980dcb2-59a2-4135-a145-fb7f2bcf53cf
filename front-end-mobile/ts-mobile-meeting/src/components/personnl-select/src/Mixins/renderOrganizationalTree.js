export default {
  methods: {
    // 点击高亮
    itemAddClassHandle(elementNodeContent) {
      const TreeView = document.getElementById('TreeView');
      const treeItemContent = TreeView.getElementsByClassName(
        'tree-item-content'
      );
      [...treeItemContent].forEach(item => item.classList.remove('active'));
      elementNodeContent.classList.add('active');
    },
    // item 展开方法
    switchClickHandle(e, data, level) {
      e.stopPropagation();

      // 点击高亮
      const elementNodeContent = e.currentTarget.parentNode;
      this.itemAddClassHandle(elementNodeContent);

      // 获取 开关元素
      const switchElement = e.currentTarget.getElementsByClassName(
        'item-switch-open'
      )[0];
      const arrowClassList = [...switchElement.classList];

      // 展开箭头 默认是open
      let arrow = arrowClassList.indexOf('item-switch-close') === -1;
      if (arrow) {
        switchElement.classList.add('item-switch-close');
      } else {
        switchElement.classList.remove('item-switch-close');
      }

      // 获取 点击元素   默认关闭
      const idName = 'TreeItem' + data.id;
      const element = document.getElementById(idName);
      let elementClassList = [...element.classList];
      let result = elementClassList.indexOf('view-close') === -1;
      if (result) {
        element.classList.add('view-close');
      } else {
        element.classList.remove('view-close');
      }

      // 获取 点击对象 子级元素
      const treeItemText = element.getElementsByClassName(
        `content-level${level + 1}`
      );
      // 默认关闭
      for (let i = 0; i < treeItemText.length; i++) {
        if (result) {
          treeItemText[i].classList.add('content-close');
        } else {
          treeItemText[i].classList.remove('content-close');
        }
      }
    },

    // 最父级别 content点击
    parentContentClick(e) {
      const element = e.currentTarget.parentNode;
      const isSwitch = [...element.classList].indexOf('active') === -1;
      if (isSwitch) {
        element.classList.add('active');
      } else {
        element.classList.remove('active');
      }

      const organizationalTree = document.getElementsByClassName(
        `organizational-tree-item`
      )[0];

      if (isSwitch) {
        organizationalTree.classList.add('close');
      } else {
        organizationalTree.classList.remove('close');
      }
    },
    // 渲染 每一级 children 信息
    treeItemHandle(children, level) {
      const { $createElement } = this;

      const isViewClose = level >= this.showLevel ? 'view-close' : '';
      const ClassName = `tree-item-view view-level${level} ${isViewClose}`;

      return children.map(item => {
        return $createElement(
          `uni-view`,
          {
            attrs: {
              type: 'view',
              id: `TreeItem${item.id}`,
              class: ClassName,
              level,
              parent: !!item.children
            }
          },
          [
            // 渲染 子级 每一项的 展开按钮 图标 名字 及 checked
            this.treeItemContentHandle(item, level),
            // 渲染 每一级 children
            item.children && this.treeItemHandle(item.children, level + 1)
          ]
        );
      });
    },
    // 渲染 子级 每一项的 展开按钮 图标 名字 及 checked
    treeItemContentHandle(data, level) {
      const { $createElement } = this;

      // 每一级 渲染信息的level相等 故要-1
      const isClose = level - 1 >= this.showLevel ? 'content-close' : '';
      const className = `tree-item-content content-level${level} ${isClose}`;

      return [
        $createElement(
          `uni-view`,
          {
            class: className,
            style: { paddingLeft: 30 * level + 'rpx' },
            attrs: {
              type: 'content',
              parent: null
            }
          },
          [this.treeItemInfoHandle(data, level), this.renderItemSelect(data)]
        )
      ];
    },
    // 渲染 展开按钮 图标 名字
    treeItemInfoHandle(data, level) {
      const { $createElement } = this;

      // children null 无展开按钮
      // parent children 图标
      let itemIconName = '';
      let showSwitchClass = 'item-switch-open';

      if (data.children) {
        itemIconName = 'personnl-select-parent.svg';
      } else {
        itemIconName = 'personnl-select-children.svg';
        showSwitchClass += ' no-switch';
      }

      const itemIcon = require(`@/assets/img/${itemIconName}`);

      return $createElement(
        'uni-view',
        {
          class: 'tree-item-info',
          on: {
            click: event => this.switchClickHandle(event, data, level)
          }
        },
        [
          <uni-view class={showSwitchClass}></uni-view>,
          <img src={itemIcon} />,
          <uni-text class="tree-item-text">{data.name}</uni-text>
        ]
      );
    },
    renderItemSelect(data) {
      const { $createElement, selectList } = this;
      return $createElement('IconWarpCheck', {
        props: {
          selectList,
          data,
          changeHandle: () => this.clickTreeItem(data, event)
        },
        class: 'icon-warp-check'
      });
    }
  },
  render(h) {
    const parentTree = this.treeList[0];
    const level = 2;
    return h(
      `view`,
      {
        attrs: {
          id: 'TreeView'
        },
        class: 'tree-view'
      },
      [
        h(
          `view`,
          {
            class: 'tree-parent'
          },
          // 渲染最外级 parent: true
          [
            <text class="text-font" onClick={this.parentContentClick}>
              {parentTree.name}
            </text>,
            this.renderItemSelect(parentTree)
          ]
        ),
        // 递归渲染 各children level 默认为2 第二层树数据
        this.treeItemHandle(parentTree.children, level)
      ]
    );
  }
};
