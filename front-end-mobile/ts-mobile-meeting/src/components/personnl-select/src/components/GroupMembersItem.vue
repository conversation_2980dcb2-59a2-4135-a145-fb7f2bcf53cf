<template>
  <view class="group-members-box">
    <view
      class="group-members-item"
      :id="groupInfo.groupId"
      @click.stop="toggle(groupInfo, 'getData')"
    >
      <view
        class="checkbox__icon-wrap"
        :class="groupInfo.checked | iconClass"
        @click.stop="toggle(groupInfo, 'select')"
      >
        <u-icon
          class="checkbox__icon-wrap__icon"
          name="checkbox-mark"
          :size="iconSize"
          :color="groupInfo.checked | iconColor"
        />
      </view>
      <view class="right">
        <view class="group-name">{{ groupInfo.groupName }}</view>
        <view
          :class="{
            'item-switch-open': true,
            'item-switch-close': groupInfo.iconSelect
          }"
        ></view>
      </view>
    </view>
    <ul v-if="groupInfo.iconSelect">
      <personnl-item-info
        v-for="item in groupInfo.memberArr"
        :person="item"
        :key="item.empId"
        :readOnly="readOnly"
        :selectData="selectData"
        type="group"
        @selectItemFormGroup="selectItemFormGroup"
      />
    </ul>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import PersonnlItemInfo from './PersonnlItemInfo';
export default {
  components: {
    PersonnlItemInfo
  },
  props: {
    /**
     * @Description: listType默认为default，已选页面为 selected 点击逻辑不一样
     * <AUTHOR>
     * @date 2022/1/20
     */
    listType: {
      type: String,
      default: () => 'default'
    },
    groupInfo: {
      type: Object,
      default: () => ({})
    },
    iconSize: {
      type: [String, Number],
      default: '28'
    },
    readOnly: {
      type: Boolean,
      default: () => false
    },
    selectData: {
      type: Array,
      default: () => []
    },
    index: {
      type: Number
    }
  },
  filters: {
    sexClassFilter(val) {
      return val === '0' ? 'sex-man' : 'sex-woman';
    },
    firstNameFilter(val) {
      return val.substring(val.length - 2);
    },
    iconColor(val) {
      return val ? '#ffffff' : 'transparent';
    },
    iconClass(val) {
      return val ? 'checkbox__icon-wrap--checked' : '';
    }
  },
  computed: {
    selectDataIds() {
      return this.selectData.map(item => item.empId);
    },
    ...mapState({
      selectAllState: state => state.personnlSelect.selectAllState
    })
  },
  async created() {
    // 群组的第一个分组 默认展开成员
    // if (this.index === 0) {
    //   const result = await this.ajax.getListPageOrgGroupEmp(
    //     this.groupInfo.groupId
    //   );
    //
    //   if (result.rows.length > 0) {
    //     this.groupInfo.memberArr = result.rows;
    //     this.groupInfo.iconSelect = true;
    //   }
    // }
  },
  methods: {
    async toggle(info, type) {
      if (this.readOnly) {
        return false;
      }

      const { groupId, checked, iconSelect } = info;

      if (!iconSelect && !checked) {
        const result = await this.ajax.getListPageOrgGroupEmp(groupId);

        if (result.rows.length > 0) {
          this.groupInfo.memberArr = result.rows;
        }
      }

      if (type === 'select') {
        this.groupInfo.checked = !this.groupInfo.checked;

        this.groupInfo.memberArr.forEach(item => {
          const index = this.selectDataIds.indexOf(item.empId);
          if (index !== -1) {
            this.selectData.splice(index, 1);
          }
          if (!checked) {
            uni.$emit('pushSelectAddressBookItem', item);
          }
        });

        return;
      }

      this.groupInfo.iconSelect = !this.groupInfo.iconSelect;
    },
    selectItemFormGroup() {
      // 分组下 有一个未选中 则分组取消选中
      const closeGroup = this.groupInfo.memberArr.some(item => {
        return this.selectDataIds.indexOf(item.empId) === -1;
      });

      // 分组下都选中 则分组选中
      const openGroup = this.groupInfo.memberArr.every(item => {
        return this.selectDataIds.indexOf(item.empId) !== -1;
      });

      if (closeGroup) {
        this.groupInfo.checked = false;
      } else if (openGroup) {
        this.groupInfo.checked = true;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/flex.scss';
.group-members-box {
  height: auto;
  .group-members-item {
    display: flex;
    align-items: center;
    padding: 0 32rpx;
    height: 88rpx;
    background: #ffffff;
    box-shadow: 0px 2rpx 0px 0px #f4f4f4;
    .checkbox__icon-wrap {
      color: $uni-text-content-color;
      @include vue-flex;
      flex: none;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 38rpx;
      height: 38rpx;
      color: transparent;
      text-align: center;
      transition-property: color, border-color, background-color;
      font-size: $uni-icon-size-base;
      border: 1px solid $uni-text-color-disable;
      border-radius: 4px;
      transition-duration: 0.2s;
    }
    .checkbox__icon-wrap--checked {
      border-color: $u-type-primary;
      background-color: $u-type-primary;
    }
    .right {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
      padding-left: 28rpx;
      .group-name {
        font-size: $uni-font-size-base;
        color: $u-main-color;
      }
      .item-switch-open {
        width: 16rpx;
        height: 16rpx;
        border-top: 4rpx solid #ccc;
        border-left: 4rpx solid #ccc;
        transform: rotate(225deg);
        transition: all 0.7s;
        &.item-switch-close {
          transform: rotate(45deg);
        }
        &.no-switch {
          visibility: hidden;
        }
      }
    }
  }
}
</style>
