<template>
  <view
    class="checkbox__icon-wrap"
    :class="selectStatus | iconClass"
    @click="changeHandle"
  >
    <u-icon
      class="checkbox__icon-wrap__icon"
      name="checkbox-mark"
      :size="32"
      :color="selectStatus | iconColor"
    />
  </view>
</template>

<script>
export default {
  name: 'icon-wrap-check',
  props: {
    selectList: {
      type: Array,
      default: () => []
    },
    data: {
      type: Object,
      default: () => {}
    },
    changeHandle: {
      type: Function,
      default: () => {}
    }
  },
  filters: {
    iconColor(val) {
      return val ? '#ffffff' : 'transparent';
    },
    iconClass(val) {
      return val ? 'checkbox__icon-wrap--checked' : '';
    }
  },
  computed: {
    selectStatus() {
      return this.selectList.indexOf(this.data.id) !== -1;
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/flex.scss';
.checkbox__icon-wrap {
  color: $uni-text-content-color;
  @include vue-flex;
  flex: none;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 38rpx;
  height: 38rpx;
  color: transparent;
  text-align: center;
  transition-property: color, border-color, background-color;
  font-size: $uni-icon-size-base;
  border: 1px solid $uni-text-color-disable;
  border-radius: 4px;
  transition-duration: 0.2s;
  border: 2rpx solid #005bac;
}
.checkbox__icon-wrap--checked {
  border-color: $u-type-primary;
  background-color: $u-type-primary;
}
</style>
