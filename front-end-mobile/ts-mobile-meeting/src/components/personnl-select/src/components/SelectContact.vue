<template>
  <view>
    <mescroll
      ref="`mescroll"
      :mescrollIndex="0"
      @getDatas="getListData"
      @setDatas="setListData"
      @datasInit="datasInit"
    >
      <template #default>
        <personnl-item-info
          v-for="item in contactlist"
          :person="item"
          :key="item.empId"
          :readOnly="selectAllState"
          :selectData="selectData"
        />
      </template>
    </mescroll>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import PersonnlItemInfo from './PersonnlItemInfo';
export default {
  name: 'SelectContact',
  components: {
    mescroll,
    PersonnlItemInfo
  },
  props: {
    selectData: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapState({
      selectAllState: state => state.personnlSelect.selectAllState
    })
  },
  watch: {
    selectAllState: {
      handler(val) {
        if (val) {
          this.contactlist.forEach(item => (item.checked = false));
        }
      },
      immediate: true
    }
  },
  data: () => ({
    contactlist: []
  }),
  methods: {
    async getListData(page, successCallback, errorCallback) {
      let pageData = {
        pageNo: page.num,
        pageSize: page.size
      };
      const result = await this.ajax.getContactDatas(pageData);

      result.rows.forEach(item => (item.checked = false));

      // 刷新之前有 已选列表。 回显列表选中状态, 保持 选择数据与请求数据 地址一致
      // this.filterRowsSelectedHandle(result.rows);

      successCallback(result.rows, result.totalCount);
    },
    setListData(rows) {
      this.contactlist = this.contactlist.concat(rows);
    },
    datasInit() {
      this.contactlist = [];
    },
    filterRowsSelectedHandle(pageData) {
      // const { selectData } = this;
      //
      // if (selectData.length > 0) {
      //   // 获取选择数据的empId 数组
      //   let empIds = selectData.map(item => item.empId);
      //
      //   while (empIds.length) {
      //     let pengding = null;
      //     let index = undefined;
      //
      //     // 从最后一个empId 筛选
      //     const lastEmpId = empIds[empIds.length - 1];
      //
      //     pageData.forEach(item => {
      //       // 找到相同empId 更改选中状态 存储对象地址
      //       if (item.empId === lastEmpId) {
      //         item.checked = true;
      //         index = empIds.indexOf(item.empId);
      //         pengding = item;
      //       }
      //     });
      //
      //     // 找到才替换
      //     if (typeof index !== 'undefined' && pengding) {
      //       selectData.splice(index, 1, pengding);
      //     }
      //     empIds.pop();
      //   }
      // }
    }
  }
};
</script>

<style scoped></style>
