# 人员组件使用
### 提供给移动端表单base-form的一个表单项目(用于选人及选择组织机构)
#### 首先得知道 base-form的基础用法

## 快速开始
```vue
<template>
    <base-form
      ref="baseForm"
      :formList="formList"
      :formData.sync="form"
      :rules="rules"
      :readOnly="false"
    ></base-form>
</template>

<script>
export default {
  data() {
    return {
      formList: [
        {
          // 类型 固定
          type: 'select', 
          mode: 'select-participants',
          // 表单项label 与提示文字
          title: '人员选择',
          placeholder: '人员选择',
          // prop为最终选择提交的name
          prop: 'participantsName',
          // propVal 为最终选择提交的数据对象
          propVal: 'participantsValue',
          // 是否必填，使用prop来进行校验
          required: true,
          // 组件参数  
          params: {
            // 最终返回数据对象 id、name的key
            dataKey: {
              id: 'objId',
              name: 'objName',
              type: 'useAuthorityType'
            }
          }
        },
      ],
      form: {
        participantsName: '',
        participantsValue: [],
      },
      rules: {
        participantsName: [
          {
            required: true,
            message: '请选择人员',
            trigger: ''
          }
        ]
      }
    };
  }
};
</script>
```
#### 通过以上配置就能参会人员组件了
##### 返回参数
```js
// 既选择人员和科室 type为1是人员， 为2是科室
participantsName: '临床科室二,测试人员1',
participantsValue: [
  {
    children: [],
    objId: "368417053658959872"
    objName: "临床科室二"
    orgCode: "368417053658959872"
    orgId: "368417053658959872"
    orgName: "临床科室二"
    useAuthorityType: 2
  },
  {
    avatar: null
    empCode: null
    empId: "68CDD1DAA774468E92B634C826A9BD14"
    empMobile: null
    empName: "测试人员1"
    enableText: null
    gender: "0"
    genderText: "男"
    isEnable: null
    objId: "68CDD1DAA774468E92B634C826A9BD14"
    objName: "测试人员1"
    orgId: "368416827372064768"
    orgName: "XX市人民医院"
    personalIdentity: null
    personalIdentityName: null
    positionId: null
    positionName: null
    useAuthorityType: 1
  }
]
// 提交时候需要什么数据 重新使用新对象包装formData提交（本地回显需要）

// 提交选择了全院人员  数据为
participantsName: "全院人员"
participantsValue: true
```

--------------

### 别的子应用使用 
1. 使用了lodash库 记得下载。
2. @/api/api/personnlSelectApi.js
3. @/components/select-participants
4. @/plugins/widgets.js

###### 注意(确保别的子应用有 mescroll组件)
```js
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
```

在对应子应用的 base-form里添加 人员组件 及 触发事件
