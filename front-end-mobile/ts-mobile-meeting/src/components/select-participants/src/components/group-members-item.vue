<template>
  <view class="group-members-box">
    <view class="group-members-item" @click.stop="groupOpen(groupInfo)">
      <view
        class="checkbox__icon-wrap"
        :class="groupInfo.checked | iconClass"
        @click.stop="groupSelect(groupInfo)"
      >
        <u-icon
          class="checkbox__icon-wrap__icon"
          name="checkbox-mark"
          size="28"
          :color="groupInfo.checked | iconColor"
        />
      </view>
      <view class="right">
        <view class="group-name">{{ groupInfo.groupName }}</view>
        <view
          :class="{
            'item-switch-open': true,
            'item-switch-close': groupInfo.iconSelect
          }"
        ></view>
      </view>
    </view>
    <ul v-if="groupInfo.iconSelect">
      <person-item-info
        v-for="item in groupInfo.memberArr"
        :person="item"
        :key="item.empId"
        :selectData="selectData"
        v-bind="$attrs"
        @selectItemFormGroup="selectItemFormGroup"
      />
    </ul>
    <u-toast ref="uToast" />
  </view>
</template>

<script>
import PersonItemInfo from './person-item-info';
import componentsData from '../mixins/componentsData';
export default {
  mixins: [componentsData],
  components: {
    PersonItemInfo
  },
  props: {
    groupInfo: {
      type: Object,
      default: () => ({})
    },
    selectData: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    selectDataIds() {
      return this.selectData.map(item => item[this.keyId]);
    }
  },
  methods: {
    selectItemFormGroup() {
      // 分组下 有一个未选中 则分组取消选中
      const closeGroup = this.groupInfo.memberArr.some(item => {
        return this.selectDataIds.indexOf(item[this.keyId]) === -1;
      });

      // 分组下都选中 则分组选中
      const openGroup = this.groupInfo.memberArr.every(item => {
        return this.selectDataIds.indexOf(item[this.keyId]) !== -1;
      });

      if (closeGroup) {
        this.groupInfo.checked = false;
      } else if (openGroup) {
        this.groupInfo.checked = true;
      }
    },
    async groupOpen(info) {
      const { groupId, iconSelect } = info;

      // 是否选中 及 是否展开
      if (!iconSelect) {
        const result = await this.ajax.getListPageOrgGroupEmp({
          groupId,
          dataKey: this.$attrs.dataKey
        });

        if (result.rows.length > 0) {
          this.groupInfo.memberArr = result.rows;
          this.groupInfo.iconSelect = true;
        } else {
          this.$refs.uToast.show({ title: '该群组下暂无成员!' });
        }
      } else {
        this.groupInfo.iconSelect = false;
      }
    },
    groupSelect(info) {
      const { checked, iconSelect } = info;
      // 全院人员 开
      if (this.$attrs.selectAllState) {
        return false;
      }

      // 群组未展开 或 群组下无人员
      if (!iconSelect) {
        return false;
      }

      // 全选 or 反选
      this.groupInfo.memberArr.forEach(item => {
        const index = this.selectDataIds.indexOf(item[this.keyId]);

        if (index !== -1) {
          this.selectData.splice(index, 1);
        }
        if (!checked) {
          this.selectData.push(item);
        }
      });

      this.groupInfo.checked = !this.groupInfo.checked;
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/flex.scss';
.group-members-box {
  height: auto;
  .group-members-item {
    display: flex;
    align-items: center;
    padding: 0 32rpx;
    height: 88rpx;
    background: #ffffff;
    box-shadow: 0px 2rpx 0px 0px #f4f4f4;
    .checkbox__icon-wrap {
      color: $uni-text-content-color;
      @include vue-flex;
      flex: none;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 38rpx;
      height: 38rpx;
      color: transparent;
      text-align: center;
      transition-property: color, border-color, background-color;
      font-size: $uni-icon-size-base;
      border: 1px solid $uni-text-color-disable;
      border-radius: 4px;
      transition-duration: 0.2s;
    }
    .checkbox__icon-wrap--checked {
      border-color: $u-type-primary;
      background-color: $u-type-primary;
    }
    .right {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
      padding-left: 28rpx;
      .group-name {
        font-size: $uni-font-size-base;
        color: $u-main-color;
      }
      .item-switch-open {
        width: 16rpx;
        height: 16rpx;
        border-top: 4rpx solid #ccc;
        border-left: 4rpx solid #ccc;
        transform: rotate(225deg);
        transition: all 0.7s;
        &.item-switch-close {
          transform: rotate(45deg);
        }
        &.no-switch {
          visibility: hidden;
        }
      }
    }
  }
}
</style>
