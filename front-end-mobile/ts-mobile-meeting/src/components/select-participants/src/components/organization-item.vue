<template>
  <view class="data-list-item-info" @click="toggle(data)">
    <view class="checkbox__icon-wrap" :class="isChecked | iconClass">
      <u-icon
        class="checkbox__icon-wrap__icon"
        name="checkbox-mark"
        size="28"
        :color="isChecked | iconColor"
      />
    </view>
    <view class="left">
      <view class="organization-img-box">
        <img
          class="data-head-image"
          :src="require('@/assets/img/organization-icon.png')"
        />
      </view>
    </view>
    <view class="right">
      <text class="data-name">{{ data.orgName }}</text>
      <text
        :class="{ 'data-operate': true, disable: isChecked }"
        @click.stop="() => clickLowerLevelFunc(data, isChecked)"
        >下级</text
      >
    </view>
  </view>
</template>

<script>
import componentsData from '../mixins/componentsData';
export default {
  mixins: [componentsData],
  props: {
    // 人员信息
    data: {
      type: Object,
      default: () => ({})
    },
    selectData: {
      type: Array,
      default: () => []
    },
    // 是否只读
    readOnly: {
      type: Boolean,
      default: () => false
    },
    clickItemFunc: {
      type: Function,
      default: () => {}
    },
    clickLowerLevelFunc: {
      type: Function,
      default: () => {}
    }
  },
  computed: {
    selectOrganizationIds() {
      const { id, type } = this.$attrs.dataKey;

      return this.selectData
        .filter(item => item[type] === 2)
        .map(item => item[id]);
    },
    isChecked() {
      return this.selectOrganizationIds.includes(this.data.orgId);
    }
  },
  methods: {
    toggle(e) {
      if (this.$attrs.selectAllState) {
        return false;
      }

      this.clickItemFunc(e);
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/flex.scss';
.data-list-item-info {
  display: flex;
  align-items: center;
  padding: 16rpx 32rpx;
  .checkbox__icon-wrap {
    color: $uni-text-content-color;
    @include vue-flex;
    flex: none;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    width: 38rpx;
    height: 38rpx;
    color: transparent;
    text-align: center;
    transition-property: color, border-color, background-color;
    font-size: $uni-icon-size-base;
    border: 1px solid $uni-text-color-disable;
    border-radius: 4px;
    transition-duration: 0.2s;
  }
  .checkbox__icon-wrap--checked {
    border-color: $u-type-primary;
    background-color: $u-type-primary;
  }
  .left {
    margin: 0 24rpx;
    .organization-img-box {
      width: $uni-img-size-lg;
      height: $uni-img-size-lg;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: $u-bg-color;
      .data-head-image {
        width: 48rpx;
        height: 48rpx;
        text-align: center;
        line-height: $uni-img-size-lg;
        font-size: $uni-font-size-base;
        color: $uni-text-color-inverse;
        &.sex-man {
          background-color: $sexman-color;
        }
        &.sex-woman {
          background-color: $sexwoman-color;
        }
      }
    }
  }
  .right {
    flex: 1;
    display: flex;
    justify-content: space-between;
    .data-name {
      font-size: $uni-font-size-base;
      color: $u-main-color;
    }
    .data-operate {
      font-size: $uni-font-size-base;
      color: $u-type-primary;
      &.disable {
        color: #e4e4e4;
      }
    }
  }
}
</style>
