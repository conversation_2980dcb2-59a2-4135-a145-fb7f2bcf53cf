<template>
  <view class="search-box">
    <view
      class="person-list-item-info"
      v-for="(item, index) in searchData"
      :key="index"
      @click="cancelSelectedHandle(item)"
    >
      <template v-if="item[keyType] === 1">
        <view
          class="checkbox__icon-wrap"
          :class="isChecked(item[keyId]) | iconClass"
        >
          <u-icon
            class="checkbox__icon-wrap__icon"
            name="checkbox-mark"
            size="28"
            :color="isChecked(item[keyId]) | iconColor"
          />
        </view>
      </template>

      <template v-if="item[keyType] === 2">
        <view
          class="checkbox__icon-wrap"
          :class="isChecked(item[keyId]) | iconClass"
        >
          <u-icon
            class="checkbox__icon-wrap__icon"
            name="checkbox-mark"
            size="28"
            :color="isChecked(item[keyId]) | iconColor"
          /> </view
      ></template>

      <view class="left">
        <template v-if="item[keyType] === 1">
          <img
            class="person-head-image"
            v-if="item.avatar"
            :src="item.avatar"
          />
          <view
            v-else
            class="person-head-image"
            :class="item.gender | sexClassFilter"
          >
            {{ item.empName | firstNameFilter }}
          </view>
        </template>
        <template v-if="item[keyType] === 2">
          <view class="organization-img-box">
            <img
              class="data-head-image"
              :src="require('@/assets/img/organization-icon.png')"
            />
          </view>
        </template>
      </view>
      <view class="right">
        <view class="person-name">{{ item[keyName] }}</view>
        <view class="person-description" v-if="item[keyType] === 1">
          {{ item.orgName }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import componentsData from '../mixins/componentsData';
export default {
  mixins: [componentsData],
  props: {
    searchData: {
      type: Array,
      default: () => []
    },
    selectData: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    isChecked() {
      return function(id) {
        return this.selectDataIds.includes(id);
      };
    },
    selectDataIds() {
      return this.selectData.map(item => item[this.keyId]);
    }
  },
  methods: {
    cancelSelectedHandle(e) {
      const id = this.keyId;
      const type = this.keyType;

      const ids = this.selectData
        .filter(item => item[type] === e[type])
        .map(item => item[id]);

      const result = ids.includes(e[id]);

      if (!result) {
        this.selectData.push(e);
      } else {
        let findIndex;

        this.selectData.forEach((item, index) => {
          if (item[id] === e[id] && item[type] === e[type]) {
            findIndex = index;
          }
        });

        this.selectData.splice(findIndex, 1);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/flex.scss';
.search-box {
  padding: 32rpx 0;

  .person-list-item-info {
    display: flex;
    align-items: center;
    padding: 16rpx 32rpx;
    .checkbox__icon-wrap {
      color: $uni-text-content-color;
      display: flex;
      flex-direction: row;
      flex: none;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 38rpx;
      height: 38rpx;
      color: transparent;
      text-align: center;
      transition-property: color, border-color, background-color;
      font-size: $uni-icon-size-base;
      border: 1px solid $uni-text-color-disable;
      border-radius: 4px;
      transition-duration: 0.2s;
    }
    .checkbox__icon-wrap--checked {
      border-color: $u-type-primary;
      background-color: $u-type-primary;
    }
    .left {
      margin: 0 24rpx;
      .organization-img-box {
        width: $uni-img-size-lg;
        height: $uni-img-size-lg;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: $u-bg-color;
        .data-head-image {
          width: 48rpx;
          height: 48rpx;
          text-align: center;
          line-height: $uni-img-size-lg;
          font-size: $uni-font-size-base;
          color: $uni-text-color-inverse;
        }
      }

      .person-head-image {
        width: $uni-img-size-lg;
        height: $uni-img-size-lg;
        border-radius: 50%;
        background-color: $u-bg-color;
        text-align: center;
        line-height: $uni-img-size-lg;
        font-size: $uni-font-size-base;
        color: $uni-text-color-inverse;
        &.sex-man {
          background-color: $sexman-color;
        }
        &.sex-woman {
          background-color: $sexwoman-color;
        }
      }
    }
    .right {
      flex: 1;
      .person-name {
        font-size: $uni-font-size-base;
        color: $u-main-color;
      }
      .person-description {
        color: $u-content-color;
        font-size: $uni-font-size-sm;
      }
    }
  }
}
.navbar-right {
  padding: 0 $uni-spacing-row-lg;
  font-size: $uni-font-size-base;
}
</style>
