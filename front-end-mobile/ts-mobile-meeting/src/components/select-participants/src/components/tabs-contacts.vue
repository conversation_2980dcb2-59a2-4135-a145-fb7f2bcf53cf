<template>
  <view class="contacts-view">
    <view class="title-box">
      <ul class="contacts-crumbs">
        <li v-for="(item, index) in titleArr" :key="index">
          <text class="icon" v-if="index !== 0"> > </text>
          <text
            :class="{ active: index < titleArr.length - 1 }"
            @click="jumpItemHandle(item, index)"
            >{{ item.orgName }}</text
          >
        </li>
      </ul>
    </view>
    <view class="Hospital-staff-switch">
      <text>全院人员</text>
      <u-switch
        size="40"
        :value="$attrs.selectAllState"
        @input="$emit('changeSelectAllState')"
      ></u-switch>
    </view>
    <view class="contacts-content">
      <view v-if="viewTreeChildPersonnl">
        <organization-item
          v-for="item in viewTreeChildPersonnl.orgList"
          :key="item.orgId"
          :data="item"
          :selectData="selectData"
          :clickItemFunc="organizationItemClick"
          :clickLowerLevelFunc="organizationLowerLevelClick"
          v-bind="$attrs"
        />
        <participants-item
          v-for="item in viewTreeChildPersonnl.empList"
          :key="item.empId"
          :person="item"
          :selectData="selectData"
          :clickItemFunc="participantsItemClick"
          v-bind="$attrs"
        />
      </view>
    </view>

    <u-modal
      :value="organizationTips"
      showCancelButton
      title=""
      confirmText="确定"
      content="你所选择的部门包含子部门,确定选择?"
      @cancel="organizationTipsCancelHandle"
      @confirm="organizationTipsConfirmHandle"
    />
  </view>
</template>

<script>
import ParticipantsItem from './participants-item';
import OrganizationItem from './organization-item';
export default {
  name: 'contacts-tabs',
  components: {
    ParticipantsItem,
    OrganizationItem
  },
  props: {
    selectData: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    titleArr: [], // 面包屑
    viewTreeChildPersonnl: {}, // 显示tree层级 的机构人员列表
    organizationTips: false // 选中tree组织 tips
  }),
  created() {
    // 默认获取最高级 组织机构数据
    this.getTreeLevelDataHandle('0', res => {
      this.titleArr.push(res);
    });
  },
  methods: {
    // 机构下级按钮点击事件
    organizationLowerLevelClick(e, val) {
      if (val) {
        return false;
      }
      this.getTreeLevelDataHandle(e.orgId, res => {
        this.titleArr.push(res);
      });
    },
    // 获取tree层级 的机构人员列表
    async getTreeLevelDataHandle(parentId, func) {
      const { object } = await this.ajax.getOrgEmp({
        name: '',
        parentId,
        dataKey: this.$attrs.dataKey
      });

      this.viewTreeChildPersonnl = object;
      func && func(object);
    },
    // 机构面包屑跳转
    jumpItemHandle(item, index) {
      if (index + 1 === this.titleArr.length) {
        return;
      }
      this.titleArr.splice(index + 1);
      this.getTreeLevelDataHandle(item.orgId);
    },
    // 人员item点击事件
    participantsItemClick(e) {
      const { empId, empName } = e;
      this.selectAddDataHandle(empId, empName, 1, e);
    },
    // 机构item点击事件
    organizationItemClick(e) {
      const { orgId, orgName } = e;
      this.selectAddDataHandle(orgId, orgName, 2, e);
    },
    // 新增 or 删除
    selectAddDataHandle(valId, valName, valType, e) {
      const { id, name, type } = this.$attrs.dataKey;

      const ids = this.selectData
        .filter(item => item[type] === valType)
        .map(item => item[id]);
      const result = ids.includes(valId);

      // 过滤对应type的数组 获取是否存在该id数据 添加 or 删除

      if (!result) {
        this.selectData.push(e);
      } else {
        let findIndex;

        this.selectData.forEach((item, index) => {
          if (item[id] === valId && item[type] === valType) {
            findIndex = index;
          }
        });

        this.selectData.splice(findIndex, 1);
      }
    },
    organizationTipsCancelHandle() {
      this.organizationTips = false;
    },
    organizationTipsConfirmHandle() {
      this.organizationTips = false;
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/flex.scss';
.contacts-view {
  height: 100%;
  padding: 0px 16rpx;
  .title-box {
    width: 100%;
    overflow: scroll;
    .contacts-crumbs {
      padding: 8rpx 0;
      display: flex;
      align-items: center;
      box-shadow: 0px 2rpx 0px 0px #f4f4f4;
      color: #666;
      overflow-x: scroll;
      li {
        white-space: nowrap;
      }
      .icon {
        padding: 0 16rpx;
      }
      .active {
        color: #005bac;
      }
    }
  }

  .Hospital-staff-switch {
    background: #fff;
    @include vue-row-flex(flex-start);
    text {
      font-size: 28rpx;
      color: #333333;
      margin-right: 16rpx;
    }
  }
}
</style>
