<template>
  <view>
    <mescroll
      ref="`mescroll"
      :mescrollIndex="0"
      @getDatas="getListData"
      @setDatas="setListData"
      @datasInit="datasInit"
    >
      <template #default>
        <group-members-item
          v-for="item in contactlist"
          :key="item.groupId"
          :groupInfo="item"
          :selectData="selectData"
          v-bind="$attrs"
        />
      </template>
    </mescroll>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import GroupMembersItem from './group-members-item.vue';
export default {
  name: 'SelectContact',
  components: {
    mescroll,
    GroupMembersItem
  },
  props: {
    groupType: {
      type: [Number, String],
      default: () => 0
    },
    selectData: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    contactlist: []
  }),
  methods: {
    async getListData(page, successCallback, errorCallback) {
      // pageNo: 0, pageSize: 15
      let pageData = {
        pageNo: page.num,
        pageSize: page.size,
        groupType: this.groupType
      };

      // 获取 群组分组数据 默认添加 选中变量 与展开变量
      const result = await this.ajax.getEmployeeOrgGroupList(pageData);

      result.rows.forEach(item => {
        item.checked = false;
        item.iconSelect = false;
      });

      successCallback(result.rows, result.totalCount);
    },
    setListData(rows) {
      this.contactlist = this.contactlist.concat(rows);
    },
    datasInit() {
      this.contactlist = [];
    }
  }
};
</script>

<style scoped></style>
