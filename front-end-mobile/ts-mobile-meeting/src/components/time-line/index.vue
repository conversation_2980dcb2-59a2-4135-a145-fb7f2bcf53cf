<template>
  <view class="time_line">
    <view class="time_line_title">
      <view
        class="time_line_title_item"
        v-for="titleItem in timeList"
        :key="titleItem.id"
      >
        {{ titleItem.label }}
      </view>
    </view>
    <view class="time_line_content">
      <view
        :class="[
          'time_line_content_item',
          getState(titleItem),
          getDisable(titleItem)
        ]"
        v-for="titleItem in timeList"
        :key="titleItem.id"
      ></view>
      <!-- 未开放 -->
      <!-- 左侧未开放 -->
      <view class="left_unopen_card" :style="leftUnopenStyle()"></view>
      <!-- 右侧未开放 -->
      <view class="right_unopen_card" :style="rightUnopenStyle()"></view>
      <!-- 禁用 -->
      <view
        class="booking_card"
        v-if="boardRoomDisable"
        :style="bookingStyle(boardRoomDisable, { type: 'disable' })"
      ></view>
      <!-- 占用 -->
      <view
        class="booking_card"
        v-for="(bookingItem, bookingIdx) in boardroomApplyList"
        :style="bookingStyle(bookingItem, { type: 'booking' })"
        :key="`bookingIdx:${bookingIdx}`"
      ></view>
    </view>
  </view>
</template>

<script>
import dayjs from 'dayjs';
let timeList = [
  {
    label: '···',
    id: 'left_other'
  },
  {
    label: '···',
    id: 'right_other'
  }
];
for (let index = 7; index < 22; index++) {
  timeList.splice(timeList.length - 1, 0, { label: index, id: index });
}
export default {
  props: {
    dataSource: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      timeList,
      bookingTimeBegin: '07:00',
      bookingTimeEnd: '21:00',
      boardroomApplyList: [],
      boardRoomDisable: undefined,
      itemWidth: 0,
      contentWidth: 0
    };
  },
  mounted() {
    this.getContentWidth();
    this.getItemWidth();
  },
  methods: {
    getState(row) {
      return '';
    },
    // 未开放
    getDisable(row) {
      const _beginHours = this.bookingTimeBegin.split(':')[0];
      const _minutes = this.bookingTimeBegin.split(':')[1];

      return '';
    },
    getItemWidth() {
      uni
        .createSelectorQuery()
        .in(this)
        .select('.time_line_content_item')
        .boundingClientRect()
        .exec(res => {
          if (res[0]) {
            this.itemWidth = res[0].width * 2;
            this.$forceUpdate();
          } else {
            this.$nextTick(() => {
              this.getItemWidth();
            });
          }
        });
    },
    getContentWidth() {
      uni
        .createSelectorQuery()
        .in(this)
        .select('.time_line_content')
        .boundingClientRect()
        .exec(res => {
          if (res[0]) {
            this.contentWidth = res[0].width * 2;
          } else {
            this.$nextTick(() => {
              this.getItemWidth();
            });
          }
        });
    },
    leftUnopenStyle() {
      const _beginHour = parseInt(this.bookingTimeBegin.split(':')[0]);
      const _beginMinute = parseInt(this.bookingTimeBegin.split(':')[1]);
      // 一小时占得宽度 向上取整，保留三位小数
      const _hourWidth = Math.ceil(this.itemWidth * 1000) / 1000;
      // 一分钟占得宽度 向上取整，保留三位小数
      const _minuteWidth = Math.ceil((this.itemWidth / 60) * 1000) / 1000;
      let _width = 0;
      if (_beginHour < 7) {
        _width = _beginHour * (Math.ceil((this.itemWidth / 7) * 1000) / 1000);
      } else {
        // 其他时间长度+剩余时间长度+间隔宽度
        _width =
          _hourWidth +
          (_beginHour - 6) * 2 +
          (_beginHour - 7) * _hourWidth +
          _beginMinute * _minuteWidth;
      }
      let style = {
        width: `${_width}rpx`
      };
      return style;
    },
    rightUnopenStyle() {
      const _endHour = parseInt(this.bookingTimeEnd.split(':')[0]);
      const _endMinute = parseInt(this.bookingTimeEnd.split(':')[1]);
      // 一小时占得宽度 向上取整，保留三位小数
      const _hourWidth = Math.ceil(this.itemWidth * 1000) / 1000;
      // 一分钟占得宽度 向上取整，保留三位小数
      const _minuteWidth = Math.ceil((this.itemWidth / 60) * 1000) / 1000;
      // 22 - 24 每分钟占得宽度
      const _otherWidth = Math.ceil((this.itemWidth / 120) * 1000) / 1000;
      let _left = 0;
      if (_endHour < 7) {
        _left = _endHour * (Math.ceil((this.itemWidth / 7) * 1000) / 1000);
      } else if (_endHour < 22) {
        _left =
          _hourWidth +
          (_endHour - 5) * 2 +
          (_endHour - 7) * _hourWidth +
          _endMinute * _minuteWidth;
      } else {
        _left =
          _hourWidth * 16 +
          17 * 2 +
          (_endHour - 22) * 60 * _otherWidth +
          _endMinute * _otherWidth;
      }
      let style = {
        left: `${_left}rpx`
      };
      return style;
    },
    bookingStyle(item, config = {}) {
      let style = {};
      let _start, _end;

      if (config.type === 'booking') {
        _start = dayjs(item.startTime).format('HH:mm');
        _end = dayjs(item.meetingEndTime || item.endTime).format('HH:mm');
      } else {
        _start = item.begintime;
        _end = item.endtime;
      }

      const _startHours = parseInt(_start.split(':')[0]);
      const _startM = parseInt(_start.split(':')[1]);
      const _endHours = parseInt(_end.split(':')[0]);
      const _endM = parseInt(_end.split(':')[1]);
      // 一小时占得宽度 向上取整，保留三位小数
      const _hourWidth = Math.ceil(this.itemWidth * 1000) / 1000;
      // 一分钟占得宽度 向上取整，保留三位小数
      const _minuteWidth = Math.ceil((this.itemWidth / 60) * 1000) / 1000;
      // 左边一分钟宽度
      const _leftwith = Math.ceil((this.itemWidth / 60 / 7) * 1000) / 1000;
      // 右边一分钟宽度
      const _rightwith = Math.ceil((this.itemWidth / 60 / 2) * 1000) / 1000;
      if (
        _startHours === 0 &&
        _startM === 0 &&
        _endHours === 23 &&
        _endM === 59
      ) {
        style = { left: 0, right: 0 };
      } else if (_startHours < 7) {
        style.left = (_startHours * 60 + _startM) * _leftwith;
        if (_endHours < 7) {
          // 大于0 小于7
          style.width =
            (_endHours * 60 + _endM - (_startHours * 60 + _startM)) * _leftwith;
        } else if (_endHours < 22) {
          // 大于0 小于22
          style.width =
            (7 * 60 - _startHours * 60 - _startM) * _leftwith +
            (_endHours * 60 + _endM - 7 * 60) * _minuteWidth +
            (_endHours - 7) * 2;
        } else {
          style.width =
            (7 * 60 - _startHours * 60 - _startM) * _leftwith +
            15 * this.itemWidth +
            (_endHours * 60 + _endM - 22 * 60) * _rightwith +
            16 * 2;
        }
      } else if (_startHours < 22) {
        style.left =
          this.itemWidth +
          2 * (_startHours - 6) +
          (_startHours * 60 + _startM - 7 * 60) * _minuteWidth;
        if (_endHours < 22) {
          style.width =
            (_endHours * 60 + _endM - _startHours * 60 - _startM) *
              _minuteWidth +
            (_endHours - _startHours - 1) * 2;
        } else {
          style.width =
            (22 * 60 - _startHours * 60 - _startM) * _minuteWidth +
            (_endHours * 60 + _endM - 22 * 60) * _rightwith +
            (_endHours - _startHours - 1) * 2;
        }
      } else {
        style.left =
          this.contentWidth -
          this.itemWidth +
          (_startHours * 60 + _startM - 22 * 60) * _rightwith;
        style.width =
          (_endHours * 60 + _endM - _startHours * 60 - _startM) * _rightwith;
      }
      style.width = style.width + 'rpx';
      style.left = style.left + 'rpx';
      style.background =
        config.type === 'booking'
          ? item.applyEmployee.employeeId === this.userInfo.empId
            ? 'rgba(82, 96, 255, 0.5)'
            : 'rgba(226, 66, 66, 0.5)'
          : '#cccccc';

      return style;
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.common.userInfo;
    }
  },
  watch: {
    dataSource: {
      handler(newValue) {
        this.bookingTimeBegin =
          newValue.bookingTimeBegin || this.bookingTimeBegin;
        this.bookingTimeEnd = newValue.bookingTimeEnd || this.bookingTimeEnd;
        this.boardroomApplyList = newValue.boardroomApplyList || [];
        this.boardRoomDisable = newValue.boardRoomDisable;
      },
      deep: true,
      immediate: true
    }
  }
};
</script>
<style lang="scss" scoped>
.time_line {
  width: 100%;
  .time_line_title {
    display: flex;
    .time_line_title_item {
      flex: 1;
      height: 32rpx;
      font-size: 11px;
      font-weight: 400;
      color: #666;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 2rpx;
    }
    // .time_line_title_item + .time_line_title_item {
    //   margin-left: 2rpx;
    // }
  }
  .time_line_content {
    display: flex;
    height: 60rpx;
    position: relative;
    // background: red;
    .time_line_content_item {
      flex: 1;
      // width: 40.35294117647059rpx;
      height: 100%;
      background: rgba(82, 96, 255, 0.08);
      // background: #005bac;
      margin-right: 2rpx;
    }
    .time_line_content_item.disable {
      background: #cccccc;
    }
    .time_line_content_item.booking {
      background: #005bac;
    }
    .time_line_content_item.othersBooking {
      background: #ffa8a8;
    }
    .left_unopen_card {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      background: #cccccc;
    }
    .right_unopen_card {
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      background: #cccccc;
    }
    .booking_card {
      overflow: hidden;
      position: absolute;
      top: 0;
      bottom: 0;
    }
    .booking_card:hover {
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.5);
      z-index: 11 !important;
    }
  }
}
</style>
