// #ifdef H5
if (window.__POWERED_BY_QIANKUN__) {
  if (process.env.NODE_ENV === 'development') {
    __webpack_public_path__ = `//localhost:${process.env.VUE_APP_PORT}${process.env.BASE_URL}`;
  } else {
  }
}
// #endif

import Vue from 'vue';
import App from './App';
import '@/plugins/widgets.js';
import common from '@/common/js/setting.js';
import config from '@/common/js/setting.js';
import downloadFile from '@/common/js/downloadFile.js';
import trasenuView from '@trasen-oa/trasen-uview-ui';
import './assets/iconfont/iconfont.css';
Vue.use(trasenuView);

import api from './api/index.js';
Vue.use(api);

import filter from './filters/index.js';
Vue.use(filter);

// import pageHead from './components/page-head/page-head.vue';
// Vue.component('page-head', pageHead);

import baseConfig from './config/config.js';
Object.keys(baseConfig).forEach(item => {
  Vue.prototype[`$${item}`] = baseConfig[item];
});

App.mpType = 'app';
import store from './store';
let instance = null;
Vue.prototype.$common = common;
Vue.prototype.$config = config;
Vue.prototype.$downloadFile = downloadFile;
Vue.config.productionTip = false;
Vue.prototype.$store = store;
Vue.mixin({
  created: function() {
    document.title = `${this.$store.state.common.globalSetting.webTitle ||
      '综合协同办公平台'}`;
  }
});
function render(props = {}) {
  let base = '';
  if (window.__POWERED_BY_QIANKUN__) {
    base = props.data.activeRule || '/';
  } else {
    base = document.querySelector('base').href || '/';
    base = base.replace(/^https?:\/\/[^\/]+/, '');
  }
  window.__uniConfig.router.base = base;
  Vue.prototype.$__uniConfig = {
    mode: 'history',
    base
  };
  const router = Vue.prototype.qiankunRouter();
  instance = new Vue({
    router,
    store,
    ...App
  });
  instance.$mount();
}
// #ifdef H5
if (!window.__POWERED_BY_QIANKUN__) {
  Vue.prototype.qiankunParentNode = document.body;
  render();
}
// #endif

//非h5
// #ifndef H5
instance = new Vue({
  store,
  ...App
});
instance.$mount();
// #endif

export async function bootstrap(props) {}
export async function mount(props) {
  Vue.prototype.qiankunParentNode = document.getElementById(
    process.env.VUE_APP_CONTAINER
  ).parentNode;
  Object.keys(props.fn).forEach(method => {
    Vue.prototype[`$${method}`] = props.fn[method];
  });
  let storeState = props.fn.storeInfo();
  let info = {
    empDeptId: storeState.common.userInfo.orgId,
    empId: storeState.common.userInfo.employeeId,
    phone: storeState.common.userInfo.phoneNumber,
    employeeName: storeState.common.userInfo.employeeName,
    empCode: storeState.common.userInfo.employeeNo
  };
  store.state.common.baseHost = storeState.common.baseHost;
  store.state.common.token = storeState.common.token;
  store.state.common.userInfo = info;
  store.state.common.globalSetting = storeState.common.globalSetting; //主动获取系统配置
  store.state.common.systemCustomCode = storeState.common.systemCustomCode;
  store.state.common.personalSortData = storeState.common.personalSortData;
  render(props);
}
export async function update(props) {}
export async function unmount() {
  instance.$destroy();
  instance.$el.innerHTML = '';
  instance = null;
}
