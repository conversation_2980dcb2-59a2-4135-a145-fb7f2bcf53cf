<template>
  <view class="booking_details">
    <u-navbar title="预约申请" :customBack="onClickBack" />
    <view class="booking_details_content">
      <collapse-card :dataSource="meetingRoomDetails" />
      <view class="base_card">
        <base-item left-text="主题">
          <view slot="right" class="motif_box">
            <text :class="['motif_box_label', isEdit ? 'edit_text' : '']">
              {{ motif }}
            </text>
            <image
              v-if="!motifType"
              class="icon_16"
              src="@/assets/img/theme_visible.png"
            ></image>
          </view>
        </base-item>
        <base-item
          left-text="选择参会人员"
          :right-text="attendEmployeeListStr"
          :rightStyle="{ color: isEdit ? '#999999' : '' }"
        />
        <base-item
          left-text="输入参会人员"
          :right-text="bookingDetails.attendEmployeeInput"
          :rightStyle="{ color: isEdit ? '#999999' : '' }"
        >
          <template slot="right">
            <view v-show="!isEdit" class="right-slot-text">
              {{ bookingDetails.attendEmployeeInput }}
            </view>
            <view v-show="isEdit" class="right-slot-text">
              <u-input
                input-align="right"
                v-model="bookingDetails.attendEmployeeInput"
                type="text"
              />
            </view>
          </template>
        </base-item>
        <base-item
          left-text="使用人数"
          :right-text="bookingDetails.attendEmployeeList.length"
          :rightStyle="{ color: isEdit ? '#999999' : '' }"
        />
        <base-item
          left-text="所需设备"
          :style="{
            height: readOnly ? '40px' : 'auto'
          }"
          :right-text="deviceListStr"
          :rightStyle="{ color: isEdit ? '#999999' : '' }"
        >
          <template v-slot:right>
            <text v-if="readOnly" style="font-size:28rpx">
              {{ deviceListStr }}
            </text>
            <view v-else>
              <u-checkbox-group
                ref="checkboxGroup"
                @change="handleboxGroupChange"
              >
                <u-checkbox
                  v-for="(item, index) of deviceList"
                  v-model="item.checked"
                  :key="index"
                  :name="item.name"
                >
                  {{ item.name }}
                </u-checkbox>
              </u-checkbox-group>
            </view>
          </template>
        </base-item>
        <base-item
          left-text="会议类型"
          :right-text="meetingType"
          :rightStyle="{ color: isEdit ? '#999999' : '' }"
        />
        <base-item
          left-text="预约时段"
          :right-text="dateTimeStr"
          :rightStyle="{ color: isEdit ? '#999999' : '' }"
        />
        <base-item left-text="签退" v-if="type !== 'approval'">
          <template v-slot:right>
            <text v-if="readOnly" style="font-size:28rpx">
              {{ bookingDetails.signOutType ? '签退' : '无需签退' }}
            </text>
            <view v-else>
              <u-switch
                size="34"
                v-model="bookingDetails.signOutType"
              ></u-switch>
            </view>
          </template>
        </base-item>
        <base-item left-text="提醒" v-if="type !== 'approval'">
          <template v-slot:right>
            <text v-if="readOnly" style="font-size:28rpx">
              {{ bookingDetails.sendRemindAdvanceMinuteName }}
            </text>
            <view v-else>
              <u-input
                input-align="right"
                v-model="bookingDetails.sendRemindAdvanceMinuteName"
                type="select"
                @click="handleOpenSelect"
              />
            </view>
          </template>
        </base-item>
      </view>

      <!-- 相关资料 -->
      <view class="base_card" v-if="type !== 'approval'">
        <file-list-card
          :readOnly="readOnly"
          :accessoryId="bookingDetails.accessoryId"
        />
      </view>
      <!-- 主要议程 -->
      <view class="base_card" v-if="type !== 'approval'">
        <main-agenda-card
          :readOnly="readOnly"
          v-model="bookingDetails.agendaList"
        />
      </view>
    </view>
    <view
      v-if="type === 'approval' && bookingDetails.status !== '1'"
      class="booking_details_footer"
    >
      <view
        style="color: #E24242;"
        class="booking_details_footer_btn"
        @click="disagree"
      >
        不同意
      </view>
      <view
        style="color: #005bac;"
        class="booking_details_footer_btn"
        @click="agree"
      >
        同意
      </view>
    </view>
    <view
      v-if="
        type != 'approval' &&
          isApplyEmployee &&
          (bookingDetails.status == '4' || bookingDetails.status == '2')
      "
      class="booking_details_footer"
    >
      <view
        style="color: #005bac;"
        class="booking_details_footer_btn"
        @click="handleReschedule"
      >
        重新预定
      </view>
    </view>
    <view
      class="booking_details_footer"
      v-else-if="
        isApplyEmployee && dataSource.status == 0 && type !== 'approval'
      "
    >
      <view
        style="color: #E24242;"
        v-if="dataSource.status == 0 && readOnly"
        @click="handleUndo"
        class="booking_details_footer_btn"
      >
        撤销
      </view>
      <view
        style="color: #666"
        @click="editCancel"
        v-if="isEdit"
        class="booking_details_footer_btn"
      >
        取消
      </view>
      <view
        style="color: #005bac;"
        @click="editSave"
        class="booking_details_footer_btn"
      >
        {{ isEdit ? '确定' : '编辑' }}
      </view>
    </view>
    <bottom-menu
      ref="bottomMenu"
      :actions="bottomMenuActions"
      @preview="handlePreview"
      @download="handleDownload"
      @delete="handleDelete"
    />
    <undo-modal ref="undoModal" @save="handleSave"></undo-modal>
    <disagree-modal ref="disagreeModal" @save="disagreeSave"></disagree-modal>
    <nk-agree-modal ref="nkAgreeModal" @save="nkAgreeSave"></nk-agree-modal>
    <agree-modal ref="agreeModal" @save="agreeSave"></agree-modal>
    <u-select
      v-model="visibleSelect"
      :list="sendRemindAdvanceMinuteList"
      @confirm="selectConfirm"
      z-index="10075"
    />
  </view>
</template>

<script>
import BaseItem from '@/components/base-item/base-item.vue';
import BottomMenu from '@/components/bottom-menu/bottom-menu.vue';
import UndoModal from './components/undo-modal.vue';
import DisagreeModal from './components/disagree-modal.vue';
import AgreeModal from './components/agree-modal.vue';
import Base64 from '@/common/js/base64.min.js';
import dayjs from 'dayjs';
import MainAgendaCard from '@/pages/common/main-agenda-card/index';
import FileListCard from '@/pages/common/file-list-card/index';
import CollapseCard from '@/pages/common/collapse-card/index';
import NkAgreeModal from './components/nk-agree-modal.vue';

const sendRemindAdvanceMinuteList = [
  { value: 0, label: '不提醒' },
  { value: 10, label: '10分钟' },
  { value: 15, label: '15分钟' },
  { value: 20, label: '20分钟' },
  { value: 30, label: '30分钟' }
];
export default {
  components: {
    BaseItem,
    BottomMenu,
    UndoModal,
    DisagreeModal,
    AgreeModal,
    MainAgendaCard,
    FileListCard,
    CollapseCard,
    NkAgreeModal
  },
  data() {
    return {
      dataSource: {
        accessoryId: this.guid() // 附件ID
      },
      meetingRoomDetails: {},
      bookingDetails: {
        motif: '',
        attendEmployeeList: [],
        attendEmployeeInput: '',
        agendaList: [],
        signOutType: false,
        sendRemindType: false,
        sendRemindAdvanceMinute: '',
        sendRemindAdvanceMinuteName: ''
      },
      attendEmployeeListStr: '',
      deviceListStr: '', // 所需设备逗号拼接
      meetingType: '',
      dateTimeStr: '',
      motifType: false,
      motif: '',
      bottomMenuActions: [
        { label: '预览', emitName: 'preview' },
        { label: '下载', emitName: 'download' },
        { label: '删除', emitName: 'delete' }
      ],
      type: '',
      applyId: '',
      options: [
        {
          text: '删除',
          style: {
            backgroundColor: '#dd524d'
          }
        }
      ],
      isApplyEmployee: false,
      isEdit: false,
      visibleSelect: false,
      sendRemindAdvanceMinuteList,
      deviceList: [],
      userInfo: null
    };
  },
  async onLoad(opt) {
    this.type = opt.type;
    await this.getMyEmployeeDetail();
    let localApplyIdData = {};
    if (opt.applyId) {
      this.applyId = opt.applyId || '';
      let param = {
        workflowType: '2',
        applyId: opt.applyId,
        sidx: 'a2.CREATE_DATE',
        sord: 'desc',
        pageNo: '1',
        pageSize: '9999'
      };
      const res = await this.ajax.getListWorkflowList(param);
      if (res.statusCode === 200 && res.success) {
        if (res.object) {
          if (res.object.rows && res.object.rows.length) {
            localApplyIdData = res.object.rows[0];
          }
        }
      }
    }

    if (this.type === 'approval') {
      this.bottomMenuActions.pop();
    }
    this.dataSource = Object.assign(
      this.dataSource,
      JSON.parse(uni.getStorageSync('bookingDetails') || '{}'),
      localApplyIdData
    );
    this.loadData();
  },
  methods: {
    onClickBack() {
      uni.$emit('bookingDetails', false);
      uni.navigateBack();
    },
    async getMyEmployeeDetail() {
      let res = await this.ajax.getMyEmployeeDetail();
      this.userInfo = res.object;
    },
    guid() {
      function S4() {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
      }
      return (
        S4() +
        S4() +
        '-' +
        S4() +
        '-' +
        S4() +
        '-' +
        S4() +
        '-' +
        S4() +
        S4() +
        S4()
      );
    },
    async loadData() {
      // 会议室类型
      let meetingRoomTypes = await this.ajax.getDictDatas({
        typeCode: 'MEETING_TYPE'
      });
      meetingRoomTypes = meetingRoomTypes.object || [];
      // 会议室详情
      const meetingRoomDetails = await this.ajax.getBoardRoom({
        id: this.dataSource.boardroomId
      });
      // 预约详情
      const bookingDetails = await this.ajax.getBoardRoomApply({
        id: this.dataSource.applyId
      });

      this.meetingRoomDetails = meetingRoomDetails.object;
      this.bookingDetails = Object.assign(
        this.bookingDetails,
        bookingDetails.object
      );
      this.isApplyEmployee =
        this.userInfo.employeeId ===
        this.bookingDetails.applyEmployee.employeeId;
      let attendEmployeeList = this.bookingDetails.attendEmployeeList || [];
      attendEmployeeList = attendEmployeeList.map(e => {
        return e.username;
      });
      this.attendEmployeeListStr = attendEmployeeList.join('、');

      // 所需设备
      this.deviceListStr = this.bookingDetails.device || '';
      let deviceSelectList = this.deviceListStr.split(',').filter(item => item);
      this.deviceList = (this.meetingRoomDetails.deviceList || []).map(item => {
        return {
          ...item,
          checked: deviceSelectList.includes(item.name)
        };
      });

      // 是否公开主题
      this.motifType = this.bookingDetails.motifType == 1;
      // 会议室类型
      const meetingRoomType = meetingRoomTypes.find(e => {
        return e.itemNameValue == this.bookingDetails.appTypeId;
      });

      this.meetingType = meetingRoomType
        ? meetingRoomType.itemName
        : this.bookingDetails.appTypeId;
      // 预约时段
      this.dateTimeStr = `${dayjs(this.bookingDetails.startTime).format(
        'YYYY-MM-DD'
      )} ${dayjs(this.bookingDetails.startTime).format('HH:mm')}-${dayjs(
        this.bookingDetails.endTime
      ).format('HH:mm')}`;

      this.bookingDetails.signOutType = this.bookingDetails.signOutType == 1;

      // 提醒
      this.bookingDetails.sendRemindType =
        this.bookingDetails.sendRemindType == 1;
      this.bookingDetails.sendRemindAdvanceMinuteName =
        this.bookingDetails.sendRemindType &&
        this.bookingDetails.sendRemindAdvanceMinute
          ? `提前${this.bookingDetails.sendRemindAdvanceMinute}分钟`
          : '无需提醒';
      this.isEdit = false;
    },
    handleReschedule() {
      uni.navigateTo({
        url: `/pages/meeting-booking/details?boardRoomId=${
          this.bookingDetails.boardroomId
        }&date=${dayjs().format(
          'YYYY-MM-DD'
        )}&type=reschedule&subscribeStatus=2&applyId=${this.bookingDetails.id}`
      });
    },
    handleActionFile(row) {
      this.$refs.bottomMenu.show(row);
    },
    handleboxGroupChange(e = []) {
      this.$set(this.bookingDetails, 'device', e.join(','));
    },
    // 文件预览
    handlePreview(row) {
      let filePath = `${
        this.$config.ENABLE_FILE_PREVIEW
          ? this.$config.DOCUMENT_BASE_HOST
          : this.$config.BASE_HOST
      }/ts-basics-bottom/fileAttachment/downloadFile/${row.fileId ||
        row.id}?fullfilename=${row.id}.${row.fileExtension}&token=${
        this.token
      }`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      } else {
        this.$downloadFile.downloadFile(filePath);
      }
    },
    // 文件下载
    handleDownload(row) {
      let filePath = `${
        this.$config.BASE_HOST
      }/ts-basics-bottom/fileAttachment/downloadFile/${row.fileId ||
        row.id}?fullfilename=${row.id}.${row.fileExtension}&token=${
        this.token
      }`;
      this.$downloadFile.downloadFile(filePath);
    },

    // 撤销
    handleUndo() {
      this.$refs.undoModal.show();
    },
    // 撤回回调
    async handleSave(record) {
      try {
        const res = await this.ajax.cancelBoardRoomApply({
          remark: record,
          applyId: this.dataSource.applyId
        });
        uni.showToast({
          title: '撤销成功！'
        });
        uni.$emit('bookingDetails', true);
        uni.navigateBack();
      } catch (error) {}
    },

    // 批量下载
    batchUpload() {
      const idList = this.fileList.map(e => {
        return e.id;
      });

      let filePath = `${
        this.$config.BASE_HOST
      }/ts-basics-bottom/fileAttachment/batchDownloadByIds?ids=${idList.join(
        ','
      )}`;
      this.$downloadFile.downloadFile(filePath);
    },
    async editSave() {
      if (this.isEdit) {
        let params = {};
        params.id = this.bookingDetails.id;
        params.signOutType = this.bookingDetails.signOutType ? 1 : 2;
        params.sendRemindType = this.bookingDetails.sendRemindType ? 1 : 2;
        params.sendRemindAdvanceMinute = this.bookingDetails.sendRemindAdvanceMinute;
        params.agendaList = this.bookingDetails.agendaList.map((e, idx) => {
          return { ...e, applyId: this.dataSource.applyId, num: idx };
        });
        params.device = this.bookingDetails.device;
        params.boardroomId = this.meetingRoomDetails.id;
        params.attendEmployeeNoList = this.bookingDetails.attendEmployeeList.map(
          item => item.usercode
        );
        params.motif = this.motif;
        params.startTime = this.bookingDetails.startTime;
        params.endTime = this.bookingDetails.endTime;
        params.attendEmployeeInput = this.bookingDetails.attendEmployeeInput;
        try {
          const res = await this.ajax.editBoardRoomApply(params);
          uni.showToast({
            title: '保存成功'
          });
          uni.$emit('bookingDetails', true);
          uni.navigateBack();
        } catch (error) {}
      }
      this.isEdit = !this.isEdit;
    },
    editCancel() {
      this.loadData();
    },
    disagree() {
      this.$refs.disagreeModal.show();
    },
    async disagreeSave(record) {
      try {
        const res = await this.ajax.failBoardRoomApply({
          remark: record,
          businessId: this.bookingDetails.businessId
        });
        uni.showToast({
          title: '操作成功！'
        });
        uni.$emit('bookingDetails', true);
        uni.navigateBack();
      } catch (error) {}
    },
    agree() {
      // 兼容内控系统
      if (this.bookingDetails.defaultProcessid === 'HYSSQ') {
        this.$refs.nkAgreeModal.show();
      } else {
        this.$refs.agreeModal.show(this.dataSource);
      }
    },
    async nkAgreeSave(remark) {
      try {
        await this.ajax.passBoardRoomApply({
          remark: remark,
          businessId: this.dataSource.businessId
        });
        uni.showToast({
          title: '操作成功！'
        });
        uni.$emit('bookingDetails', true);
        uni.navigateBack();
      } catch (error) {}
    },
    agreeSave(row) {
      uni.showToast({
        icon: 'none',
        title: '操作成功！'
      });
      uni.$emit('bookingDetails', true);
      setTimeout(() => {
        if (this.applyId) {
          uni.navigateTo({
            url: `/pages/booing-approve/index`
          });
        } else {
          uni.navigateBack();
        }
      }, 500);
    },
    handleOpenSelect() {
      this.visibleSelect = true;
    },
    selectConfirm(e) {
      if (e[0].value === 0) {
        this.bookingDetails.sendRemindType = false;
        this.bookingDetails.sendRemindAdvanceMinute = undefined;
      } else {
        this.bookingDetails.sendRemindType = true;
        this.bookingDetails.sendRemindAdvanceMinute = e[0].value;
      }

      this.bookingDetails.sendRemindAdvanceMinuteName =
        this.bookingDetails.sendRemindType &&
        this.bookingDetails.sendRemindAdvanceMinute
          ? `提前${this.bookingDetails.sendRemindAdvanceMinute}分钟`
          : '无需提醒';
    }
  },
  mounted() {
    // #ifdef H5
    // #endif
  },
  computed: {
    token() {
      return this.$store.state.common.token;
    },
    isUploadFile() {
      return this.type !== 'approval' && this.isApplyEmployee;
    },
    readOnly() {
      return !(this.isEdit && this.isApplyEmployee);
    }
  },
  watch: {
    motifType: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.motif = this.bookingDetails.motif;
        } else {
          this.motif = this.bookingDetails.motif
            .split('')
            .map(e => {
              return '*';
            })
            .join('');
        }
      },
      immediate: true,
      deep: true
    },
    isUploadFile: {
      handler(newVal, oldVal) {
        if (newVal) {
        }
      },
      immediate: true,
      deep: true
    }
  }
};
</script>

<style lang="scss" scoped>
.booking_details {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  .booking_details_content {
    flex: 1;
    overflow-y: auto;
    // margin-bottom: 80rpx;
  }
  .booking_details_footer {
    margin-top: 20rpx;
    height: 88rpx;
    background: #ffffff;
    box-shadow: 0px -2px 4px 0px rgba(182, 182, 182, 0.5);
    // position: absolute;
    // bottom: 0;
    // left: 0;
    // right: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    font-size: 32rpx;
    font-weight: 400;
    .booking_details_footer_btn {
      flex: 1;
      text-align: center;
    }
  }
  .base_card {
    margin-top: 16rpx;
    background: white;
    .motif_box {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .icon_16 {
        margin-left: 16rpx;
      }
      .motif_box_label {
        font-size: 24rpx;
        font-weight: 400;
        color: #333333;
        text-align: right;
        flex: 1;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
      }
    }
  }
}
.drag_sorts_item {
  width: 100%;
  padding-right: 32rpx;
  .drag_sorts_item_head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .drag_sorts_item_head_agenda {
      flex: 1;
      font-size: 28rpx;
      font-weight: 400;
      color: #333333;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
    }
    .drag_sorts_item_head_functionary {
      width: 200rpx;
      font-size: 28rpx;
      font-weight: 400;
      color: #999999;
      text-align: right;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
    }
  }
  .drag_sorts_item_extra {
    font-size: 28rpx;
    font-weight: 400;
    color: #999999;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
}
.icon_box {
  display: flex;
  align-items: center;
}
.edit_text {
  color: #999999 !important;
}
.addBox {
  font-size: 36rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
page {
  background-color: #efeff4;
}
@media (prefers-color-scheme: dark) {
  page {
    background-color: #000000;
  }
}
.content {
  .row {
    display: flex;
    flex-direction: row;
    align-items: center;
    .icon {
      width: 30px;
      border-radius: 6px;
      margin-right: 13px;
    }
    .text {
      font-size: 13px;
    }
  }
}
</style>
