<template>
  <view>
    <u-popup v-model="visible" mode="center" width="560rpx" border-radius="8">
      <view class="undo_modal_container">
        <!-- <view class="title">撤销审批</view> -->
        <view class="undo_modal_container_head">
          <u-form
            :model="form"
            ref="uForm"
            label-width="190"
            :error-type="errorType"
          >
            <u-form-item
              label-position="top"
              label="办理节点"
              :required="true"
              prop="wfStepId"
            >
              <u-input
                @click="handleWfStepId"
                type="select"
                v-model="form.wfStepName"
                placeholder="请选择办理节点"
              />
            </u-form-item>
            <view v-if="form.wfStepName !== '结束'">
              <!-- 办理人 -->
              <u-form-item
                label-position="top"
                label="办理人"
                :required="true"
                prop="nameStr"
              >
                <u-input
                  @click="handleNames"
                  type="select"
                  v-model="form.nameStr"
                  placeholder="请选择办理人"
                />
              </u-form-item>
              <!-- 紧急程度 -->
              <u-form-item label-position="top" label="紧急程度">
                <u-input
                  @click="handleUrgencyLevelStr"
                  type="select"
                  v-model="form.urgencyLevelStr"
                  placeholder="请选择紧急程度"
                />
              </u-form-item>
              <!-- 办理期限 -->
              <u-form-item label-position="top" label="办理期限">
                <u-input
                  @click="handleOpenDate"
                  type="select"
                  v-model="form.handleAllottedTime"
                  placeholder="请选择办理期限"
                />
              </u-form-item>
              <!-- 办理提示 -->
              <u-form-item label-position="top" label="办理提示">
                <u-input
                  type="textarea"
                  v-model="form.handleMarkedWords"
                  placeholder="请输入办理提示"
                />
              </u-form-item>
            </view>
          </u-form>
        </view>
        <view class="undo_modal_container_footer">
          <view
            class="undo_modal_container_footer_btn"
            style="color: #666;"
            @click="handleCancel"
          >
            取消
          </view>
          <view class="line"></view>
          <view
            class="undo_modal_container_footer_btn"
            style="color:#005bac"
            @click="handleSave"
          >
            确定
          </view>
        </view>
      </view>
    </u-popup>
    <u-select
      v-model="showNextWfStepList"
      :list="nextWfStepList"
      @confirm="confirm"
    ></u-select>
    <!-- 紧急程度 -->
    <u-select
      v-model="showurgencyLevel"
      :list="urgencyLevelList"
      @confirm="urgencyLevelListConfirm"
    ></u-select>
    <!-- 办理期限 -->
    <u-picker
      v-model="showHandleAllottedTime"
      :params="pickerParams"
      mode="time"
      :default-time="form.handleAllottedTime"
      @confirm="dateConfirm"
    ></u-picker>
    <select-multiple
      ref="selectMultiple"
      @ok="selectMultipleOk"
    ></select-multiple>
  </view>
</template>

<script>
import SelectMultiple from '@/components/select-multiple/select-multiple.vue';
export default {
  components: { SelectMultiple },
  data() {
    return {
      visible: false,
      form: {
        wfStepId: '',
        wfStepName: '',
        userList: [],
        users: [],
        nameStr: '',
        urgencyLevel: '',
        urgencyLevelStr: '',
        handleAllottedTime: '',
        handleMarkedWords: ''
      },
      rules: {
        wfStepId: [
          {
            required: true,
            message: '请选择办理节点',
            trigger: 'change'
          }
        ],
        nameStr: [
          {
            required: true,
            message: '请选择办理人',
            trigger: 'change'
          }
        ]
      },
      errorType: ['toast'],
      approvalData: {},
      showNextWfStepList: false,
      nextWfStepList: [],
      showurgencyLevel: false,
      urgencyLevelList: [
        {
          value: '1',
          label: '一般'
        },
        {
          value: '2',
          label: '加急'
        },
        {
          value: '3',
          label: '急件'
        },
        {
          value: '4',
          label: '特急'
        }
      ],
      showHandleAllottedTime: false,
      pickerParams: {
        year: true,
        month: true,
        day: true,
        hour: false,
        minute: false,
        second: false,
        // 选择时间的时间戳
        timestamp: true
      }
    };
  },

  methods: {
    async show(row) {
      try {
        this.approvalData = row;
        const res = await this.ajax.getNextWfStepListByTaskId({
          taskId: row.taskId
        });
        this.form.taskId = row.taskId;
        this.nextWfStepList = res.object || [];
        this.nextWfStepList = this.nextWfStepList.map(e => {
          return {
            ...e,
            extra: e.userList || [],
            label: e.wfStepName,
            value: e.wfStepId
          };
        });
        if (this.nextWfStepList.length > 0) {
          this.form.wfStepId = this.nextWfStepList[0].wfStepId;
          this.form.wfStepName = this.nextWfStepList[0].wfStepName;
          this.$set(this.form, 'userList', this.nextWfStepList[0].extra);
          if (this.form.userList.length > 0) {
            this.$set(this.form, 'users', [this.form.userList[0].usercode]);
            this.$set(this.form, 'nameStr', this.form.userList[0].username);
          }
        }
        this.visible = true;
        this.$nextTick(() => {
          this.$refs.uForm.setRules(this.rules);
        });
      } catch (error) {
        console.log(error);
      }
    },
    handleCancel() {
      this.form = this.$options.data().form;
      this.$refs.uForm.resetFields();
      this.visible = false;
    },
    handleSave() {
      this.$refs.uForm.validate(async valid => {
        if (valid) {
          try {
            if (this.form.wfStepName === '结束') {
              await this.ajax.passBoardRoomApply({
                businessId: this.approvalData.businessId
              });
            } else {
              let params = JSON.parse(JSON.stringify(this.form));
              params.handleAllottedTime = params.handleAllottedTime
                ? params.handleAllottedTime + ' 00:00:00'
                : '';
              if (params.userList) {
                delete params.userList;
              }
              params.users.forEach(e => {
                if (!params.names) params.names = [];
                params.names.push(
                  this.form.userList.find(i => {
                    return i.usercode == e;
                  }).username
                );
              });
              params.names = params.names.join(',');
              params.users = params.users.join(',');
              await this.ajax.completeTaskByWfStepId(params);
            }

            this.$emit('save', this.form.remark);
          } catch (error) {
            console.log(error);
          }
        }
      });
    },
    handleWfStepId() {
      this.showNextWfStepList = true;
    },
    confirm(e) {
      this.form.wfStepId = e[0].value;
      this.form.wfStepName = e[0].label;
      this.$set(this.form, 'userList', e[0].extra);
    },
    handleNames() {
      const list = this.form.userList.map(e => {
        return {
          label: e.username,
          value: e.usercode
        };
      });
      this.$refs.selectMultiple.open({
        title: '办理人',
        list: list,
        selected: this.form.users
      });
    },
    selectMultipleOk(list) {
      this.form.users = list.map(e => {
        return e.value;
      });
      this.form.nameStr = list
        .map(e => {
          return e.label;
        })
        .join(',');
    },
    handleUrgencyLevelStr() {
      this.showurgencyLevel = true;
    },
    urgencyLevelListConfirm(e) {
      this.form.urgencyLevel = e[0].value;
      this.form.urgencyLevelStr = e[0].label;
    },
    handleOpenDate() {
      this.showHandleAllottedTime = true;
    },
    dateConfirm(row) {
      this.form.handleAllottedTime = `${row.year}-${row.month}-${row.day}`;
    }
  }
};
</script>
<style lang="scss" scoped>
.undo_modal_container {
  padding-top: 20rpx;
  .title {
    padding: 0 24rpx;
    font-size: 32rpx;
    line-height: 34rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 12rpx;
  }
  .undo_modal_container_head {
    padding: 0 24rpx;
    border-bottom: 2rpx solid #eee;
  }
  .undo_modal_container_footer {
    padding: 0 24rpx;
    height: 90rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    .undo_modal_container_footer_btn {
      flex: 1;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      font-size: 32rpx;
      font-weight: 400;
    }
    .line {
      height: 100%;
      width: 2rpx;
      background: #eee;
    }
  }
}
</style>
