<template>
  <view class="booking_approve_card" @click="handleClick">
    <view class="title_box mb_4">
      <text class="label">{{ dataSource.motif }}</text>
      <text class="status" :style="statusStyle(dataSource.status)">
        {{ dataSource.statusLable }}
      </text>
    </view>
    <view class="mb_4">
      <text class="text_style" style="margin-right:64rpx">
        {{ dataSource.boardroomName }}
      </text>
      <text class="text_style">
        {{ applyInfo }}
      </text>
    </view>
    <view class="text_style mb_4">预约时段: {{ dateRange }}</view>
    <!-- 通过 -->
    <view
      class="pass_box mb_4"
      v-if="dataSource.status == 1 || dataSource.status == 2"
    >
      <view class="text_style">申请时间: {{ destineDate }}</view>
      <view class="text_style">办理时间: {{ checkTime }}</view>
    </view>
    <!-- 理由 -->
    <view class="text_style mb_4" v-if="dataSource.status == 2">
      理由:
    </view>
    <!-- 待办 -->
    <view class="text_style mb_4" v-if="dataSource.status == 0 && showNodeText">
      当前节点: {{ nodeInfo }}
    </view>
  </view>
</template>

<script>
import dayjs from 'dayjs';
export default {
  props: {
    dataSource: {
      type: Object,
      default: () => {}
    },
    showNodeText: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    statusStyle(type) {
      let color = 'rgba(51, 51, 51, 0.5)';
      switch (type) {
        case '0':
          color = '#6B95CB';
          break;
        case '1':
          color = '#239530';
          break;
        case '2':
          color = '#E24242';
          break;

        default:
          break;
      }
      return { color: color };
    },
    handleClick() {
      this.$emit('click');
    }
  },
  computed: {
    dateRange() {
      const startTime = this.dataSource.startTime;
      const endTime = this.dataSource.endTime;
      return `${dayjs(startTime).format('YYYY-MM-DD')} ${dayjs(
        startTime
      ).format('HH:mm')}-${dayjs(endTime).format('HH:mm')}`;
    },
    destineDate() {
      return `${dayjs(this.dataSource.destineDate).format('YYYY-MM-DD HH:mm')}`;
    },
    checkTime() {
      return `${dayjs(this.dataSource.checkTime).format('YYYY-MM-DD HH:mm')}`;
    },
    nodeInfo() {
      let taskListResList = this.dataSource.taskListResList || [];
      taskListResList = taskListResList.map(e => {
        return e.assigneeName;
      });
      return this.dataSource.stepName
        ? `${this.dataSource.stepName} ${
            taskListResList.length > 0
              ? '(' + taskListResList.join('、') + ')'
              : ''
          }`
        : '';
    },
    applyInfo() {
      let str = `${this.dataSource.applyOrgName || ''}${
        this.dataSource.applyEmpName ? '-' + this.dataSource.applyEmpName : ''
      }`;
      return str;
    }
  }
};
</script>

<style lang="scss" scoped>
.booking_approve_card {
  margin-top: 16rpx;
  background: #fff;
  padding: 16rpx 32rpx;
  .title_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .label {
      font-size: 28rpx;
      font-weight: bold;
      color: #333333;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      margin-right: 26rpx;
    }
    .status {
      font-size: 28rpx;
      font-weight: bold;
    }
  }
  .text_style {
    font-size: 24rpx;
    color: rgba(51, 51, 51, 0.7);
  }
  .pass_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
