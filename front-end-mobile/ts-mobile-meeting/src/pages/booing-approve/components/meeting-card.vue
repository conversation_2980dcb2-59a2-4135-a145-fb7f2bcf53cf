<template>
  <view class="meeting_card" @click="handleClick">
    <view class="modal" v-if="dataScource.disableType == 2"></view>
    <view class="basic_card_top mb_4">
      <view class="basic_card_top_content">
        <view class="title ">
          <text class="name">{{ dataScource.name }}</text>
        </view>
        <view class="date">
          <image class="icon_16 mr_5" src="@/assets/img/icon_date.png"></image>
          <view class="mr_24 text_style">
            {{ dataScource.bookingTimeBegin }}-{{ dataScource.bookingTimeEnd }}
          </view>
          <view class="capacitance">
            <image
              class="mr_4 icon_16"
              src="@/assets/img/icon_people.png"
            ></image>
            {{ dataScource.capacitance }}
          </view>
        </view>
        <view class="address">
          <image
            class="icon_16 mr_5"
            src="@/assets/img/cion_address.png"
          ></image>
          <view class="address_text text_style">
            {{
              `${dataScource.location || ''} ${
                dataScource.floor ? '-' + dataScource.floor + '层' : ''
              }`
            }}
          </view>
        </view>
      </view>
      <view
        class="basic_card_top_image"
        @click.stop="previewImage(dataScource.emphasis)"
      >
        <image
          mode="aspectFill"
          :src="
            dataScource.emphasis
              ? $config.BASE_HOST + dataScource.emphasis
              : iconEmpty
          "
          class="img"
        ></image>
        <view class="img-desc">
          {{ dataScource.isVideoLable || '' }}
        </view>
      </view>
    </view>
    <view
      class="basic_card_bottom"
      v-if="dataScource.deviceList && dataScource.deviceList.length > 0"
    >
      <view
        class="device_item mr_8 mt_4"
        v-for="(deviceItem, idx) in dataScource.deviceList"
        :key="idx"
      >
        {{ deviceItem.name }}
      </view>
    </view>
  </view>
</template>

<script>
const iconEmpty = require('@/assets/img/icon_empty.png');
export default {
  props: {
    dataScource: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      iconEmpty
    };
  },
  methods: {
    previewImage(emphasis) {
      if (!emphasis) return;
      let urlList = [];
      urlList.push(this.$config.BASE_HOST + emphasis); //push中的参数为 :src="item.img_url" 中的图片地址
      uni.previewImage({
        indicator: 'number',
        loop: true,
        urls: urlList
      });
    },
    handleClick() {
      this.$emit('click', this.dataScource);
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/flex.scss';

.meeting_card {
  position: relative;
  background: #ffffff;
  padding: 24rpx 30rpx;
  .basic_card_top {
    @include vue-flex;
    height: 134rpx;
    .basic_card_top_content {
      flex: 1;
      @include vue-flex;
      flex-direction: column;
      justify-content: space-between;
      padding-right: 24rpx;
      .title {
        @include vue-flex;
        align-items: center;
        & > .name {
          flex: 1;
          font-size: 32rpx;
          font-weight: bold;
          color: #333333;
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          margin-right: 26rpx;
        }
      }
      .date,
      .address {
        display: flex;
        align-items: center;
        .capacitance {
          @include vue-flex;
          width: 100rpx;
          align-items: center;
          font-size: 28rpx;
          white-space: nowrap;
        }
        .text_style {
          font-size: 24rpx;
          line-height: 24rpx;
          font-weight: 400;
          color: #333333;
        }
        .address_text {
          flex: 1;
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
        }
        .text_red {
          color: #d21f1f;
        }
      }
    }

    .basic_card_top_image {
      position: relative;
      width: 220rpx;
      max-width: 220rpx;
      min-width: 220rpx;
      height: 100%;
      .img {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
      }
      .img-desc {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 24rpx;
        color: #ffffff;
        height: 40rpx;
        background: rgba(32, 32, 32, 0.5);
        line-height: 40rpx;
        border-bottom-left-radius: 8rpx;
        border-bottom-right-radius: 8rpx;
      }
    }
  }
  .basic_card_bottom {
    @include vue-flex;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    .device_item {
      height: 40rpx;
      line-height: 36rpx;
      padding: 0 20rpx;
      background: #fafafa;
      border-radius: 20rpx;
      border: 2rpx solid #eee;
      font-size: 22rpx;
      color: #333333;
      text-align: center;
    }
  }
  .modal {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.59);
  }
}
</style>
