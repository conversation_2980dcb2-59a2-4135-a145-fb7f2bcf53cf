<template>
  <u-popup v-model="visible" mode="center" width="560rpx" border-radius="8">
    <view class="undo_modal_container">
      <view class="undo_modal_container_head">
        <u-form
          :model="form"
          ref="disagreeForm"
          label-width="190"
          :error-type="errorType"
        >
          <u-form-item label-position="top" label="备注" prop="remark">
            <u-input
              trim
              type="textarea"
              v-model="form.remark"
              placeholder="请输入备注信息"
              style="background:#F4F4F4;padding-left:16rpx"
            />
          </u-form-item>
        </u-form>
      </view>
      <view class="undo_modal_container_footer">
        <view
          class="undo_modal_container_footer_btn"
          style="color: #666;"
          @click="handleCancel"
        >
          取消
        </view>
        <view class="line"></view>
        <view
          class="undo_modal_container_footer_btn"
          style="color:#005bac"
          @click="handleSave"
        >
          确定
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      form: { remark: '' },
      errorType: ['toast']
    };
  },
  methods: {
    show() {
      this.visible = true;
    },
    handleCancel() {
      this.visible = false;
      this.form.remark = '';
    },
    handleSave() {
      this.$emit('save', this.form.remark);
      this.handleCancel();
    }
  }
};
</script>
<style lang="scss" scoped>
.undo_modal_container {
  padding-top: 20rpx;
  .title {
    padding: 0 24rpx;
    font-size: 32rpx;
    line-height: 34rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 12rpx;
  }
  .undo_modal_container_head {
    padding: 0 24rpx;
    border-bottom: 2rpx solid #eee;
  }
  .undo_modal_container_footer {
    padding: 0 24rpx;
    height: 90rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    .undo_modal_container_footer_btn {
      flex: 1;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      font-size: 32rpx;
      font-weight: 400;
    }
    .line {
      height: 100%;
      width: 2rpx;
      background: #eee;
    }
  }
}
</style>
