<template>
  <view class="booing_approve">
    <u-navbar title="预约审批" :customBack="onClickBack"> </u-navbar>
    <base-tabs-swiper
      ref="tabSwiper"
      fontSize="28"
      class="tab-swiper-box"
      :list="tabList"
      :current="currentTab"
      :is-scroll="false"
      @change="changeTab"
    ></base-tabs-swiper>
    <view class="search-box">
      <u-search
        shape="square"
        :show-action="false"
        placeholder="输入主题搜索"
        v-model="queryParam['motif' + currentTab]"
        @search="search"
        @clear="clear"
      ></u-search>

      <i
        v-if="currentTab === 1"
        @click="batchExpire"
        class="screen_icon oa-icon oa-icon-qingchu"
      />
      <i
        @click.stop="handleScreen"
        class="screen_icon oa-icon oa-icon-shaixuan"
      />
    </view>
    <swiper
      class="swiper-box"
      :current="currentTab"
      :duration="300"
      @change="onTabChange"
    >
      <swiper-item
        class="swiper-item"
        v-for="(item, index) in tabList"
        :key="index"
      >
        <mescroll
          :ref="`mescroll${index}`"
          :mescrollIndex="index"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <booking-approve-card
            v-for="i in item.list"
            :key="i.id"
            :dataSource="i"
            :showNodeText="index !== 1"
            @click="handleOpenDetails(i)"
          />
        </mescroll>
      </swiper-item>
    </swiper>
    <base-screen
      ref="baseScreen"
      :screenList="screenList"
      :screenData="screenData[currentTab]"
      @save="handleSave"
      @reset="handleReset"
    />
    <base-dialog ref="baseDialog" />
  </view>
</template>

<script>
import BookingApproveCard from './components/booking-approve-card.vue';
import BaseTabsSwiper from '../../components/base-tabs-swiper/base-tabs-swiper.vue';
import Mescroll from '@/components/mescroll-swiper/mescroll.vue';
import BaseScreen from '@/components/base-screen/index.vue';
import BaseDialog from '@/components/base-dialog/base-dialog.vue';
export default {
  components: {
    BaseTabsSwiper,
    Mescroll,
    BookingApproveCard,
    BaseScreen,
    BaseDialog
  },
  data() {
    return {
      tabList: [
        {
          name: '由我发起',
          status: 1,
          list: []
        },
        {
          name: '待我办理',
          status: 2,
          list: []
        },
        {
          name: '我已办理',
          status: 3,
          list: []
        }
      ],
      currentTab: 0,
      queryParam: {
        motif0: '',
        motif1: '',
        motif2: ''
      },
      dataSource: [],
      screenList: [
        {
          label: '筛选条件',
          prop: 'applyStatus',
          mode: 'radio',
          option: [
            { name: '待办', value: 0 },
            { name: '通过', value: 1 },
            { name: '不通过', value: 2 },
            { name: '已过期', value: 3 },
            { name: '已撤销', value: 4 }
          ]
        },
        {
          label: '预约时段',
          prop: 'timeName',
          propVal: 'time',
          mode: 'dateRange',
          placeholder: '请选择预约时段',
          format: 'YYYY-MM-DD',
          type: 'select'
        }
      ],
      screenData: [
        { applyStatus: '', time: [], timeName: '' },
        { applyStatus: '', time: [], timeName: '' },
        { applyStatus: '', time: [], timeName: '' }
      ]
    };
  },
  methods: {
    /**
     * 点击返回页面
     */
    onClickBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    },
    changeTab(index) {
      this.currentTab = index;
    },
    onTabChange(e) {
      this.currentTab = e.target.current || e.detail.current;
    },
    search(val) {
      this.$nextTick(() => {
        this.datasInit(val, this.currentTab);
        this.$refs[`mescroll${this.currentTab}`][0].downCallback();
      });
    },
    clear() {
      this.search('');
    },
    // 列表数据
    async getListData(page, successCallback, errorCallback, keyword, index) {
      try {
        let vm = this;
        let param = JSON.parse(JSON.stringify(this.screenData[index]));
        param = Object.assign(
          {
            sidx: 'a2.CREATE_DATE',
            sord: 'desc',
            pageNo: page.num,
            pageSize: page.size
          },
          param
        );
        if (param.time && param.time.length === 2) {
          param.startTime = param.time[0];
          param.endTime = param.time[1];
        }
        param.motif = this.queryParam['motif' + index];
        param.workflowType = this.tabList[index].status;
        const res = await vm.ajax.getListWorkflowList(param);

        successCallback(res.object.rows, res.object.totalCount, index);
      } catch (e) {
        errorCallback();

        //TODO handle the exception
      }
    },
    datasInit(keyword, index) {
      this.tabList[index]['list'] = [];
    },
    setListData(rows, totalCount, index) {
      this.tabList[index]['list'] = this.tabList[index]['list'].concat(rows);
      //   vm.dataSource = vm.dataSource.concat(rows);
    },
    handleOpenDetails(row) {
      uni.setStorageSync('bookingDetails', JSON.stringify(row));
      uni.$on('bookingDetails', data => {
        uni.removeStorageSync('bookingDetails');
        if (data) {
          this.$nextTick(() => {
            this.datasInit('', this.currentTab);
            this.$refs[`mescroll${this.currentTab}`][0].downCallback();
          });
        }
        //清除监听，不清除会消耗资源
        uni.$off('bookingDetails');
      });

      uni.navigateTo({
        url: `/pages/booing-approve/booking-details?type=${
          this.currentTab === 1 ? 'approval' : ''
        }`
      });
    },
    // 打开筛选条件
    handleScreen() {
      this.$refs.baseScreen.open();
    },
    // 筛选确定回调
    handleSave(row) {
      this.screenData[this.currentTab] = Object.assign(
        this.screenData[this.currentTab],
        row
      );
      this.datasInit('', this.currentTab);
      this.$refs[`mescroll${this.currentTab}`][0].downCallback();
    },
    // 筛选条件重置
    handleReset() {
      this.screenData[this.currentTab] = this.$options.data().screenData[
        this.currentTab
      ];
      this.datasInit('', this.currentTab);
      this.$refs[`mescroll${this.currentTab}`][0].downCallback();
    },
    batchExpire() {
      const vm = this;
      this.$refs.baseDialog.show({
        title: '一键清除过期申请',
        content: '确定一键清除过期申请？',
        save: function() {
          vm.ajax.batchExpire().then(rer => {
            vm.datasInit('', vm.currentTab);
            vm.$refs[`mescroll${vm.currentTab}`][0].downCallback();
          });
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.booing_approve {
  height: 100%;
  display: flex;
  flex-direction: column;
  .search-box {
    display: flex;
    position: relative;
    background-color: #ffffff;
    padding: $uni-spacing-col-base $uni-spacing-row-lg;
    margin-bottom: $uni-spacing-col-base;
    align-items: center;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: $uni-spacing-row-lg;
      left: $uni-spacing-row-lg;
      height: 1px;
      background-color: #eeeeee;
    }
    .screen_icon {
      font-size: 36rpx;
      margin-left: 24rpx;
    }
  }
  .swiper-box {
    flex-grow: 1;
  }
}
</style>
