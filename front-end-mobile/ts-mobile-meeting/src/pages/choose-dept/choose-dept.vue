<template>
  <view class="ts-container">
    <u-navbar title="科室选择" title-bold>
      <text class="navbar-right" slot="right" @click="confirm">确定</text>
    </u-navbar>
    <view class="search-container">
      <u-search
        v-model="keywords"
        :show-action="false"
        placeholder="输入科室搜索"
        @search="search"
        @clear="clear"
      ></u-search>
      <view class="choose-dept-num" @click="changePopopShow">
        已选({{ chooseDeptNum }})
      </view>
    </view>
    <view class="choose-dept">
      <mescroll
        v-if="mode == 'scoll'"
        :ref="`mescroll`"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <template #default>
          <dept-list
            :type="chooseType"
            :list="list"
            @change="chooseDept"
          ></dept-list>
        </template>
      </mescroll>
      <dept-list
        v-else
        :type="chooseType"
        :list="list"
        :props="deptProps"
        @change="chooseDept"
      ></dept-list>
    </view>
    <choose-dept-popup
      v-model="showPopup"
      :list="chooseDeptList"
      @change="chooseDeptChange"
    ></choose-dept-popup>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import deptList from './components/dept-list.vue';
import chooseDeptPopup from './components/choose-dept-popup.vue';

export default {
  name: 'choose-dept',
  components: {
    mescroll,
    deptList,
    chooseDeptPopup
  },
  data() {
    return {
      title: '',
      mode: 'scoll',
      keywords: '',
      list: [],
      chooseDeptList: [],
      chooseType: '',
      api: '/organization/getTree',
      params: {},
      showPopup: false,
      deptProps: undefined,
      localData: []
    };
  },
  computed: {
    chooseDeptNum() {
      return this.chooseDeptList.length;
    }
  },
  onLoad(opt) {
    this.chooseDeptList = JSON.parse(uni.getStorageSync('dept_list'));
    this.chooseType = opt.chooseType;
    this.mode = opt.mode || 'scoll';
    let deptPageParams = JSON.parse(uni.getStorageSync('deptPageParams'));
    if (deptPageParams.api) {
      this.api = deptPageParams.api;
      this.params = deptPageParams.params;
    }
    this.title = deptPageParams.title;

    if (this.mode == 'once') {
      this.ajax
        .getDeptList(this.api, {
          employeeName: this.keywords
        })
        .then(res => {
          let datas = res.rows || res.object || [];

          let newlist = [];
          datas.forEach(data => {
            this.flattenedDept(data, newlist);
          });
          this.deptProps = {
            key: 'id'
          };
          this.list = newlist;
          this.localData = newlist;
        });
    }
  },
  methods: {
    search() {
      if (this.mode == 'scoll') {
        this.datasInit();
        this.$refs['mescroll'].downCallback();
      } else if (this.mode == 'once') {
        let newList = this.localData.filter(
          item => item.name.indexOf(this.keywords) >= 0
        );
        this.list = newList;
      }
    },
    clear() {
      if (this.mode == 'scoll') {
        this.datasInit();
        this.$refs['mescroll'].downCallback();
      } else if (this.mode == 'once') {
        this.list = [...this.localData];
      }
    },
    getListData(page, successCallback, errorCallback) {
      this.ajax
        .getDeptList(this.api, {
          ...this.params,
          pageNo: page.num,
          pageSize: page.size,
          employeeName: this.keywords
        })
        .then(res => {
          let datas = res.rows || res.object;
          successCallback(datas, res.totalCount);
        })
        .catch(err => {
          errorCallback();
        });
    },
    setListData(rows, totalCount) {
      const key = (this.deptProps && this.deptProps.key) || 'id';
      rows.forEach(item => {
        item.checked = this.chooseDeptList.some(one => one[key] === item[key]);
      });
      this.list = this.list.concat(rows);
    },
    datasInit() {
      this.list = [];
    },
    chooseDept(val) {
      this.chooseDeptList = val;
    },
    changePopopShow() {
      this.showPopup = true;
    },
    chooseDeptChange(val) {
      const key = (this.deptProps && this.deptProps.key) || 'id';
      this.chooseDeptList = val;
      this.list.forEach(item => {
        item.checked = this.chooseDeptList.some(one => one[key] === item[key]);
      });
    },
    confirm() {
      uni.$emit('trasenDept', this.chooseDeptList);
      uni.navigateBack({
        delta: 1
      });
    },
    flattenedDept(data, list) {
      if (this.chooseDeptList.findIndex(item => item.id == data.id) >= 0) {
        data.checked = true;
      }
      list.push(data);

      if (data.children && data.children.length) {
        data.children.forEach(child => {
          this.flattenedDept(child, list);
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../assets/css/flex.scss';
.ts-container {
  height: 100%;
  position: relative;
  @include vue-flex(column);
}
.navbar-right {
  padding: 0 $uni-spacing-row-lg;
  font-size: $uni-font-size-base;
}
.search-container {
  padding: $uni-spacing-row-sm $uni-spacing-row-lg;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  margin-bottom: $uni-spacing-col-base;
}
.choose-dept-num {
  font-size: $uni-font-size-base;
  margin-left: $uni-spacing-row-lg;
}
.choose-dept {
  flex: 1;
  position: relative;
}
</style>
