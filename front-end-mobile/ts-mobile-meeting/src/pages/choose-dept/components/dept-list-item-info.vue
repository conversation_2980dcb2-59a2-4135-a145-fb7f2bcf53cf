<template>
  <view class="dept-list-item-info">
    <view class="left">
      <image
        class="dept-head-image"
        v-if="dept.empHeadImg"
        :src="dept.empHeadImg"
        mode="aspectFill"
      ></image>
      <view v-else class="dept-head-image" :class="dept.sex | sexClassFilter">
        {{ dept.name | firstNameFilter }}
      </view>
    </view>
    <view class="right">
      <view class="dept-name">{{ dept.name }}</view>
      <view class="dept-description">
        {{ dept.deptName }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'dept-list-item-info',
  props: {
    dept: {
      type: Object,
      default() {
        return {};
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../../assets/css/ellipsis.scss';
.dept-list-item-info {
  display: flex;
}
.right {
  flex: 1;
  padding-left: $uni-spacing-row-base;
}
.dept-head-image {
  width: $uni-img-size-lg;
  height: $uni-img-size-lg;
  border-radius: 50%;
  background-color: $u-bg-color;
  text-align: center;
  line-height: $uni-img-size-lg;
  font-size: $uni-font-size-base;
  color: $uni-text-color-inverse;
}
.sex-man {
  background-color: $sexman-color;
}
.sex-woman {
  background-color: $sexwoman-color;
}
.dept-name {
  font-size: $uni-font-size-base;
  color: $u-main-color;
}
.dept-description {
  color: $u-content-color;
  font-size: $uni-font-size-sm;
  @include ellipsis;
}
</style>
