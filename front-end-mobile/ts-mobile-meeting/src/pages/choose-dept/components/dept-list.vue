<template>
  <view>
    <dept-list-item-radio
      v-if="type == 'radio'"
      :list="list"
      :props="props"
      @change="radioChange"
    ></dept-list-item-radio>
    <dept-list-item-checkbox
      v-else
      :list="list"
      :props="props"
      @change="checkboxChange"
    ></dept-list-item-checkbox>
  </view>
</template>

<script>
import deptListItemRadio from './dept-list-item-radio.vue';
import deptListItemCheckbox from './dept-list-item-checkbox.vue';
export default {
  name: 'dept-list',
  components: {
    deptListItemRadio,
    deptListItemCheckbox
  },
  props: {
    type: {
      type: String,
      default: 'checkbox'
    },
    list: {
      type: Array,
      default() {
        return [];
      }
    },
    props: {
      type: Object,
      default: () => {
        return {
          key: 'userId'
        };
      }
    }
  },
  methods: {
    radioChange(val) {
      this.$emit('change', val);
    },
    checkboxChange(val) {
      this.$emit('change', val);
    }
  }
};
</script>

<style lang="scss" scoped></style>
