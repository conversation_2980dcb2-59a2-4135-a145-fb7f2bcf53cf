<template>
  <view class="ts-container">
    <u-navbar title="人员选择" title-bold></u-navbar>
    <view class="search-container">
      <u-search
        v-model="keywords"
        :show-action="false"
        @search="search"
        @clear="clear"
      ></u-search>
      <view class="choose-person-num">已选({{ choosePersonNum }})</view>
    </view>
    <view class="choose-person">
      <base-tabs-swiper
        v-if="type == 'all'"
        ref="tabs"
        fontSize="28"
        :list="list"
        :current="current"
        :is-scroll="false"
      ></base-tabs-swiper>
      <swiper
        :current="tabIndex"
        class="swiper-box"
        :duration="300"
        @change="ontabchange"
      >
        <swiper-item
          class="swiper-item"
          v-for="(item, index) in list"
          :key="index"
        >
          <mescroll
            v-if="item.mode == 'scoll'"
            :ref="`mescroll${index}`"
            :mescrollIndex="index"
            @getDatas="getListData"
            @setDatas="setListData"
            @datasInit="datasInit"
          >
            <template #default>
              <person-list
                :type="chooseType"
                :list="item.list"
                :value="value"
              ></person-list>
            </template>
          </mescroll>
          <person-list
            v-else-if="item.mode == 'notScoll'"
            :type="chooseType"
            :list="item.list"
            :value="value"
          ></person-list>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script>
const allList = [
  {
    name: '组织架构',
    mode: 'scoll',
    list: []
  },
  {
    name: '虚拟群组',
    mode: 'notScoll',
    list: []
  },
  {
    name: '个人群组',
    mode: 'notScoll',
    list: []
  }
];
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import personList from './components/person-list.vue';
export default {
  name: 'choose-person',
  components: {
    mescroll,
    personList
  },
  data() {
    return {
      tabIndex: 0,
      keywords: '',
      type: '',
      value: '',
      list: [],
      choosePersonList: [],
      chooseType: '',
      api: '',
      apiType: '',
      params: {}
    };
  },
  computed: {
    choosePersonNum() {
      return this.choosePersonList.length;
    }
  },
  onLoad(opt) {
    this.chooseType = opt.chooseType;
    if (opt.getListType == 'scollSearch') {
      let pageParams = JSON.parse(uni.getStorageSync('personPageParam'));
      this.api = pageParams.api;
      this.apiType = pageParams.apiType;
      this.params = pageParams.params;
      (this.type = 'part'),
        (this.list = [
          {
            name: '处理人',
            mode: 'scoll',
            list: []
          }
        ]);
    } else {
      this.list = allList;
    }
  },
  methods: {
    search() {},
    clear() {},
    getListData(page, successCallback, errorCallback, index) {
      let api = '';
      let apiType = '';
      if (this.type == 'all') {
        api = list[index].api;
        apiType = list[index].apiType;
      } else {
        api = this.api;
        apiType = this.apiType;
      }
      this.ajax
        .getPersonListFullPathPost(api, apiType, {
          ...this.params,
          pageNo: page.num,
          pageSize: page.size,
          searchKey: this.keywords
        })
        .then(res => {
          successCallback(res.rows, res.totalCount, index);
        });
    },
    setListData(rows, totalCount, index) {
      this.list[index]['list'] = this.list[index]['list'].concat(rows);
    },
    datasInit() {}
  }
};
</script>

<style scoped>
.search-container {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
}
.choose-person-num {
  font-size: 28rpx;
  margin-left: 30rpx;
}
</style>
