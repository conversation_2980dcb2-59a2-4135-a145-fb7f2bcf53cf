<template>
  <view class="choose_secondary_page">
    <u-navbar class="header" :title="config.title">
      <text
        class="right_slot"
        slot="right"
        @click="handleRight"
        v-if="inputVal || config.mode === 'multiple'"
      >
        {{ config.rightText }}
      </text>
    </u-navbar>
    <view class="content">
      <view
        v-for="(item, idx) in dataScource"
        :key="idx"
        @click="handleCheck(item)"
      >
        <view
          :class="['item', checked(item) ? 'active' : '']"
          v-if="item.label"
        >
          <text class="label">{{ item[config.labelProp] }}</text>
          <view class="icon_box">
            <u-icon
              v-if="checked(item)"
              name="checkmark"
              color="#005bac"
              size="32"
            ></u-icon>
          </view>
        </view>
      </view>
      <view class="input_box">
        <u-input
          type="textarea"
          :auto-height="true"
          v-model="inputVal"
          :placeholder="'请输入' + config.title"
          height="72"
          :trim="true"
        />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      config: {
        title: '',
        selectKeys: [],
        rightText: '保存',
        prop: 'value',
        labelProp: 'label',
        mode: 'default' // 'default' | 'multiple'
      },
      dataScource: [],
      inputVal: ''
    };
  },
  onLoad(opt) {
    const vm = this;
    this.dataScource = JSON.parse(uni.getStorageSync('secondary_page_list'));
    let config = JSON.parse(uni.getStorageSync('secondary_page_config'));
    this.config = Object.assign(this.config, config);
    this.config.selectKeys = this.config.selectKeys || [];
    this.config.selectKeys.forEach(e => {
      const idx = this.dataScource.findIndex(i => {
        return i[vm.config.prop] == e;
      });
      if (idx === -1) {
        let data = {};
        data[vm.config.prop] = e;
        data[vm.config.labelProp] = e;
        vm.dataScource.push(data);
      }
    });
  },
  methods: {
    handleCheck(row) {
      const vm = this;
      switch (this.config.mode) {
        case 'default':
          this.$set(this.config, 'selectKeys', [row[vm.config.prop]]);
          uni.$emit('chooseSecondaryPage', [row]);
          uni.navigateBack({
            delta: 1
          });
          break;
        case 'multiple':
          const idx = this.config.selectKeys.findIndex(e => {
            return e == row[vm.config.prop];
          });
          if (idx > -1) {
            this.config.selectKeys.splice(idx, 1);
          } else {
            this.config.selectKeys.push(row[vm.config.prop]);
          }
          break;
        default:
          break;
      }
    },
    checked(row) {
      const vm = this;
      const data = this.config.selectKeys.find(e => {
        return e == row[vm.config.prop];
      });
      return data ? true : false;
    },
    handleRight() {
      const vm = this;
      if (vm.inputVal) {
        if (
          vm.dataScource.some(e => {
            return e[vm.config.labelProp] === vm.inputVal;
          })
        ) {
          uni.showToast({
            icon: 'none',
            title: vm.config.title + '重复！'
          });
          return;
        }
        let data = {};
        data[vm.config.prop] = vm.inputVal;
        data[vm.config.labelProp] = vm.inputVal;
        vm.inputVal = '';
        vm.dataScource.push(data);
      } else {
        // 多选保存
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.choose_secondary_page {
  height: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  .header {
    .right_slot {
      padding: 0 36rpx;
    }
  }
  .content {
    flex: 1;
    overflow-y: auto;
    .item {
      padding: 0 32rpx;
      height: 72rpx;
      font-size: 28rpx;
      font-weight: 400;
      color: #333333;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .label {
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
      }
      .icon_box {
        width: 64rpx;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
    }
    .active {
      background: rgba(82, 96, 255, 0.08);
    }
    .input_box {
      padding: 0 36rpx;
    }
  }
}
</style>
