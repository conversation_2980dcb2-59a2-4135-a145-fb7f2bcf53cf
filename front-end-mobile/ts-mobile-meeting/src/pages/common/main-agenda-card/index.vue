<template>
  <view class="main_agenda_card">
    <base-item left-text="主要议程">
      <template v-slot:right v-if="!readOnly">
        <i
          @click="addMainAgenda('ADD')"
          class="addBox oa-icon oa-icon-plus-circle"
        ></i>
      </template>
    </base-item>
    <HM-dragSorts
      v-if="!readOnly"
      :list="myValue"
      :isLongTouch="false"
      :rowHeight="60"
      :listHeight="myValue.length ? myValue.length * 60 : 1"
      @confirm="dragSortsConfirm"
      @onclick="dragSortsClick"
    >
      <template v-slot:rowContent="{ row }">
        <view class="drag_sorts_item">
          <view class="drag_sorts_item_head">
            <view class="drag_sorts_item_head_agenda">
              {{ row.agenda }}
            </view>
            <view class="drag_sorts_item_head_functionary">
              {{ row.functionary }}
            </view>
          </view>
          <view class="drag_sorts_item_extra">
            {{ row.content || '未填写内容' }}
          </view>
        </view>
      </template>
    </HM-dragSorts>
    <view v-else>
      <base-item
        v-for="(row, rowId) in myValue"
        :key="row.id"
        :left-text="row.agenda"
        :right-text="row.functionary"
        :extraText="row.content || '未填写内容'"
        :style="{ borderTop: rowId === 0 ? '2rpx solid #e4e4e4' : '' }"
      />
    </view>
  </view>
</template>

<script>
import BaseItem from '@/components/base-item/base-item.vue';
export default {
  components: { BaseItem },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    readOnly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      myValue: this.value
    };
  },
  methods: {
    addMainAgenda(type, e) {
      let _data = { value: {}, index: 0 };
      _data = Object.assign(_data, e);
      uni.setStorageSync(
        'mainAgendaData',
        JSON.stringify({
          type: type,
          data: _data.value || {}
        })
      );
      uni.$on('mainAgenda', data => {
        if (data.type === 'delete') {
          this.myValue.splice(_data.index, 1);
        } else if (data.type === 'edit') {
          this.myValue.splice(_data.index, 1, data.data);
        } else if (data.type === 'add') {
          this.myValue.push(data.data);
        }
        uni.removeStorageSync('mainAgendaData');
        //清除监听，不清除会消耗资源
        uni.$off('mainAgenda');
      });
      uni.navigateTo({
        url: `/pages/common/main-agenda-card/edit`
      });
    },
    // 拖拽事件
    dragSortsClick(e) {
      this.addMainAgenda('EDIT', e);
    },
    dragSortsConfirm(e) {
      this.myValue = e.list;
    }
  },
  watch: {
    value: {
      handler(newValue) {
        this.myValue = newValue;
      },
      deep: true,
      immediate: true
    },
    myValue(newValue) {
      if (!newValue) this.myValue = '';
      this.$emit('input', newValue);
    }
  }
};
</script>

<style lang="scss" scoped>
.main_agenda_card {
  .drag_sorts_item {
    width: 100%;
    padding-right: 32rpx;
    .drag_sorts_item_head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .drag_sorts_item_head_agenda {
        flex: 1;
        font-size: 28rpx;
        font-weight: 400;
        color: #333333;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
      }
      .drag_sorts_item_head_functionary {
        width: 200rpx;
        font-size: 28rpx;
        font-weight: 400;
        color: #999999;
        text-align: right;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
      }
    }
    .drag_sorts_item_extra {
      font-size: 28rpx;
      font-weight: 400;
      color: #999999;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
    }
  }
  .addBox {
    font-size: 36rpx;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
</style>
