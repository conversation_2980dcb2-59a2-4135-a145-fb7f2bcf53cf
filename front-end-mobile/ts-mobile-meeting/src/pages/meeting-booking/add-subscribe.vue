<template>
  <view class="add_subscribe">
    <u-navbar class="header" title="我的订阅" :customBack="onClickBack">
      <text
        @click="handleToggle"
        :style="{ color: roomIdList.length > 0 ? '#005bac' : '#333333' }"
        class="right_slot"
        slot="right"
      >
        {{ roomIdList.length > 0 ? '保存' : '取消' }}
      </text>
    </u-navbar>
    <view class="search-box">
      <u-search
        shape="square"
        :show-action="false"
        placeholder="输入会议室名称"
        v-model="roomName"
        @search="search"
        @clear="clear"
      ></u-search>
    </view>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <u-checkbox-group size="40" icon-size="32" active-color="#005bac">
          <u-checkbox
            v-model="item.checked"
            v-for="(item, idx) in notSubscribe"
            :key="idx"
            :wrap="true"
            class="checkbox_box"
          >
            <subscribe-card :dataSource="item"></subscribe-card>
          </u-checkbox>
        </u-checkbox-group>
      </mescroll>
    </view>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import SubscribeCard from './components/subscribe-card.vue';
export default {
  components: { mescroll, SubscribeCard },
  data() {
    return {
      roomName: '',
      notSubscribe: [],
      isVideo: undefined
    };
  },
  onLoad(opt) {
    this.isVideo = opt.isVideo;
  },
  methods: {
    onClickBack() {
      uni.$emit('addSubscribe');
      uni.navigateBack();
    },
    // 列表数据
    async getListData(page, successCallback, errorCallback) {
      try {
        let res = await this.ajax.getListBoardRoomSubscribe({
          roomName: this.roomName,
          isVideo: this.isVideo
        });
        res = res.object || [];

        this.notSubscribe = res.filter(e => {
          return e.subscribeStatus === 1 && e.disableType != 2;
        });
        successCallback(this.notSubscribe);
      } catch (e) {
        errorCallback();
        //TODO handle the exception
      }
    },
    datasInit() {
      let vm = this;
      vm.notSubscribe = [];
    },
    setListData(rows) {
      let vm = this;

      vm.notSubscribe = rows;
      this.$forceUpdate();
    },
    search(val) {
      this.$nextTick(() => {
        this.datasInit(val);
        this.$refs.mescroll.downCallback();
      });
    },
    clear() {
      this.search('');
    },
    async handleToggle() {
      if (this.roomIdList.length > 0) {
        try {
          await this.ajax.addBoardRoomSubscribe({
            roomIdList: this.roomIdList
          });
          uni.$emit('addSubscribe');
        } catch (error) {}
      }
      uni.navigateBack();
    }
  },
  computed: {
    roomIdList() {
      return this.notSubscribe
        .filter(e => {
          return e.checked;
        })
        .map(e => {
          return e.roomId;
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.add_subscribe {
  height: 100%;
  display: flex;
  flex-direction: column;
  /deep/.u-checkbox__label {
    flex: 1 !important;
    margin: 0;
  }
  .right_slot {
    padding: 0 36rpx;
    font-size: 32rpx;
    font-weight: 400;
  }
  .mescroll-content {
    flex: 1;
    position: relative;
  }
  .search-box {
    display: flex;
    position: relative;
    background-color: #ffffff;
    padding: $uni-spacing-col-base $uni-spacing-row-lg;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: $uni-spacing-row-lg;
      left: $uni-spacing-row-lg;
      height: 1px;
      background-color: #eeeeee;
    }
  }
  .checkbox_box {
    width: 100% !important;
    display: flex;
    align-items: center;
    background: white;
    margin-top: 16rpx;
    padding-left: 32rpx;
  }
}
</style>
