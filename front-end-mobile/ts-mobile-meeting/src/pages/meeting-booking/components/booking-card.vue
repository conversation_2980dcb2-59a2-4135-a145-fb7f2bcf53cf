<template>
  <view class="booking_card">
    <view class="head mb_8">
      <view class="name">{{ dataSource.name }}</view>
      <view class="capacitance">
        <image class="mr_4 icon_16" src="@/assets/img/icon_people.png"></image>
        {{ dataSource.capacitance }}
      </view>
    </view>
    <view class="device_box mb_8">
      <device-list :options="dataSource.deviceList" />
    </view>
    <view class="address_box flex mb_8">
      <view class="address_left flex">
        <image class="icon_16 mr_8" src="@/assets/img/cion_address.png"></image>
        <view class="address_text text_hidden">
          {{
            `${dataSource.location || ''} ${
              dataSource.floor ? '-' + dataSource.floor + '层' : ''
            }`
          }}
        </view>
      </view>
      <view class="address_right" @click="handleAdd">
        预约
      </view>
    </view>
    <view class="line mb_4"></view>
    <time-line :dataSource="dataSource" />
  </view>
</template>

<script>
import DeviceList from '@/components/device_list/index';
import TimeLine from '@/components/time-line/index';
export default {
  components: { DeviceList, TimeLine },
  props: {
    dataSource: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    handleAdd() {
      this.$emit('add');
    }
  }
};
</script>
<style lang="scss" scoped>
.booking_card {
  background: white;
  padding: 16rpx 32rpx;
  margin-top: 16rpx;
  .flex {
    display: flex;
    align-items: center;
  }
  .text_hidden {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    margin-right: 26rpx;
  }
  .head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    & > .name {
      flex: 1;
      font-size: 28rpx;
      font-weight: bold;
      color: #333333;
      margin-right: 26rpx;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
    }
    .capacitance {
      display: flex;
      align-items: center;
      font-size: 28rpx;
      white-space: nowrap;
    }
  }
  .address_box {
    .address_left {
      flex: 1;
      .address_text {
        font-size: 24rpx;
        font-weight: 400;
        color: #333333;
      }
    }
    .address_right {
      width: 128rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 26rpx;
      border: 2rpx solid #005bac;
      font-size: 24rpx;
      font-weight: 400;
      color: #005bac;
    }
  }
  .line {
    width: 100%;
    height: 1px;
    background: #f4f4f4;
  }
}
</style>
