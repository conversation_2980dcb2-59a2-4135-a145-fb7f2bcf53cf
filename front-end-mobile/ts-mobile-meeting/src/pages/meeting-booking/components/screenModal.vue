<template>
  <view class="screen">
    <u-popup
      v-model="isShow"
      mode="right"
      z-index="989"
      width="600"
      @close="handleSearch"
    >
      <view class="screen-box">
        <view
          class="screen-item"
          v-for="(item, index) in formList"
          :key="index"
        >
          <view class="screen-item-title">
            {{ item.title }}
          </view>
          <view
            class="screen-item-option"
            v-if="item.type == 'select' && item.mode == 'checkbox'"
          >
            <u-checkbox-group
              class="screen-option-group"
              v-model="formData[item.prop]"
              @change="checkboxGroupChange($event, item)"
            >
              <u-checkbox
                class="screen-option-item"
                v-for="(radioItem, radioIndex) in item.optionList"
                :key="radioIndex"
                :name="radioItem.value"
                v-model="radioItem.checked"
                :show-icon="false"
                :class="radioItem.checked ? 'active-screen-item' : ''"
              >
                {{ radioItem.name }}

                <view v-if="radioItem.checked" class="checked-icon">
                  <u-icon name="checkmark" size="24"></u-icon>
                </view>
              </u-checkbox>
            </u-checkbox-group>
          </view>
          <view
            class="screen-item-option"
            v-if="item.type == 'select' && item.mode == 'radio'"
          >
            <u-radio-group
              class="screen-option-group"
              v-model="formData[item.prop]"
              @change="radioGroupChange($event, item)"
            >
              <u-radio
                class="screen-option-item"
                v-for="(radioItem, radioIndex) in item.optionList"
                :key="radioIndex"
                :name="radioItem.value"
                :show-icon="false"
              >
                {{ radioItem.name }}

                <view
                  v-if="formData[item.prop] == radioItem.value"
                  class="checked-icon"
                >
                  <u-icon name="checkmark" size="24"></u-icon>
                </view>
              </u-radio>
            </u-radio-group>
          </view>
          <view
            class="screen-item-option"
            v-else-if="item.type == 'select' && item.mode == 'dept'"
          >
            <view
              v-if="item.chooseType == 'checkbox'"
              class="screen-option-group dept-content"
            >
              <view
                class="screen-option-item dept-selected-item"
                v-for="deptItem in formData[item.propVal]"
                :key="deptItem.id"
                @click="changeDept(deptItem.id, item)"
              >
                <view class="dept-item-text">{{ deptItem.name }}</view>
                <view class="checked-icon">
                  <u-icon name="checkmark" size="24"></u-icon>
                </view>
              </view>
              <text class="screen-option-item" @tap="chooseDept(item)">+</text>
            </view>

            <view v-else class="screen-option-group dept-content">
              <view
                class="screen-option-item dept-selected-item-radio"
                v-for="deptItem in formData[item.propVal]"
                :key="deptItem.id"
                @tap="chooseDept(item)"
              >
                <view class="dept-item-text">{{ deptItem.name }}</view>
                <view class="checked-icon">
                  <u-icon name="checkmark" size="24"></u-icon>
                </view>
              </view>

              <text
                v-if="!formData[item.propVal].length"
                class="screen-option-item"
                @tap="chooseDept(item)"
                >+</text
              >
            </view>
          </view>
          <view
            class="screen-item-option"
            v-else-if="item.type == 'select' && item.mode == 'date'"
          >
            <u-input
              :placeholder="item.placeholder"
              style="background: #F8F8F8;margin-right:4%;border-radius: 8rpx;"
              type="select"
              v-model="formData[item.prop]"
              @click="chooseRangePicker(item)"
            ></u-input>
          </view>
          <view class="screen-item-option" v-else-if="item.type == 'input'">
            <u-input
              :placeholder="item.placeholder"
              style="background: #F8F8F8;margin-right:4%;border-radius: 8rpx;width:30%"
              v-model="formData[item.prop]"
            ></u-input>
          </view>
        </view>
        <view class="btn-box">
          <u-button @click="handleReset">重置</u-button>
          <u-button @click="handleSearch">确定</u-button>
        </view>
      </view>
    </u-popup>
    <!-- 时间区间 -->
    <base-time-range-picker
      v-model="rangePickerShow"
      :defaultValue="rangePickerValue"
      :format="rangePickerFormat"
      :rangeDate="rangePickerRangeDate"
      @confirm="timeRangePickerConfirm"
    ></base-time-range-picker>
  </view>
</template>

<script>
import BaseTimeRangePicker from '@/components/base-time-range-picker/index';
export default {
  components: { BaseTimeRangePicker },
  name: 'screen',

  props: {
    show: {
      type: Boolean,
      default: false
    },
    formList: {
      type: Array,
      default() {
        return [];
      }
    },
    formData: {
      type: Object,
      default() {
        return {};
      }
    }
  },

  data() {
    return {
      isShow: false,
      rangePickerShow: false,
      clickMode: null,
      clickParams: null,
      clickField: null,
      clickProp: null,
      clickPropVal: null,
      rangePickerValue: [],
      rangePickerFormat: 'YYYY-MM-DD HH:mm:ss',
      rangePickerRangeDate: []
    };
  },
  methods: {
    handleopen() {
      this.isShow = true;
    },
    radioGroupChange(e, item) {
      this.$set(this.formData, item.prop, e);
    },
    checkboxGroupChange(e, item) {
      this.$set(this.formData, item.prop, e.join(','));
    },
    chooseDept(item) {
      //获取已选科室
      let deptList = this.formData[item.propVal];
      uni.setStorageSync('dept_list', JSON.stringify(deptList));
      let deptPageParams = {
        title: item.name
      };
      //判断是否为条件查询
      if (item.getListType == 'scollSearch' || item.getListType == 'search') {
        //查询api
        let params = {
          api: item.searchApi
        };
        //获取查询条件参数
        for (var i = 0; i < item.searchParams.length; i++) {
          if (!this.formData[item.searchParams[i].value]) {
            this.$u.toast(item.searchParams[i].message);
            return false;
          }
          params[item.searchParams[i].name] = this.formData[
            item.searchParams[i].value
          ];
        }
        deptPageParams = { ...deptPageParams, ...params };
      }
      uni.setStorageSync('deptPageParams', JSON.stringify(deptPageParams));
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('trasenDept', data => {
        this.$set(this.formData, item.propVal, data);
        let deptName = [],
          deptId = [];
        data.map(i => {
          deptName.push(i.name);
          deptId.push(i.userId);
        });
        uni.removeStorageSync('deptPageParams');
        uni.removeStorageSync('dept_list');
        //清除监听，不清除会消耗资源
        uni.$off('trasenDept');
      });
      uni.navigateTo({
        url: `/pages/choose-dept/choose-dept?chooseType=${item.chooseType}&getListType=${item.getListType}&mode=once`
      });
    },
    changeDept(id, item) {
      let newData = this.formData[item.propVal].filter(item => item.id != id);
      this.$set(this.formData, item.propVal, newData);
    },
    handleReset() {
      this.$emit('reset');
    },
    handleSearch() {
      this.isShow = false;
      this.$emit('change');
    },
    //打开选择器
    chooseRangePicker(e) {
      this.rangePickerValue = this.formData[e.propVal];
      this.rangePickerFormat = e.format || this.rangePickerFormat;
      this.rangePickerRangeDate = e.rangeDate || this.rangePickerRangeDate;
      this.$nextTick(() => {
        this.rangePickerShow = true;
        this.clickProp = e.prop;
        this.clickPropVal = e.propVal;
      });
    },
    // 时间区间选择器回调
    timeRangePickerConfirm(e) {
      if (e) {
        this.$set(this.formData, this.clickProp, e.join('-'));
        this.$set(this.formData, this.clickPropVal, e);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.screen-box {
  padding: 0 20rpx;
  padding-bottom: 44px;
}
.screen-item-title {
  height: 56rpx;
  line-height: 56rpx;
  font-size: 28rpx;
  margin: 10rpx 0;
  color: #666666;
}
.screen-option-group {
  width: 100%;
}
.screen-option-item {
  border-radius: 8rpx;
  line-height: 70rpx;
  height: 70rpx;
  width: 30% !important;
  margin: 0 3% 20rpx 0;
  text-align: center;
  background-color: #ffffff;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  border: 1px solid #dddddd;
  box-sizing: border-box;
  color: #333333;
  position: relative;
}
.btn-box {
  background: white;
  display: flex;
  position: fixed;
  bottom: 0;
  width: calc(100% - 40rpx);
  padding: 8px 0;
  .u-btn {
    width: 132px;
    height: 44px;
    line-height: 25px;
    font-size: 18px;
    color: #333333;
    &:last-child {
      background: $u-type-primary;
      border-radius: 4px;
      border-color: $u-type-primary;
      color: #fff;
    }
    &::after {
      border-color: #e5e5e5;
    }
  }
}

.dept-content {
  display: flex;
  flex-wrap: wrap;
}

/deep/ {
  .screen-item-option .screen-option-item {
    background-color: #f5f5f5;
    border-radius: 4px;
    border: none;
    width: 85px;
    height: 36px;
    line-height: 36px;
    .u-radio__label,
    .u-checkbox__label {
      margin: 0 5px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 14px;
      text-align: center;
    }
  }
  .screen-item-option .screen-option-item.u-radio--checked,
  .screen-item-option .screen-option-item.active-screen-item {
    background-color: $u-type-primary-light;
    .u-checkbox__label,
    .u-radio__label {
      color: $u-type-primary;
    }
  }
  .checked-icon {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 19px;
    border-bottom: 19px solid #005bac;
    border-left: 19px solid transparent;

    .u-icon {
      color: #fff;
      transform: scale(0.8);
      position: absolute;
      left: -12px;
      bottom: -19px;
      .u-icon__icon {
        font-size: 12px !important;
      }
    }
  }
  .screen-item-title {
    font-size: 16px;
    line-height: 22px;
    height: 22px;
  }
  .screen-item:first-child .screen-item-title {
    margin-top: 58px;
  }
  .screen-option-item.dept-selected-item-radio,
  .screen-option-item.dept-selected-item {
    position: relative;
    color: $u-type-primary;
    background-color: $u-type-primary-light;
    padding: 0 8px;

    .dept-item-text {
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .screen-option-item.dept-selected-item-radio {
    min-width: 30%;
    max-width: 100%;
    width: auto !important;
  }
  @media screen and (max-width: 750px) {
    .screen-item-option .screen-option-item {
      border-radius: 8rpx;
      border: none;
      width: 170rpx;
      height: 72rpx;
      line-height: 72rpx;
      .u-radio__label,
      .u-checkbox__label {
        margin: 0 10rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 28rpx;
        text-align: center;
      }
    }

    .checked-icon {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 38rpx;
      border-bottom: 38rpx solid #005bac;
      border-left: 38rpx solid transparent;

      .u-icon {
        color: #fff;
        transform: scale(0.8);
        position: absolute;
        left: -24rpx;
        bottom: -38rpx;
        .u-icon__icon {
          font-size: 24rpx;
        }
      }
    }
    .screen-item:first-child .screen-item-title {
      margin-top: 116rpx;
    }
  }
  @media screen and (max-width: 349px) {
    .checked-icon .u-icon {
      transform: scale(0.7);
      left: -11px;
      bottom: -17px;
    }
    .screen-item:first-child .screen-item-title {
      margin-top: 116rpx;
    }
  }
}
</style>
