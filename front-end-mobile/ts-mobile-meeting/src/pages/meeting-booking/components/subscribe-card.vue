<template>
  <view class="basic_card" @click="handleClick">
    <view class="basic_card_top mb_4">
      <view class="basic_card_top_content">
        <view class="title ">
          <text class="name">{{ dataSource.roomName }}</text>
        </view>
        <view class="date">
          <image
            mode="aspectFill"
            class="icon_16 mr_5"
            src="@/assets/img/icon_people.png"
          ></image>
          <view class="mr_8 text_style">
            {{ dataSource.capacitance }}
          </view>
        </view>
        <view class="address">
          <image
            mode="aspectFill"
            class="icon_16 mr_5"
            src="@/assets/img/cion_address.png"
          ></image>
          <view class="address_text text_style">
            {{
              `${dataSource.location || ''} ${
                dataSource.floor ? '-' + dataSource.floor + '层' : ''
              }`
            }}
          </view>
        </view>
      </view>
      <view
        class="basic_card_top_image"
        @click.stop="previewImage(dataSource.emphasis)"
      >
        <image
          mode="aspectFill"
          :src="
            dataSource.emphasis
              ? $config.BASE_HOST + dataSource.emphasis
              : iconEmpty
          "
          class="img"
        ></image>
      </view>
    </view>
  </view>
</template>

<script>
const iconEmpty = require('@/assets/img/icon_empty.png');
export default {
  props: {
    dataSource: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      iconEmpty
    };
  },
  methods: {
    previewImage(emphasis) {
      if (!emphasis) return;
      let urlList = [];
      urlList.push(this.$config.BASE_HOST + emphasis); //push中的参数为 :src="item.img_url" 中的图片地址
      uni.previewImage({
        indicator: 'number',
        loop: true,
        urls: urlList
      });
    },
    handleClick() {
      this.$emit('click', this.dataSource);
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/flex.scss';

.basic_card {
  width: 100%;
  // margin-top: 16rpx;
  position: relative;
  background: #ffffff;
  padding: 16rpx 32rpx;
  .basic_card_top {
    @include vue-flex;
    height: 134rpx;
    .basic_card_top_content {
      flex: 1;
      @include vue-flex;
      flex-direction: column;
      justify-content: space-between;
      padding-right: 24rpx;
      .title {
        @include vue-flex;
        align-items: center;
        & > .name {
          flex: 1;
          font-size: 32rpx;
          font-weight: bold;
          color: #333333;
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          margin-right: 26rpx;
        }
        .capacitance {
          @include vue-flex;
          width: 100rpx;
          align-items: center;
          font-size: 28rpx;
        }
      }
      .date,
      .address {
        display: flex;
        align-items: center;

        .text_style {
          font-size: 24rpx;
          line-height: 24rpx;
          font-weight: 400;
          color: #333333;
        }
        .address_text {
          flex: 1;
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
        }
        .text_red {
          color: #d21f1f;
        }
      }
    }

    .basic_card_top_image {
      position: relative;
      width: 220rpx;
      max-width: 220rpx;
      min-width: 220rpx;
      height: 100%;
      .img {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
      }
      .img-desc {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 24rpx;
        color: #ffffff;
        height: 40rpx;
        background: rgba(32, 32, 32, 0.5);
        line-height: 40rpx;
        border-bottom-left-radius: 8rpx;
        border-bottom-right-radius: 8rpx;
      }
    }
  }

  .modal {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.59);
  }
}
</style>
<style>
.basic_card + .basic_card {
  margin-top: 16rpx;
}
</style>
