const repeatEndTimeConfig = {
  title: '重复期至',
  prop: 'repeatEndTime',
  type: 'select',
  mode: 'time',
  placeholder: '重复期至'
};
const repeatRateList = [
  {
    value: 0,
    label: '不重复'
  },
  {
    value: 1,
    label: '每天'
  },
  {
    value: 2,
    label: '每周'
  },
  {
    value: 3,
    label: '每月'
  }
];
const signOutAdvanceMinuteList = [
  {
    value: 0,
    label: '不要求签退'
  },
  {
    value: 5,
    label: '5分钟'
  },
  {
    value: 10,
    label: '10分钟'
  },
  {
    value: 15,
    label: '15分钟'
  }
];
const sendRemindAdvanceMinuteList = [
  {
    value: 0,
    label: '不提醒'
  },
  {
    value: 10,
    label: '10分钟'
  },
  {
    value: 15,
    label: '15分钟'
  },
  {
    value: 20,
    label: '20分钟'
  },
  {
    value: 30,
    label: '30分钟'
  }
];
export default {
  data() {
    return {
      // 表单基础设置
      labelWidth: 200,
      formList: [
        {
          title: '主题',
          prop: 'motif',
          type: 'text',
          required: true,
          placeholder: '请输入50字以内名称'
        },
        {
          title: '公开主题',
          prop: 'motifType',
          type: 'switch',
          align: 'right'
        },
        // {
        //   title: '参会人员',
        //   prop: 'attendEmployeeNoListName',
        //   propVal: 'attendEmployeeNoList',
        //   type: 'select',
        //   mode: 'person',
        //   chooseType: 'checkbox',
        //   getListType: 'search',
        //   searchParams: [],
        //   // searchApi: '/ts-basics-bottom/employee/linkman/innerLinkManlist',
        //   placeholder: '请选择参会人员',
        //   required: true
        // },
        {
          title: '选择参会人员',
          prop: 'attendEmployeeNoListName',
          propVal: 'attendEmployeeNoList',
          domSlot: 'attendEmployeeNoSlot',
          className: 'attend-employee-no',
          required: true
        },
        {
          title: '输入参会人员',
          prop: 'attendEmployeeInput',
          type: 'textarea',
          required: false,
          placeholder: '请输入参会人员'
        },
        {
          title: '参会人数',
          prop: 'controlNumber',
          type: 'number',
          required: true,
          class: 'is_readonly',
          placeholder: '请输入参会人数',
          callback: value => {
            return value.toString().match(/^\d*(\.?\d{0,1})/g)[0];
          }
        },
        {
          title: '所需资源',
          prop: 'device',
          propVal: 'device',
          domSlot: 'deviceSlot'
        },
        {
          title: '会议类型',
          prop: 'appTypeName',
          propVal: 'appTypeId',
          type: 'select',
          mode: 'select',
          placeholder: '',
          required: true,
          optionList: []
        },
        {
          title: '预订时段',
          prop: 'bookingTimeName',
          propVal: 'bookingTime',
          type: 'select',
          mode: 'range-picker',
          format: 'HH:mm',
          placeholder: '请选择预订时段',
          required: true
        },
        {
          title: '重复',
          prop: 'repeatRateName',
          propVal: 'repeatRate',
          type: 'select',
          mode: 'select',
          placeholder: '',
          optionList: repeatRateList
        },

        {
          title: '签退',
          prop: 'signOutType',
          type: 'switch',
          align: 'right'
        },

        {
          title: '提醒',
          prop: 'sendRemindAdvanceMinuteName',
          propVal: 'sendRemindAdvanceMinute',
          type: 'select',
          mode: 'select',
          placeholder: '',
          optionList: sendRemindAdvanceMinuteList
        },
        {
          title: '备注',
          prop: 'remark',
          type: 'textarea',
          required: false,
          placeholder: '请输入200字以内名称'
        }
      ],
      form: {
        motif: '',
        motifType: true, // 会议主题1显示2隐藏
        attendEmployeeNoList: [], // 参会人员列表
        attendEmployeeNoListName: '',
        attendEmployeeInput: '',
        controlNumber: '', // 参会人数
        times: '',
        repeatRate: 0, // 是否重复 2重复1不重复
        repeatRateName: '不重复',
        repeatEndTime: '',
        signOutType: false, // 是否要求签退 1要求2不要求
        sendRemindType: 1, //是否发送提醒 1要求2不要求
        sendRemindAdvanceMinute: 15,
        sendRemindAdvanceMinuteName: '15分钟',
        accessoryId: this.guid(), // 附件ID
        agendaList: [], // 主要议程
        appTypeName: '',
        appTypeId: '', // 会议类型
        bookingTimeName: '', // 预约时间段
        bookingTime: [],
        remark: '', //备注,
        userInfo: null
      },
      rules: {
        motif: [
          {
            required: true,
            message: '请输入50字以内名称',
            trigger: ''
          }
        ],
        attendEmployeeNoListName: [
          {
            required: true,
            message: '请选择参会人员',
            trigger: ''
          }
        ],
        controlNumber: [
          {
            required: true,
            message: '请输入参会人数',
            trigger: ''
          }
        ],
        appTypeName: [
          {
            required: true,
            message: '请选择会议类型',
            trigger: ''
          }
        ],
        bookingTimeName: [
          {
            required: true,
            message: '请选择预约时间',
            trigger: ''
          }
        ],
        remark: [
          {
            max: 200,
            message: '请输入200字以内名称',
            trigger: ''
          }
        ],
        attendEmployeeInput: [
          {
            message: '请输入参会人员',
            trigger: ''
          }
        ]
      }
    };
  },
  onLoad(opt) {
    this.init(opt);
  },
  onReady() {
    //绑定验证规则，解决通过props传递变量时，微信小程序会过滤掉对象中的方法，导致自定义验证规则无效
    this.$refs.baseForm.$refs.uForm.setRules(this.rules);
  },
  // computed: {
  //   userInfo() {
  //     return this.$store.state.common.userInfo;
  //   }
  // },
  methods: {
    async init(opt) {
      try {
        let gold = await this.ajax.getAllGlobalSetting();
        if (gold.object.orgCode == 'hnnkyy') {
          this.formList[3].required = true;
        }
        let userRes = await this.ajax.getUserInfo();
        this.userInfo = userRes.object;
        // 默认主题
        // this.form.motif = this.userInfo.employeeName + '预约的会议';
        // 预定人设置为默认参会人员
        this.form.attendEmployeeNoList = [this.userInfo.employeeNo];
        this.form.attendEmployeeNoListName = this.userInfo.employeeName;
        this.personlist = [
          {
            id: this.userInfo.employeeNo,
            name: this.userInfo.employeeName
          }
        ];
        // 会议类型
        let meetingRoomTypes = await this.ajax.getDictDatas({
          typeCode: 'MEETING_TYPE'
        });
        meetingRoomTypes.object = meetingRoomTypes.object.map(e => {
          return {
            value: e.itemNameValue,
            label: e.itemName
          };
        });
        this.$set(this.formList[6], 'optionList', meetingRoomTypes.object);
        if (meetingRoomTypes.object.length > 0) {
          this.form.appTypeId = meetingRoomTypes.object[0].value;
          this.form.appTypeName = meetingRoomTypes.object[0].label;
        }
        if (this.type === 'reschedule') {
          let form = await this.ajax.getBoardRoomApply({
            id: opt.applyId
          });
          form = form.object;
          form.motifType = form.motifType == '1';

          form.signOutType = form.signOutType == '1';

          form.controlNumber =
            form.controlNumber || form.attendEmployeeList.length;
          // 参会人员
          let attendEmployeeNoListName = [];
          let attendEmployeeNoList = [];
          for (const i in form.attendEmployeeList) {
            const e = form.attendEmployeeList[i];
            attendEmployeeNoList.push(e.usercode);
            attendEmployeeNoListName.push(e.username);
          }
          form.attendEmployeeNoListName = attendEmployeeNoListName.join(',');
          form.attendEmployeeNoList = attendEmployeeNoList;
          // 所需资源
          this.handleUpdateDeviceSource(form.device);
          // 会议类型
          let meetingRoom = meetingRoomTypes.object.find(e => {
            return e.value === form.appTypeId;
          });
          form.appTypeId = meetingRoom.value;
          form.appTypeName = meetingRoom.label;
          // 重复
          if (!form.repeatEndTime) {
            form.repeatRate = 0;
          }
          form.repeatRateName = repeatRateList.find(e => {
            return e.value === form.repeatRate;
          }).label;
          // 提醒

          if (form.sendRemindAdvanceMinute) {
            form.sendRemindAdvanceMinuteName = sendRemindAdvanceMinuteList.find(
              e => {
                return e.value === form.sendRemindAdvanceMinute;
              }
            ).label;
          } else {
          }

          form.sendRemindType =
            !form.sendRemindAdvanceMinute || form.sendRemindAdvanceMinute == 0
              ? 2
              : 1;
          // 复制文件
          let business = await this.ajax.copyBusinessIdFiles({
            businessId: form.accessoryId
          });
          this.fileList = business.object || [];
          if (business.object && business.object.length > 0) {
            form.accessoryId = business.object[0].businessId;
          } else {
            delete form.accessoryId;
          }
          this.form = Object.assign(this.form, form);
        }
      } catch (error) {
        console.log(error);
      }
    },
    guid() {
      function S4() {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
      }
      return (
        S4() +
        S4() +
        '-' +
        S4() +
        '-' +
        S4() +
        '-' +
        S4() +
        '-' +
        S4() +
        S4() +
        S4()
      );
    },
    handleUpdateDeviceSource(device) {
      if (!device || !Object.keys(this.meetingRoomInfo).length) {
        setTimeout(() => {
          device && this.handleUpdateDeviceSource(device);
        }, 100);
        return;
      }
      let list = device.split(',');
      this.deviceList.forEach((item, index) => {
        if (list.includes(item.name)) {
          this.$set(this.deviceList[index], 'checked', true);
        }
      });
    }
  },

  watch: {
    'form.attendEmployeeNoList': {
      handler(newVal, oldVal) {
        this.$set(
          this.form,
          'controlNumber',
          newVal ? newVal.length.toString() : ''
        );
      },
      immediate: true,
      deep: true
    },
    'form.repeatRate': {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal == 0) {
          this.formList = this.formList.filter(e => {
            return e.propVal != 'repeatEndTime';
          });
        } else {
          const idx = this.formList.findIndex(e => {
            return e.propVal === 'repeatRate';
          });
          this.formList.splice(
            idx + 1,
            0,
            JSON.parse(JSON.stringify(repeatEndTimeConfig))
          );
        }
      }
    }
  }
};
