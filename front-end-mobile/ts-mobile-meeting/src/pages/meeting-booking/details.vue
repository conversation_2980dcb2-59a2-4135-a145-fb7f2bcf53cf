<template>
  <view class="meeting_booking_details">
    <u-navbar title="资源预约" :customBack="onClickBack" />
    <view class="container">
      <collapse-card :dataSource="meetingRoomInfo" />
      <base-form
        ref="baseForm"
        :formLabelWidth="labelWidth"
        :formList="formList"
        :formData.sync="form"
        :rules="rules"
        :readOnly="readOnly"
        :showSubmitButton="false"
      >
        <template slot="deviceSlot">
          <u-checkbox-group ref="checkboxGroup" @change="handleboxGroupChange">
            <u-checkbox
              v-for="(item, index) of deviceList"
              v-model="item.checked"
              :key="index"
              :name="item.name"
            >
              {{ item.name }}
            </u-checkbox>
          </u-checkbox-group>
        </template>

        <template slot="attendEmployeeNoSlot">
          <u-input
            v-if="!viewerNameEllipsis"
            placeholder="请选择"
            v-model="form.attendEmployeeNoListName"
            trim
            input-align="right"
            @tap="showPersonSelet"
          ></u-input>
          <view class="row_value row_value_personal" v-if="viewerNameEllipsis">
            <text class="personNameStr" @tap="showPersonSelet">{{
              viewerNameEllipsis
            }}</text>
            <uni-icons
              v-if="viewerNameEllipsis"
              :size="60"
              class="uni-icon-wrapper"
              color="#ccc"
              type="closeempty"
              @tap="emptyPerson"
            />
          </view>
        </template>
      </base-form>
      <view class="base_card">
        <base-item
          left-text="相关资料"
          :right-style="{ color: '#333333', fontSize: '28rpx' }"
        >
          <template v-slot:right>
            <view class="icon_box">
              <form
                class="addBox oa-icon oa-icon-plus-circle"
                ref="fileinput"
              ></form>
            </view>
          </template>
        </base-item>
        <base-item
          v-for="file in fileList"
          :key="file.id"
          :left-text="file.originalName"
          :left-style="{ color: '#666666', flex: '1' }"
          @click="handleActionFile(file)"
        />
      </view>
      <!-- 主要议程 -->
      <view class="base_card">
        <main-agenda-card v-model="form.agendaList" />
      </view>
    </view>
    <u-button class="footer_btn" :loading="btnLoading" @click="handleSave">
      确定
    </u-button>
    <bottom-menu
      ref="bottomMenu"
      :actions="bottomMenuActions"
      @preview="handlePreview"
      @download="handleDownload"
      @delete="handleDelete"
    />
  </view>
</template>

<script>
import index from './details.js';
import BaseForm from '@/components/base-form/base-form.vue';
import Base64 from '@/common/js/base64.min.js';
import BottomMenu from '@/components/bottom-menu/bottom-menu.vue';
import MainAgendaCard from '@/pages/common/main-agenda-card/index';
import CollapseCard from '@/pages/common/collapse-card/index';
export default {
  components: { BaseForm, BottomMenu, MainAgendaCard, CollapseCard },
  mixins: [index],
  data() {
    return {
      readOnly: false,
      btnLoading: false,
      fileList: [],
      bottomMenuActions: [
        { label: '预览', emitName: 'preview' },
        { label: '下载', emitName: 'download' },
        { label: '删除', emitName: 'delete' }
      ],
      meetingRoomInfo: {},
      formatDate: '',
      type: '',
      subscribeStatus: '',
      deviceList: [],
      personlist: [],
      viewerNameEllipsis: ''
    };
  },
  onLoad(opt) {
    this.formatDate = opt.date;
    this.type = opt.type;
    this.subscribeStatus = opt.subscribeStatus || '1';
    this.ajax.getBoardRoom({ id: opt.boardRoomId }).then(res => {
      this.meetingRoomInfo = res.object || {};
      this.deviceList = this.meetingRoomInfo.deviceList || [];
    });
  },
  methods: {
    onClickBack() {
      uni.navigateTo({
        url: `/pages/meeting-booking/index?fromPage=workBench&index=0&subscribeStatus=${this.subscribeStatus}`
      });
    },
    handleActionFile(row) {
      this.$refs.bottomMenu.show(row);
    },
    handleboxGroupChange(e = []) {
      this.$set(this.form, 'device', e.join(','));
    },
    //跳转至人员选择
    showPersonSelet() {
      let _self = this;
      //监听事件
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', data => {
        _self.personlist = data;
        let viewerNameArr = [],
          viewerIdArr = [],
          viewerNameEllipsisArr = [];
        data.forEach((item, index) => {
          viewerNameArr.push(item.name);
          viewerIdArr.push(item.id);
          if (index < 3) {
            viewerNameEllipsisArr.push(item.name);
          }
        });
        _self.viewerNameEllipsis = viewerNameEllipsisArr.join(',');
        if (data.length > 3) {
          _self.viewerNameEllipsis += `等${data.length}人`;
        }

        _self.form.attendEmployeeNoListName = viewerNameArr.join(',');
        _self.form.attendEmployeeNoList = viewerIdArr;
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
      });
      uni.setStorageSync('person_list', JSON.stringify(_self.personlist));
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=checkBox'
      });
    },
    emptyPerson() {
      let _self = this;
      _self.personlist = [];
      _self.viewerNameEllipsis = '';
      _self.form.attendEmployeeNoListName = '';
      _self.form.attendEmployeeNoList = '';
    },
    //上传文件
    uploadFile(files, inputDom) {
      //创建对象实例
      let formData = new FormData();
      //追加文件数据
      for (let i = 0; i < files.length; i++) {
        formData.append('file', files[i]);
      }
      //上传单个
      let xhr = new XMLHttpRequest();
      let requestUrl = `${this.$config.BASE_HOST}/ts-basics-bottom/fileAttachment/upload?moduleName=meeting&businessId=${this.form.accessoryId}`;
      xhr.open('POST', requestUrl, true);
      xhr.upload.addEventListener(
        'progress',
        function(event) {
          if (event.lengthComputable) {
            let percent = Math.ceil((event.loaded * 100) / event.total) + '%';
            uni.showLoading({
              title: `上传中(${percent})`
            });
          }
        },
        false
      );
      xhr.ontimeout = function() {
        // xhr请求超时事件处理
        uni.showToast({
          title: '请求超时',
          icon: 'none'
        });
      };
      xhr.onreadystatechange = async () => {
        if (xhr.readyState === 4 && xhr.status === 200) {
          //上传成功
          let res = JSON.parse(xhr.responseText);
          if (res.statusCode === 302 || res.statusCode === 21000) {
            this.$store.dispatch('common/goToLogin');
            // uni.reLaunch({
            //   url: '/pages/login/login'
            // });
          } else if (res.statusCode != 200) {
            uni.showModal({
              title: '提示',
              showCancel: false,
              confirmColor: '#005BAC',
              content: res.message
            });
          } else {
            uni.showToast({
              title: '上传成功',
              icon: 'none'
            });
            inputDom.value = '';
            const fileList = await this.ajax.getFileAttachmentByBusinessId({
              businessId: this.form.accessoryId
            });
            this.fileList = fileList.object || [];
          }
        }
      };
      xhr.send(formData);
    },
    // 文件预览
    handlePreview(row) {
      let filePath = `${
        this.$config.ENABLE_FILE_PREVIEW
          ? this.$config.DOCUMENT_BASE_HOST
          : this.$config.BASE_HOST
      }/ts-basics-bottom/fileAttachment/downloadFile/${row.fileId ||
        row.id}?fullfilename=${row.id}.${row.fileExtension}&token=${
        this.token
      }`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      } else {
        this.$downloadFile.downloadFile(filePath);
      }
    },
    // 文件下载
    handleDownload(row) {
      let filePath = `${
        this.$config.BASE_HOST
      }/ts-basics-bottom/fileAttachment/downloadFile/${row.fileId ||
        row.id}?fullfilename=${row.id}.${row.fileExtension}&token=${
        this.token
      }`;
      this.$downloadFile.downloadFile(filePath);
    },
    // 删除附件
    async handleDelete(row) {
      try {
        await this.ajax.deleteFileId({
          fileid: row.id
        });
        uni.showToast({
          title: '删除成功！'
        });
        const idx = this.fileList.findIndex(e => {
          return e.id == row.id;
        });
        this.fileList.splice(idx, 1);
      } catch (error) {}
    },
    // handleSave() {
    //   this.$refs.baseForm.submit();
    // },
    async handleSave() {
      // const res = this.$refs.baseForm.validate();
      // if (!res) return;
      this.btnLoading = true;
      let params = JSON.parse(JSON.stringify(this.form));
      // 议程排序重新赋值
      params.agendaList = params.agendaList.map((e, idx) => {
        return { ...e, num: idx };
      });
      // 时间区间
      params.startTime = this.formatDate + ' ' + params.bookingTime[0] + ':00';
      params.endTime = this.formatDate + ' ' + params.bookingTime[1] + ':00';
      // 是否发送提醒 1要求2不要求
      if (params.sendRemindAdvanceMinute == 0) {
        delete params.sendRemindAdvanceMinute;
        params.sendRemindType = 2;
      } else {
        params.sendRemindType = 1;
      }
      // 是否要求签退 1要求2不要求
      params.signOutType = params.signOutType ? 1 : 2;
      // 会议主题1显示2隐藏
      params.motifType = params.motifType ? 1 : 2;
      params.boardroomId = this.meetingRoomInfo.id;

      try {
        switch (this.type) {
          case 'reschedule':
          case 'ADD':
            let res = await this.ajax.addBoardRoomApply(params);
            if (res.success) {
              uni.showToast({
                title:
                  this.meetingRoomInfo.needfFlow == '0'
                    ? '提交成功，该资源预约无须审批，请您按时出席会议'
                    : '新增成功！',
                icon: 'none'
              });
              setTimeout(() => {
                this.btnLoading = false;
                uni.navigateTo({
                  url: `/pages/meeting-booking/index?fromPage=workBench&index=0&subscribeStatus=${this.subscribeStatus}`
                });
              }, 800);
            } else {
              uni.showToast({
                title: res.message || '预约失败',
                icon: 'none'
              });
            }
            break;
          case 'EDIT':
            // params.agendaList = params.agendaList.map(e => {
            //   return { ...e, applyId: this.form.id };
            // });
            // await this.ajax.updateBoardRoomAgenda(params.agendaList);
            // this.$message({
            //   showClose: true,
            //   message: '修改成功！',
            //   type: 'success'
            // });
            // this.$emit('ok', params.agendaList);
            break;
          default:
            break;
        }
      } catch (error) {
        console.log(error);
        this.btnLoading = false;
      } finally {
        this.btnLoading = false;
      }
    }
  },
  mounted() {
    // #ifdef H5
    if (this.$refs.fileinput) {
      let input = document.createElement('input');
      input.style.width = '100%';
      input.type = 'file'; //添加file类型
      // input.accept='.pdf' //限制只能上传PDF文件
      input.style.height = '100%';
      input.style.position = 'absolute';
      input.style.top = '0';
      input.style.right = '0';
      input.style.opacity = '0';
      input.style.overflow = 'hidden'; //防止注意input 元素溢出
      input.id = 'file';
      input.multiple = 'multiple'; //安卓浏览器不兼容（除QQ浏览器）
      input.onchange = event => {
        let files = event.target.files;
        this.uploadFile(files, input);
      };
      this.$refs.fileinput.$el.appendChild(input);
    }

    // #endif
  }
};
</script>
<style lang="scss" scoped>
.meeting_booking_details {
  height: 100%;
  display: flex;
  flex-direction: column;
  ::v-deep {
    .attend-employee-no {
      .u-form-item--right__content__icon {
        display: flex;
        flex-direction: column;
      }
    }
    .row_value_personal {
      color: #333;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      text-align: left;
    }
  }

  .container {
    flex: 1;
    overflow: auto;
    margin-top: 16rpx;
    .base_card {
      margin-top: 16rpx;
      background: white;
      .motif_box {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .icon_16 {
          margin-left: 16rpx;
        }
        .motif_box_label {
          font-size: 24rpx;
          font-weight: 400;
          color: #333333;
          text-align: right;
          flex: 1;
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
        }
      }
    }
  }
}
.addBox {
  font-size: 36rpx;
  position: relative;
}
.footer_btn {
  margin: 32rpx 30rpx;
  height: 88rpx;
  background: #005bac;
  border-radius: 8rpx;
  font-size: 36rpx;
  font-weight: 400;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  &.u-default-hover {
    color: #fff !important;
    background-color: #005bac !important;
  }
}
</style>
