<template>
  <view class="meeting_booking">
    <u-navbar class="header" title="资源预约" :customBack="onClickBack">
    </u-navbar>
    <view class="calender_nav">
      <view class="today" :style="todayStyle" @click="handleToday">今日</view>
      <view class="calender_nav_title">
        <view class="indicator" @click="switch_week('prev')">
          <u-icon name="arrow-left"></u-icon>
        </view>
        <text class="calender_nav_title_text">
          {{
            `${dayjs(calendarValue).format(
              'YYYY[年]MM[月]DD[日]'
            )}  ${getWeekName}`
          }}
        </text>
        <view class="indicator" @click="switch_week('next')">
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <image
        src="@/assets/img/calendar.png"
        class="img"
        @click="toggle"
      ></image>
      <view class="dropdown_menu" v-if="visible">
        <uni-calendar
          :date="calendarValue"
          ref="lxCalendar"
          :head="false"
          :insert="true"
          :lunar="false"
          @change="change"
        />
        <view @click="visible = false" class="mask_layer"></view>
      </view>
    </view>
    <view class="menu">
      <u-dropdown inactive-color="#333333">
        <u-dropdown-item
          v-model="subscribeStatus"
          @change="dropdownChange"
          :title="subscribeText"
          :options="subscribeOption"
        ></u-dropdown-item>
      </u-dropdown>
      <i
        @click.stop="handleScreen"
        class="screen_icon oa-icon oa-icon-shaixuan"
      />
      <view
        v-if="subscribeStatus == 2"
        class="subscribe_btn"
        @click.stop="handleSubscribe"
      >
        订阅管理
      </view>
    </view>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <booking-card
          v-for="(item, idx) in boardRoomList"
          :key="idx"
          :dataSource="item"
          @add="handleAdd(item)"
        ></booking-card>
      </mescroll>
    </view>

    <base-screen
      ref="baseScreen"
      :screenList="screenList"
      :screenData="screenData"
      @save="handleSave"
      @reset="handleReset"
    />
  </view>
</template>

<script>
import BookingCard from './components/booking-card.vue';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import BaseScreen from '@/components/base-screen/index.vue';
const dayjs = require('dayjs');
const weekList = [
  '星期日',
  '星期一',
  '星期二',
  '星期三',
  '星期四',
  '星期五',
  '星期六'
];
export default {
  components: { mescroll, BookingCard, BaseScreen },
  data() {
    return {
      calendarValue: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      visible: false,
      subscribeStatus: '1',
      subscribeOption: [
        {
          label: '我的订阅',
          value: '2'
        },
        {
          label: '全部资源',
          value: '1'
        }
      ],
      isShowScreen: false,
      screenList: [
        { label: '筛选条件', prop: 'isVideo', mode: 'radio', option: [] },
        { label: '设备', prop: 'deviceIdList', mode: 'checkbox', option: [] },
        {
          label: '容纳人数>',
          prop: 'capacitance',
          mode: 'input',
          placeholder: '请输入容纳人数',
          type: 'number',
          callback: value => {
            return value.toString().match(/^\d*(\.?\d{0,1})/g)[0];
          }
        },
        {
          label: '空闲时段',
          prop: 'freeTimeName',
          propVal: 'freeTime',
          mode: 'dateRange',
          placeholder: '请选择空闲时段',
          format: 'HH:mm',
          type: 'select'
        }
      ],
      screenData: {
        isVideo: '',
        deviceIdList: [],
        freeTime: [],
        freeTimeName: ''
      },
      boardRoomList: []
    };
  },
  onLoad(opt) {
    this.subscribeStatus = opt.subscribeStatus || '1';
    this.dataLoad();
  },
  created() {
    this.init();
  },
  methods: {
    dayjs,
    async dataLoad() {
      try {
        let meetingRoomTypes = await this.ajax.getDictDatas({
          typeCode: 'RESOURCES_TYPE'
        });
        meetingRoomTypes = meetingRoomTypes.object || [];
        meetingRoomTypes = meetingRoomTypes.map(e => {
          return {
            value: e.itemNameValue,
            name: e.itemName
          };
        });
        this.$set(this.screenList[0], 'option', meetingRoomTypes);
        if (meetingRoomTypes.length > 0) {
          this.screenData.isVideo = meetingRoomTypes[0].value;
        }
        let deviceList = await this.ajax.getDeviceList();
        deviceList = deviceList.object || [];
        deviceList = deviceList.map(e => {
          return {
            value: e.id,
            name: e.name
          };
        });
        this.$set(this.screenList[1], 'option', deviceList);
      } catch (error) {
        console.log(error);
      }
    },
    async init() {
      try {
        let queryParam = {};
        queryParam.subscribeStatus = '2';
        queryParam.startTime =
          dayjs(this.calendarValue).format('YYYY-MM-DD') + ' 00:00:00';
        queryParam.endTime =
          dayjs(this.calendarValue).format('YYYY-MM-DD') + ' 23:59:59';
        let mysubTotal = await this.ajax.getBoardRoomApplyTimeDetailList(
          queryParam
        );
        this.subscribeOption[0].label =
          '我的订阅' +
          `${
            mysubTotal.object.length ? '(' + mysubTotal.object.length + ')' : ''
          }`;
      } catch (error) {}
    },
    /**
     * 点击返回页面
     */
    onClickBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    },
    //
    change(e) {
      this.calendarValue = dayjs(e.fulldate).format('YYYY-MM-DD HH:mm:ss');
      this.visible = false;
      this.datasInit();
      this.$refs.mescroll.downCallback();
    },
    toggle() {
      this.visible = !this.visible;
    },
    switch_week(type) {
      if (type === 'prev') {
        this.calendarValue = dayjs(this.calendarValue)
          .subtract(1, 'days')
          .format('YYYY-MM-DD HH:mm:ss');
      } else {
        this.calendarValue = dayjs(this.calendarValue)
          .add(1, 'days')
          .format('YYYY-MM-DD HH:mm:ss');
      }
      this.datasInit();
      this.$refs.mescroll.downCallback();
    },
    handleToday() {
      this.calendarValue = dayjs().format('YYYY-MM-DD HH:mm:ss');
      this.datasInit();
      this.$refs.mescroll.downCallback();
    },
    handleScreen() {
      this.$refs.baseScreen.open();
    },

    handleRightScreenReset() {},
    // 列表数据
    async getListData(page, successCallback, errorCallback) {
      try {
        let vm = this;
        let queryParam = JSON.parse(JSON.stringify(this.screenData));
        queryParam.subscribeStatus =
          vm.subscribeStatus == '2' ? vm.subscribeStatus : '';
        queryParam.startTime =
          dayjs(vm.calendarValue).format('YYYY-MM-DD') + ' 00:00:00';
        queryParam.endTime =
          dayjs(vm.calendarValue).format('YYYY-MM-DD') + ' 23:59:59';
        // 空闲时间
        if (queryParam.freeTime && queryParam.freeTime.length === 2) {
          queryParam.freeStartTime =
            dayjs(vm.calendarValue).format('YYYY-MM-DD') +
            ' ' +
            queryParam.freeTime[0] +
            ':00';
          queryParam.freeEndTime =
            dayjs(vm.calendarValue).format('YYYY-MM-DD') +
            ' ' +
            queryParam.freeTime[1] +
            ':00';
          delete queryParam.freeTime;
        }
        let boardRoomList = await vm.ajax.getBoardRoomApplyTimeDetailList(
          queryParam
        );
        vm.boardRoomList = boardRoomList.object || [];
        successCallback(vm.boardRoomList);
      } catch (e) {
        errorCallback();
        //TODO handle the exception
      }
    },
    datasInit() {
      let vm = this;
      vm.boardRoomList = [];
    },
    setListData(rows) {
      let vm = this;

      vm.boardRoomList = vm.boardRoomList.concat(rows);
      this.$forceUpdate();
    },
    // 切换下拉菜单
    dropdownChange() {
      this.datasInit();
      this.$refs.mescroll.downCallback();
    },
    // 订阅管理
    handleSubscribe() {
      uni.$on('subscribe', data => {
        this.datasInit();
        this.$refs.mescroll.downCallback();
        //清除监听，不清除会消耗资源
        uni.$off('subscribe');
      });

      uni.navigateTo({
        url: `/pages/meeting-booking/subscribe?isVideo=${this.screenData.isVideo}`
      });
    },
    // 新增预约
    async handleAdd(item) {
      uni.navigateTo({
        url: `/pages/meeting-booking/details?boardRoomId=${
          item.id
        }&date=${dayjs(this.calendarValue).format(
          'YYYY-MM-DD'
        )}&type=ADD&subscribeStatus=${this.subscribeStatus}`
      });
    },
    // 筛选确定回调
    handleSave(row) {
      this.screenData = Object.assign(this.screenData, row);
      this.datasInit();
      this.$refs.mescroll.downCallback();
    },
    // 筛选条件重置
    handleReset() {
      this.screenData = this.$options.data().screenData;
      this.datasInit();
      this.$refs.mescroll.downCallback();
    }
  },
  computed: {
    getWeekName() {
      return weekList[dayjs(this.calendarValue).day()];
    },
    subscribeText() {
      return this.subscribeOption.find(e => {
        return e.value === this.subscribeStatus;
      }).label;
    },
    todayStyle() {
      let style = {};
      if (
        dayjs(this.calendarValue).format('YYYY-MM-DD') !==
        dayjs().format('YYYY-MM-DD')
      ) {
        style = {
          borderColor: '#999999',
          color: '#999999'
        };
      }
      return style;
    }
  }
};
</script>
<style lang="scss" scoped>
.meeting_booking {
  height: 100%;
  display: flex;
  flex-direction: column;
  .subscribe_btn {
    position: absolute;
    right: 80rpx;
    top: 50%;
    transform: translateY(-50%);
    z-index: 100;
    height: 48rpx;
    border-radius: 24rpx;
    border: 2rpx solid #005bac;
    font-size: 24rpx;
    font-weight: 400;
    color: #005bac;
    padding: 0 24rpx;
    display: flex;
    align-items: center;
  }
  .calender_nav {
    position: relative;
    width: 100%;
    height: 44px;
    background: #ffffff;
    padding: 0 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2rpx solid #eee;
    .today {
      width: 76rpx;
      height: 40rpx;
      border-radius: 20rpx;
      border: 2rpx solid #005bac;
      font-size: 22rpx;
      font-weight: 400;
      color: #005bac;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .img {
      width: 40rpx;
      height: 40rpx;
    }
    .calender_nav_title {
      height: 100%;
      display: flex;
      align-items: center;
      .indicator {
        height: 100%;
        display: flex;
        align-items: center;
        padding: 0 16rpx;
      }
      .calender_nav_title_text {
        font-size: 28rpx;
        font-weight: 400;
        color: #333333;
        margin: 0 20rpx;
        white-space: nowrap;
      }
    }
    .dropdown_menu {
      position: absolute;
      left: 0;
      right: 0;
      top: calc(100% + 2rpx);
      height: 100vh;
      z-index: 1000;
      // background: white;
      display: flex;
      flex-direction: column;
      .mask_layer {
        flex: 1;
        background: rgba(0, 0, 0, 0.3);
      }
    }
  }
  .menu {
    position: relative;
    background: white;
    /deep/.u-dropdown__menu {
      .u-dropdown__menu__item {
        justify-content: flex-start;
        padding: 0 30rpx;
      }
    }
    .screen_icon {
      position: absolute;
      right: 30rpx;
      top: 50%;
      font-size: 36rpx;
      transform: translateY(-50%);
      z-index: 100;
    }
  }
  .mescroll-content {
    flex: 1;
    position: relative;
  }
}
</style>
