<template>
  <view class="subscribe">
    <u-navbar class="header" title="我的订阅" :customBack="onClickBack">
      <text @click="addSubscribe" class="right_slot" slot="right">
        添加订阅
      </text>
    </u-navbar>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <subscribe-card
          v-for="(item, idx) in subscribed"
          :key="idx"
          :dataSource="item"
          @click="handleClick"
        ></subscribe-card>
      </mescroll>
    </view>
    <bottom-menu
      ref="bottomMenu"
      :actions="bottomMenuActions"
      @cancelSubscribe="handleCancel"
    />
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import SubscribeCard from './components/subscribe-card.vue';
import BottomMenu from '@/components/bottom-menu/bottom-menu.vue';
export default {
  components: { mescroll, SubscribeCard, BottomMenu },
  data() {
    return {
      subscribed: [],
      bottomMenuActions: [{ label: '取消订阅', emitName: 'cancelSubscribe' }],
      isVideo: undefined
    };
  },
  onLoad(opt) {
    this.isVideo = opt.isVideo;
  },
  methods: {
    onClickBack() {
      uni.$emit('subscribe');
      uni.navigateBack();
    },
    // 列表数据
    async getListData(page, successCallback, errorCallback) {
      try {
        let res = await this.ajax.getListBoardRoomSubscribe({
          isVideo: this.isVideo
        });
        res = res.object || [];

        this.subscribed = res.filter(e => {
          return e.subscribeStatus === 2;
        });
        successCallback(this.subscribed);
      } catch (e) {
        errorCallback();
        //TODO handle the exception
      }
    },
    datasInit() {
      let vm = this;
      vm.subscribed = [];
    },
    setListData(rows) {
      let vm = this;

      vm.subscribed = rows;
      this.$forceUpdate();
    },
    handleClick(row) {
      this.$refs.bottomMenu.show(row);
    },
    async handleCancel(row) {
      try {
        await this.ajax.delBoardRoomSubscribe({ roomId: row.roomId });
        const idx = this.subscribed.findIndex(e => {
          return e.roomId === row.roomId;
        });
        this.subscribed.splice(idx, 1);
      } catch (error) {}
    },
    addSubscribe() {
      uni.$on('addSubscribe', data => {
        this.datasInit();
        this.$refs.mescroll.downCallback();
        //清除监听，不清除会消耗资源
        uni.$off('addSubscribe');
      });
      uni.navigateTo({
        url: `/pages/meeting-booking/add-subscribe?isVideo=${this.isVideo}`
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.subscribe {
  height: 100%;
  display: flex;
  flex-direction: column;
  .right_slot {
    padding: 0 36rpx;
  }
  .mescroll-content {
    flex: 1;
    position: relative;
  }
}
</style>
