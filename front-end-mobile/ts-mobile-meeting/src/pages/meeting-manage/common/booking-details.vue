<template>
  <view class="booking_details">
    <scroll-view scroll-y="true" class="booking_details_scroll">
      <view>
        <collapse-card :dataSource="boardroomInfo" />
        <view class="base_card">
          <base-item left-text="主题">
            <view slot="right" class="motif_box">
              <text v-if="!isEdit" class="motif_box_label">{{
                bookingDetails.motif
              }}</text>
              <u-input v-else input-align="right" v-model="bookingData.motif" />

              <image
                v-if="!bookingDetails.motifType"
                class="icon_16"
                src="@/assets/img/theme_visible.png"
              ></image>
            </view>
          </base-item>
          <base-item
            left-text="选择参与人员"
            :right-text="attendEmployeeListStr"
            :rightStyle="{ color: isEdit ? '#999999' : '' }"
          />
          <base-item
            left-text="输入参与人员"
            :right-text="bookingData.attendEmployeeInput"
            :rightStyle="{ color: isEdit ? '#999999' : '' }"
          >
            <template slot="right">
              <view v-show="!isEdit" class="right-slot-text">
                {{ bookingDetails.attendEmployeeInput }}
              </view>
              <view v-show="isEdit" class="right-slot-text">
                <u-input
                  input-align="right"
                  v-model="bookingDetails.attendEmployeeInput"
                  type="text"
                />
              </view>
            </template>
          </base-item>
          <base-item
            left-text="参与人数"
            :rightStyle="{ color: isEdit ? '#999999' : '' }"
            :right-text="
              bookingData.attendEmployeeList
                ? bookingData.attendEmployeeList.length
                : ''
            "
          />
          <base-item
            :style="{
              height: isEdit ? 'auto' : '40px'
            }"
            left-text="所需资源"
          >
            <template slot="right">
              <view v-show="!isEdit" class="right-slot-text">
                {{ bookingDetails.device }}
              </view>
              <view v-show="isEdit">
                <u-checkbox-group
                  ref="checkboxGroup"
                  @change="handleboxGroupChange"
                >
                  <u-checkbox
                    v-for="(item, index) of deviceList"
                    v-model="item.checked"
                    :key="index"
                    :name="item.name"
                  >
                    {{ item.name }}
                  </u-checkbox>
                </u-checkbox-group>
              </view>
            </template>
          </base-item>
          <base-item
            left-text="会议类型"
            :right-text="meetingType"
            :rightStyle="{ color: isEdit ? '#999999' : '' }"
          />
          <base-item left-text="预约时段">
            <view slot="right" class="motif_box">
              <text v-if="!isEdit" class="motif_box_label">{{
                dateTimeStr
              }}</text>
              <view v-else>
                <u-input
                  input-align="right"
                  v-model="dateTimeStr"
                  type="select"
                  @click="handleOpenSelectTime"
                />
              </view>
            </view>
          </base-item>

          <base-item left-text="签退">
            <template v-slot:right>
              <text
                v-if="
                  readOnly || [-1, 4].indexOf(meetingInfo.meetingStatus) > -1
                "
                :style="{ fontSize: '28rpx', color: isEdit ? '#999999' : '' }"
              >
                {{ bookingData.signOutType ? '签退' : '无需签退' }}
              </text>
              <view v-else>
                <u-switch
                  size="34"
                  v-model="bookingData.signOutType"
                ></u-switch>
              </view>
            </template>
          </base-item>
          <base-item left-text="提醒">
            <template v-slot:right>
              <text
                v-if="
                  readOnly || [-1, 4].indexOf(meetingInfo.meetingStatus) > -1
                "
                :style="{ fontSize: '28rpx', color: isEdit ? '#999999' : '' }"
              >
                {{ bookingData.sendRemindAdvanceMinuteName }}
              </text>
              <view v-else>
                <u-input
                  input-align="right"
                  v-model="bookingData.sendRemindAdvanceMinuteName"
                  type="select"
                  @click="handleOpenSelect"
                />
              </view>
            </template>
          </base-item>
        </view>
        <!-- 取消信息 -->
        <view class="base_card" v-if="meetingInfo.meetingStatus == '4'">
          <base-item
            left-text="取消人"
            :right-text="meetingInfo.cancelEmployee.employeeName"
          />
          <base-item
            left-text="取消理由"
            :right-text="meetingInfo.cancelRemark"
          />
        </view>
        <!-- 相关资料 -->
        <view class="base_card">
          <file-list-card
            :readOnly="readOnly"
            :accessoryId="bookingData.accessoryId"
          />
        </view>

        <!-- 主要议程 -->
        <view class="base_card">
          <main-agenda-card
            :readOnly="
              readOnly || [-1, 4].indexOf(meetingInfo.meetingStatus) > -1
            "
            v-model="bookingData.agendaList"
          />
        </view>
      </view>
    </scroll-view>
    <view
      class="footer"
      v-if="!(meetingInfo.meetingStatus == 4 && !isBookingUser())"
    >
      <view
        class="footer_btn"
        style="color:#f93534"
        v-if="
          meetingInfo.meetingStatus == '0' &&
            (isBookingUser() || meetingInfo.currentEmpPower.indexOf('1') > -1)
        "
        @click="cancelBoardRoomMeeting"
      >
        取消会议
      </view>
      <view
        class="footer_btn"
        style="color:#f93534"
        v-if="meetingInfo.meetingStatus == '1' && isBookingUser()"
        @click="endBoardRoomApply"
      >
        结束会议
      </view>

      <view
        class="footer_btn"
        style="color: #999999"
        @click="editCancel"
        v-if="meetingInfo.meetingStatus == '0' && isEdit"
      >
        取消
      </view>
      <view
        class="footer_btn"
        @click="handleEdit"
        v-if="meetingInfo.meetingStatus == 0 && isBookingUser()"
      >
        {{ isEdit ? '保存' : '编辑' }}
      </view>
      <view
        class="footer_btn"
        v-if="meetingInfo.meetingStatus == 4 && isBookingUser()"
        @click="handleReschedule"
      >
        重新预定
      </view>

      <view
        class="footer_btn"
        v-if="[1, 0, '1', '0'].includes(meetingInfo.meetingStatus) && !isEdit"
        @click="handleLeaveMeeting"
      >
        请假
      </view>
    </view>
    <u-select
      v-model="visibleSelect"
      :list="sendRemindAdvanceMinuteList"
      @confirm="selectConfirm"
      z-index="10075"
    />
    <base-dialog ref="baseDialog" />

    <base-time-range-picker
      v-model="timePickerVisible"
      :defaultValue="rangePickerRangeDate"
      format="HH:mm"
      :rangeDate="rangePickerRangeDate"
      @confirm="handleConfirmTimePick"
    ></base-time-range-picker>
  </view>
</template>

<script>
import MainAgendaCard from '@/pages/common/main-agenda-card/index';
import FileListCard from '@/pages/common/file-list-card/index';
import BaseItem from '@/components/base-item/base-item.vue';
import CollapseCard from '@/pages/common/collapse-card/index';
import BaseDialog from '@/components/base-dialog/base-dialog.vue';
import BaseTimeRangePicker from '@/components/base-time-range-picker/index';
import dayjs from 'dayjs';

const sendRemindAdvanceMinuteList = [
  { value: 0, label: '不提醒' },
  { value: 10, label: '10分钟' },
  { value: 15, label: '15分钟' },
  { value: 20, label: '20分钟' },
  { value: 30, label: '30分钟' }
];
export default {
  components: {
    BaseItem,
    FileListCard,
    MainAgendaCard,
    BaseDialog,
    CollapseCard,
    BaseTimeRangePicker
  },
  props: {
    // 会议室详情
    boardroomInfo: {
      type: Object,
      default: () => {}
    },

    // 预约详情
    bookingDetails: {
      type: Object,
      default: () => {}
    },
    // 会议详情
    meetingInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      bookingData: {
        signOutType: false,
        sendRemindType: false,
        sendRemindAdvanceMinute: '',
        sendRemindAdvanceMinuteName: ''
      },
      copyInfo: undefined,
      applyEmployee: {
        employeeId: '',
        employeeName: '',
        employeeNo: '',
        orgId: '',
        orgName: '',
        phoneNumber: ''
      }, // 预约人信息
      attendEmployeeListStr: '',
      meetingType: '',
      dateTimeStr: '',
      visibleSelect: false,
      sendRemindAdvanceMinuteList,
      isEdit: false,
      timePickerVisible: false,
      rangePickerRangeDate: [],
      deviceList: [] // 预约所需资源列表
    };
  },
  methods: {
    async handleEdit() {
      if (this.isEdit) {
        try {
          let { motif } = this.bookingData;
          if (!motif) {
            uni.showToast({
              title: '请输入主题',
              icon: 'none'
            });
            return;
          }
          let day = dayjs(this.bookingData.startTime).format('YYYY-MM-DD'),
            [startTime, endTime] = this.rangePickerRangeDate,
            params = {
              motif,
              startTime: `${day} ${startTime}:00`,
              endTime: `${day} ${endTime}:00`
            };
          params.id = this.bookingData.id;
          params.signOutType = this.bookingData.signOutType ? 1 : 2;
          params.sendRemindType = this.bookingData.sendRemindType ? 1 : 2;
          params.sendRemindAdvanceMinute = this.bookingData.sendRemindAdvanceMinute;
          params.agendaList = this.bookingData.agendaList.map((e, idx) => {
            return { ...e, applyId: this.bookingData.id, num: idx };
          });
          params.boardroomId = this.bookingData.boardroomId;
          params.attendEmployeeNoList = this.bookingData.attendEmployeeList.map(
            item => item.usercode
          );
          params.device = this.bookingData.device;
          params.attendEmployeeInput = this.bookingData.attendEmployeeInput;
          await this.ajax.editBoardRoomApply(params);
          this.$emit('refresh');
        } catch (error) {
          console.log(error);
        }
      } else {
        this.bookingData.device = this.bookingDetails.device;
        this.copyInfo = JSON.parse(JSON.stringify(this.bookingData));

        // 所需资源
        let deviceSelectList = (this.bookingDetails.device || '')
          .split(',')
          .filter(item => item);
        this.deviceList = (this.boardroomInfo.deviceList || []).map(item => ({
          ...item,
          checked: deviceSelectList.includes(item.name)
        }));
      }
      this.isEdit = !this.isEdit;
    },
    editCancel() {
      this.bookingData = this.copyInfo;
      this.copyInfo = undefined;
      this.isEdit = false;
    },
    handleOpenSelect() {
      this.visibleSelect = true;
    },
    selectConfirm(e) {
      if (e[0].value === 0) {
        this.bookingData.sendRemindType = false;
        this.bookingData.sendRemindAdvanceMinute = undefined;
      } else {
        this.bookingData.sendRemindType = true;
        this.bookingData.sendRemindAdvanceMinute = e[0].value;
      }

      this.bookingData.sendRemindAdvanceMinuteName =
        this.bookingData.sendRemindType &&
        this.bookingData.sendRemindAdvanceMinute
          ? `提前${this.bookingData.sendRemindAdvanceMinute}分钟`
          : '无需提醒';
    },
    handleOpenSelectTime(type) {
      this.timePickerVisible = true;
    },
    handleConfirmTimePick(data) {
      let [startTime, endTime] = data,
        day = dayjs(this.bookingData.startTime).format('YYYY-MM-DD'),
        fullStartTime = day + ' ' + startTime,
        fullEndTime = day + ' ' + endTime;
      if (dayjs(fullStartTime).isAfter(dayjs(fullEndTime))) {
        startTime = data[1];
        endTime = data[0];
      }
      this.dateTimeStr = `${day} ${startTime}-${endTime}`;
      this.rangePickerRangeDate = [startTime, endTime];
    },
    handleboxGroupChange(e = []) {
      this.$set(this.bookingData, 'device', e.join(','));
    },
    // 取消会议
    cancelBoardRoomMeeting() {
      const vm = this;
      this.$refs.baseDialog.show({
        title: '取消会议',
        content: '确定取消会议吗？',
        showInput: true,
        placeholder: '请输入取消原因',
        save: function(text) {
          vm.ajax
            .cancelBoardRoomMeeting({
              meetingId: vm.meetingInfo.meetingId,
              remark: text
            })
            .then(rer => {
              vm.$emit('back');
            });
        }
      });
    },
    // 结束会议
    endBoardRoomApply() {
      const vm = this;
      this.$refs.baseDialog.show({
        title: '结束会议会议',
        content: '确定结束会议吗？',

        save: function() {
          vm.ajax
            .endBoardRoomApply({
              id: vm.meetingInfo.meetingId
            })
            .then(rer => {
              vm.$emit('back');
            });
        }
      });
    },
    handleReschedule() {
      uni.navigateTo({
        url: `/pages/meeting-booking/details?boardRoomId=${
          this.meetingInfo.boardroomId
        }&date=${dayjs().format(
          'YYYY-MM-DD'
        )}&type=reschedule&subscribeStatus=2&applyId=${
          this.meetingInfo.applyId
        }`
      });
    },
    isBookingUser() {
      return this.userInfo.empId === this.applyEmployee.employeeId;
    },
    /**@desc 请假 */
    handleLeaveMeeting() {
      const vm = this;
      this.$refs.baseDialog.show({
        title: '请假',
        showInput: true,
        placeholder: '请输入请假原因',
        save: function(text) {
          vm.ajax
            .handleLeaveMeeting({
              meetingId: vm.meetingInfo.meetingId,
              remark: text
            })
            .then(rer => {
              vm.$emit('back');
            });
        }
      });
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.common.userInfo;
    },
    readOnly() {
      return !(
        this.userInfo.empId === this.applyEmployee.employeeId && this.isEdit
      );
    }
  },
  watch: {
    bookingDetails: {
      handler(newVal, oldVal) {
        this.bookingData = Object.assign(this.bookingData, newVal);
        // 是否公开主题
        this.bookingData.motifType = this.bookingData.motifType == 1;
        // 预约人信息
        this.applyEmployee = Object.assign(
          this.applyEmployee,
          this.bookingData.applyEmployee
        );
        // 参会人员
        let attendEmployeeList = this.bookingData.attendEmployeeList || [];
        attendEmployeeList = attendEmployeeList.map(e => {
          return e.username;
        });
        this.attendEmployeeListStr = attendEmployeeList.join('、');
        // 会议室类型
        this.ajax
          .getDictDatas({
            typeCode: 'MEETING_TYPE'
          })
          .then(res => {
            const meetingRoomType = res.object.find(e => {
              return e.itemNameValue == this.bookingData.appTypeId;
            });

            this.meetingType = meetingRoomType
              ? meetingRoomType.itemName
              : this.bookingData.appTypeId;
          });
        // 预约时段
        this.dateTimeStr = `${dayjs(this.bookingData.startTime).format(
          'YYYY-MM-DD'
        )} ${dayjs(this.bookingData.startTime).format('HH:mm')}-${dayjs(
          this.bookingData.endTime
        ).format('HH:mm')}`;

        this.rangePickerRangeDate = [
          dayjs(this.bookingData.startTime).format('HH:mm'),
          dayjs(this.bookingData.endTime).format('HH:mm')
        ];
        // 签退
        this.bookingData.signOutType = this.bookingData.signOutType == 1;

        // 提醒
        this.bookingData.sendRemindType = this.bookingData.sendRemindType == 1;
        this.bookingData.sendRemindAdvanceMinuteName =
          this.bookingData.sendRemindType &&
          this.bookingData.sendRemindAdvanceMinute
            ? `提前${this.bookingData.sendRemindAdvanceMinute}分钟`
            : '无需提醒';
      },
      immediate: true,
      deep: true
    }
  }
};
</script>
<style lang="scss" scoped>
.booking_details {
  width: 100%;
  height: 100%;
  padding-bottom: 88rpx;
  .base_card {
    margin-top: 16rpx;
    background: white;
    .motif_box {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .icon_16 {
        margin-left: 16rpx;
      }
      .motif_box_label {
        font-size: 24rpx;
        font-weight: 400;
        color: #333333;
        text-align: right;
        flex: 1;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
      }
    }
  }
  .booking_details_scroll {
    height: 100%;
  }
  .footer + .booking_details_scroll {
    height: calc(100% - 88rpx);
  }
  .footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    background: #fff;
    height: 88rpx;
    .footer_btn {
      height: 100%;
      display: flex;
      align-items: center;
      flex: 1;
      justify-content: center;
      font-size: 32rpx;
      color: #005bac;
    }
  }
  .edit_text {
    color: #999999 !important;
  }
  .right-slot-text {
    flex: 1;
    font-size: 28rpx;
    font-weight: 400;
    color: #333333;
    text-align: right;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
}
.u-inline-input {
  flex: unset;
  width: 65px;
}
</style>
