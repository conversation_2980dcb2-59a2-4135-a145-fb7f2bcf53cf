<template>
  <view class="admin_card" @click="handleClick">
    <view class="title_box mb_4">
      <text class="label">{{ dataSource.motif }}</text>
      <view class="time_box text_style" style="color:#333333">
        {{ lifeTimer }}
      </view>
    </view>
    <view class="text_style text_hidden mb_4">
      {{ meetingTitle }}
    </view>
    <view class="footer">
      <text class="text_style text_hidden" v-html="applyEmployee"> </text>
      <text
        v-if="showStatus && type === 'meeting-admin'"
        class="status text_style"
        :style="statusStyle(dataSource.status)"
      >
        {{ dataSource.meetingStatusLable }}
      </text>
      <text
        v-if="showStatus && type === 'my-meeting'"
        class="status text_style"
        :style="attendanceStyle"
      >
        {{ attendanceStatus }}
      </text>
    </view>
  </view>
</template>

<script>
import dayjs from 'dayjs';
export default {
  props: {
    dataSource: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: ''
    },
    showStatus: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    statusStyle(type) {
      let color = 'rgba(51, 51, 51, 0.5)';
      switch (type) {
        case '0':
          color = '#6B95CB';
          break;
        case '1':
          color = '#239530';
          break;

        default:
          break;
      }
      return { color: color };
    },
    handleClick() {
      this.$emit('click');
    }
  },
  computed: {
    lifeTimer() {
      const startTime = this.dataSource.startTime;
      const endTime = this.dataSource.endTime;
      return `${dayjs(startTime).format('MM-DD')} ${dayjs(startTime).format(
        'HH:mm'
      )}-${dayjs(endTime).format('HH:mm')}`;
    },
    meetingTitle() {
      let location = this.dataSource.location;
      let floor = this.dataSource.floor;
      let boardroomName = this.dataSource.boardroomName;

      return `${location ? location + '-' : ''}${floor ? floor + '层' : ''}${
        boardroomName ? '-' + boardroomName : ''
      }`;
    },
    applyEmployee() {
      let info = Object.assign(
        { employeeName: '', orgName: '' },
        this.dataSource.applyEmployee
      );

      return `预约人&nbsp;&nbsp;${info.employeeName}&nbsp;&nbsp;&nbsp;&nbsp;${info.orgName} `;
    },
    attendanceStatus() {
      return this.dataSource.currentEmpSignout == 1
        ? this.dataSource.currentEmpSignoutLable
        : this.dataSource.currentEmpSigninLable;
    },
    attendanceStyle() {
      let color = '';
      if (
        this.dataSource.currentEmpSignout == 1 ||
        this.dataSource.currentEmpSignin == 2
      ) {
        color = '#6B95CB';
      } else if (this.dataSource.currentEmpSignin == 0) {
        color = '#E24242';
      } else if (this.dataSource.currentEmpSignin == 1) {
        color = '#239530';
      }
      return { color };
    }
  }
};
</script>
<style lang="scss" scoped>
.admin_card {
  margin-top: 16rpx;
  background: white;
  padding: 16rpx 32rpx;
  .text_hidden {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    margin-right: 26rpx;
  }
  .title_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .label {
      font-size: 28rpx;
      font-weight: bold;
      color: #333333;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      margin-right: 26rpx;
    }
    .time_box {
      white-space: nowrap;
    }
  }
  .text_style {
    font-size: 24rpx;
    color: #666;
  }
  .footer {
    display: flex;
    align-items: center;
    .text_style.text_hidden {
      flex: 1;
    }
    .status {
      font-size: 28rpx;
      font-weight: bold;
    }
  }
}
</style>
