<template>
  <view class="meeting_details">
    <u-navbar title="会议详情"> </u-navbar>
    <base-tabs-swiper
      ref="tabSwiper"
      fontSize="28"
      class="tab-swiper-box"
      :list="tabList"
      :current="currentTab"
      :is-scroll="false"
      @change="changeTab"
    />
    <swiper
      class="swiper-box"
      :current="currentTab"
      :duration="300"
      @change="onTabChange"
    >
      <swiper-item
        class="swiper-item"
        v-for="(item, index) in tabList"
        :key="index"
      >
        <component
          :key="`component${index}`"
          :ref="`component${index}`"
          :is="item.component"
          :boardroomInfo="boardroomInfo"
          :bookingDetails="bookingDetails"
          :meetingInfo="meetingInfo"
          @refresh="init(true)"
          @back="goBack"
        />
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
import BaseTabsSwiper from '@/components/base-tabs-swiper/base-tabs-swiper.vue';
import BookingDetails from './booking-details.vue';
import SigninDetails from './signin-details.vue';
import MeetingMinutes from './meeting-minutes.vue';

export default {
  components: {
    BaseTabsSwiper,
    BookingDetails,
    SigninDetails,
    MeetingMinutes
  },
  data() {
    return {
      currentTab: 0,
      tabList: [
        { name: '预约详情', id: 0, component: 'BookingDetails' },
        { name: '签到情况', id: 1, component: 'SigninDetails' },
        { name: '会议纪要', id: 2, component: 'MeetingMinutes' }
      ],
      meetingInfo: {},
      boardroomInfo: {},
      bookingDetails: {
        motif: '',
        attendEmployeeList: [],
        agendaList: [],
        applyEmployee: {},
        boardroomSigninCount: {},
        signOutType: false,
        sendRemindType: false,
        sendRemindAdvanceMinute: '',
        sendRemindAdvanceMinuteName: ''
      }
    };
  },
  onLoad(opt) {
    this.meetingInfo = JSON.parse(uni.getStorageSync('meetingInfo'));
    this.init();
  },
  methods: {
    async init(arg) {
      try {
        // 会议室详情
        if (!arg) {
          const boardroomInfo = await this.ajax.getBoardRoom({
            id: this.meetingInfo.boardroomId
          });
          this.boardroomInfo = boardroomInfo.object;
        }

        const bookingDetails = await this.ajax.getBoardRoomApply({
          id: this.meetingInfo.applyId
        });
        this.bookingDetails = bookingDetails.object;
      } catch (error) {
        console.log(error);
      }
    },
    // tabs切换
    changeTab(index) {
      this.currentTab = index;
    },
    onTabChange(e) {
      this.currentTab = e.target.current || e.detail.current;
    },
    goBack() {
      uni.$emit('meetingDetails');
      uni.navigateBack();
    }
  }
};
</script>
<style lang="scss" scoped>
.meeting_details {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  .search-box {
    display: flex;
    position: relative;
    background-color: #ffffff;
    padding: $uni-spacing-col-base $uni-spacing-row-lg;
    margin-bottom: $uni-spacing-col-base;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: $uni-spacing-row-lg;
      left: $uni-spacing-row-lg;
      height: 1px;
      background-color: #eeeeee;
    }
  }
  .swiper-box {
    flex-grow: 1;
  }
}
</style>
