<template>
  <view style="height:100%">
    <view v-if="boardRoomSummary.length > 0">
      <view
        class="meeting_minutes"
        v-for="(boardRoomItem, idx) in boardRoomSummary"
        :key="idx"
        @click="handleClick(boardRoomItem)"
      >
        <image class="icon" src="@/assets/img/word.png"></image>
        <view class="right">
          <view class="title text_hidden">
            {{ boardRoomItem.accessoryName }}
          </view>
          <view class=" text_style">
            最新更新时间：
            {{ boardRoomItem.updateDate }}
          </view>
        </view>
      </view>
    </view>
    <view v-else class="empty">
      <image mode="aspectFill" src="@/assets/img/empty.png" class="img"></image>
    </view>
    <bottom-menu
      ref="bottomMenu"
      :actions="bottomMenuActions"
      @preview="handlePreview"
      @download="handleDownload"
      @delete="handleDelete"
    />
  </view>
</template>

<script>
import BottomMenu from '@/components/bottom-menu/bottom-menu.vue';
import Base64 from '@/common/js/base64.min.js';
export default {
  components: {
    BottomMenu
  },
  props: {
    meetingInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      boardRoomSummary: [],
      bottomMenuActions: [
        { label: '预览', emitName: 'preview' },
        { label: '下载', emitName: 'download' }
      ]
    };
  },
  methods: {
    async getBoardRoomSummary() {
      let boardRoomSummary = await this.ajax.getListBoardRoomSummary({
        meetingId: this.meetingInfo.meetingId
      });
      this.boardRoomSummary = boardRoomSummary.object || [];
    },
    handleClick(row) {
      this.$refs.bottomMenu.show(row);
    },
    // 文件预览
    handlePreview(row) {
      let fileId =
        row.fileId ||
        row.id ||
        row.accessoryUrl.split('/')[row.accessoryUrl.split('/').length - 1];
      let filePath = `${
        this.$config.ENABLE_FILE_PREVIEW
          ? this.$config.DOCUMENT_BASE_HOST
          : this.$config.BASE_HOST
      }/ts-basics-bottom/fileAttachment/downloadFile/${fileId}?fullfilename=${fileId}.${
        row.fileExtension
      }`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      } else {
        this.$downloadFile.downloadFile(filePath);
      }
    },
    // 文件下载
    handleDownload(row) {
      let fileId =
        row.fileId ||
        row.id ||
        row.accessoryUrl.split('/')[row.accessoryUrl.split('/').length - 1];
      let filePath = `${this.$config.BASE_HOST}/ts-basics-bottom/fileAttachment/downloadFile/${fileId}?fullfilename=${fileId}.${row.fileExtension}`;
      this.$downloadFile.downloadFile(filePath);
    }
  },
  watch: {
    meetingInfo: {
      handler(newVal, oldVal) {
        this.getBoardRoomSummary();
      },
      immediate: true,
      deep: true
    }
  }
};
</script>
<style lang="scss" scoped>
.meeting_minutes {
  display: flex;
  background: white;
  padding: 16rpx 32rpx;
  .icon {
    width: 80rpx;
    max-width: 80rpx;
    min-width: 80rpx;
    height: 80rpx;
  }
  .right {
    padding-left: 32rpx;
    height: 80rpx;

    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .title {
    font-size: 14px;
    font-weight: 400;
    color: #333333;
  }
  .text_hidden {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    margin-right: 26rpx;
  }
  .text_style {
    font-size: 12px;
    font-weight: 400;
    color: #666666;
  }
}
.empty {
  width: 100%;
  height: 100%;
  position: relative;
  .img {
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
