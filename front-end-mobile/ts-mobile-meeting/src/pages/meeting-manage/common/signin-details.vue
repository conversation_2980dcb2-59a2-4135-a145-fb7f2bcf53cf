<template>
  <view class="signin_details">
    <view class="header">
      <image class="header_img" src="@/assets/img/oa-icon-tongji.png"></image>
      <view class="content">
        <view>应到{{ boardroomSigninCount.planNum || 0 }}人</view>
        <view>
          签到{{ boardroomSigninCount.signInNum || 0 }}人，未签到{{
            boardroomSigninCount.noSignInNum || 0
          }}，请假{{ boardroomSigninCount.leaveNum || 0 }}人，签退{{
            boardroomSigninCount.signOut || 0
          }}人{{
            boardroomSigninCount.noInviteSignInNum
              ? '，未邀请签到' + boardroomSigninCount.noInviteSignInNum ||
                0 + '人'
              : ''
          }}
        </view>
      </view>
    </view>
    <!-- 列表 -->
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <view
          class="check_in_card"
          v-for="(item, idx) in checkInDetails"
          :key="idx"
        >
          <view class="title">
            <view>
              {{ getUserInfo(item) }}
            </view>
            <view :style="getColor(item)">
              {{ item.signinStatusLable }}
            </view>
          </view>
          <view class="text_style">
            联系电话：{{ item.signinPhoneNumber || '' }}
          </view>
          <view class="text_style" v-if="item.signinTime">
            签到时间：{{ item.signinTime }}
          </view>
          <view class="text_style" v-if="item.signinOutTime">
            签退时间：{{ item.signinOutTime }}
          </view>
          <view class="text_style" v-if="item.remarks">
            备注：{{ item.remarks || '' }}
          </view>
        </view>
      </mescroll>
    </view>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: { mescroll },
  props: {
    // 预约详情
    bookingDetails: {
      type: Object,
      default: () => {}
    },
    // 会议详情
    meetingInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      checkInDetails: [],
      boardroomSigninCount: {}
    };
  },
  methods: {
    // 列表数据
    async getListData(page, successCallback, errorCallback) {
      try {
        let vm = this;
        // 签到详情
        this.getSignInCount();
        let checkInDetails = await this.ajax.getBoardRoomSignInList({
          applyId: this.meetingInfo.applyId,
          meetingId: this.meetingInfo.meetingId
        });
        checkInDetails = checkInDetails.object || [];

        successCallback(checkInDetails);
      } catch (e) {
        errorCallback();
        //TODO handle the exception
      }
    },
    datasInit() {
      let vm = this;
      vm.checkInDetails = [];
    },
    setListData(rows) {
      let vm = this;

      vm.checkInDetails = rows;
      this.$forceUpdate();
    },
    async getSignInCount() {
      try {
        let boardroomSigninCount = await this.ajax.getSignInCount({
          meetingId: this.meetingInfo.meetingId
        });
        this.boardroomSigninCount = boardroomSigninCount.object;
      } catch (error) {}
    },
    getUserInfo(row) {
      return `${row.signinUsername}${
        row.signinOrgName ? '-' + row.signinOrgName : ''
      }`;
    },
    getColor(item) {
      let style = {};
      switch (item.signinStatus) {
        case '0':
          style.color = '#FF6565';
          break;
        case '1':
          style.color = '#00B578';
          break;
        case '2':
          style.color = '#4876FF';
          break;
        default:
          break;
      }
      return style;
    }
  }
};
</script>
<style lang="scss" scoped>
.signin_details {
  height: 100%;
  display: flex;
  flex-direction: column;
  .header {
    display: flex;
    width: 100%;
    align-items: center;
    padding: 24rpx 32rpx;
    background: white;
    .header_icon {
      font-size: 80rpx;
      color: #005bac;
    }
    .header_img {
      width: 80rpx;
      height: 80rpx;
    }
    .content {
      margin-left: 16rpx;
      & > view:first-child {
        font-size: 14px;
        font-weight: bold;
        color: #333333;
      }
      & > view:last-child {
        font-size: 12px;
        color: #333333;
      }
    }
  }
  .mescroll-content {
    flex: 1;
    position: relative;
  }
  .check_in_card {
    margin-top: 16rpx;
    background: white;
    padding: 16rpx 32rpx;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      view {
        font-size: 28rpx;
        color: #333333;
      }
    }
    .text_style {
      font-size: 24rpx;
      color: #666666;
      margin-top: 8rpx;
    }
  }
}
</style>
