<template>
  <view class="meeting_admin">
    <u-navbar title="会议管理" :customBack="onClickBack"> </u-navbar>
    <base-tabs-swiper
      ref="tabSwiper"
      class="tab-swiper-box"
      :list="tabList"
      :current="currentTab"
      :is-scroll="false"
      fontSize="28"
      @change="changeTab"
    ></base-tabs-swiper>
    <view class="search-box">
      <u-search
        shape="square"
        :show-action="false"
        placeholder="输入主题搜索"
        v-model="queryParam['motif' + currentTab]"
        @search="search"
        @clear="clear"
      ></u-search>
      <i
        @click.stop="handleScreen"
        class="screen_icon oa-icon oa-icon-shaixuan"
      />
    </view>
    <swiper
      class="swiper-box"
      :current="currentTab"
      :duration="300"
      @change="onTabChange"
    >
      <swiper-item
        class="swiper-item"
        v-for="(item, index) in tabList"
        :key="index"
      >
        <mescroll
          :ref="`mescroll${index}`"
          :mescrollIndex="index"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <ts-card
            type="meeting-admin"
            v-for="i in item.list"
            :key="i.id"
            :dataSource="i"
            @click="openDetails(i)"
          />
        </mescroll>
      </swiper-item>
    </swiper>
    <base-screen
      ref="baseScreen"
      :screenList="screenList"
      :screenData="screenData[currentTab]"
      @save="handleSave"
      @reset="handleReset"
    />
  </view>
</template>

<script>
import BaseTabsSwiper from '@/components/base-tabs-swiper/base-tabs-swiper.vue';
import Mescroll from '@/components/mescroll-swiper/mescroll.vue';
import TsCard from '../common/card.vue';
import BaseScreen from '@/components/base-screen/index.vue';
export default {
  components: { BaseTabsSwiper, Mescroll, TsCard, BaseScreen },
  data() {
    return {
      currentTab: 0,
      tabList: [],
      queryParam: {},
      screenList: [
        {
          label: '状态',
          prop: 'meetingStatus',
          mode: 'radio',
          option: [
            {
              value: 0,
              name: '未开始'
            },
            {
              value: 1,
              name: '进行中'
            },
            {
              value: -1,
              name: '已结束'
            },
            {
              value: 4,
              name: '取消'
            }
          ]
        },
        {
          label: '参与人/预约人',
          prop: 'joinEmpCodeName',
          propVal: 'joinEmpCode',
          type: 'select',
          mode: 'person',
          chooseType: 'checkbox',
          getListType: 'search',
          searchParams: [],
          // searchApi: '/employee/list',
          placeholder: '请选择参与人/预约人'
        },

        {
          label: '会议开始时间',
          prop: 'timeName',
          propVal: 'time',
          mode: 'dateRange',
          placeholder: '请选择会议开始时间',
          format: 'YYYY-MM-DD',
          type: 'select'
        }
      ],
      screenData: [
        {
          meetingStatus: '',
          time: [],
          timeName: '',
          joinEmpCodeName: '',
          joinEmpCode: ''
        }
      ]
    };
  },
  onLoad() {
    this.loadData();
  },
  methods: {
    loadData() {
      this.ajax.getBoardRoomList({}).then(res => {
        let _tabList = res.object || [];

        this.tabList = _tabList.map(e => {
          return {
            name: `${e.isVideoLable}(${e.name})`,
            id: e.id,
            list: []
          };
        });
        this.screenData = this.tabList.map(e => {
          return {
            meetingStatus: '',
            time: [],
            timeName: '',
            joinEmpCodeName: '',
            joinEmpCode: ''
          };
        });
      });
    },
    /**
     * 点击返回页面
     */
    onClickBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    },
    // tabs切换
    changeTab(index) {
      this.currentTab = index;
    },
    onTabChange(e) {
      this.currentTab = e.target.current || e.detail.current;
    },
    // 列表数据
    async getListData(page, successCallback, errorCallback, keyword, index) {
      let _totalCount = 0;
      let _list = [];
      let _boardroomId = this.tabList[index].id;
      try {
        let vm = this;
        let param;
        if (this.screenData[index]) {
          param = JSON.parse(JSON.stringify(this.screenData[index]));
        }

        param = Object.assign(
          {
            sidx: 'a2.CREATE_DATE',
            sord: 'desc',
            pageNo: page.num,
            pageSize: page.size
          },
          param
        );
        param.motif = this.queryParam['motif' + index] || '';
        param.boardroomId = _boardroomId;
        if (param.joinEmpCode) {
          param.joinEmpCode = param.joinEmpCode.join(',');
        }
        if (param.time && param.time.length === 2) {
          param.startTime = param.time[0] + ' 00:00';
          param.endTime = param.time[1] + ' 00:00';
        }
        // if (index === this.currentTab) {
        //   const res = await vm.ajax.getBoardRoomMeetingList(param);
        //   _list = res.object.rows;
        //   _totalCount = res.object.totalCount;
        // }
        const res = await vm.ajax.getBoardRoomMeetingList(param);
        _list = res.object.rows;
        _totalCount = res.object.totalCount;
        successCallback(_list, _totalCount, index);
      } catch (e) {
        errorCallback();
        //TODO handle the exception
      }
    },
    datasInit(keyword, index) {
      this.tabList[index]['list'] = [];
    },
    setListData(rows, totalCount, index) {
      this.tabList[index]['list'] = this.tabList[index]['list'].concat(rows);
      //   vm.dataSource = vm.dataSource.concat(rows);
    },
    search(val) {
      this.$nextTick(() => {
        this.datasInit(val, this.currentTab);
        this.$refs[`mescroll${this.currentTab}`][0].downCallback();
      });
    },
    clear() {
      this.search('');
    },
    // 打开筛选条件
    handleScreen() {
      this.$refs.baseScreen.open();
    },
    // 筛选确定回调
    handleSave(row) {
      this.screenData[this.currentTab] = Object.assign(
        this.screenData[this.currentTab],
        row
      );
      this.datasInit('', this.currentTab);
      this.$refs[`mescroll${this.currentTab}`][0].downCallback();
    },
    // 筛选条件重置
    handleReset() {
      this.screenData[this.currentTab] = {
        meetingStatus: '',
        time: [],
        timeName: '',
        joinEmpCodeName: '',
        joinEmpCode: ''
      };
      this.datasInit('', this.currentTab);
      this.$refs[`mescroll${this.currentTab}`][0].downCallback();
    },
    // 打开详情页
    openDetails(row) {
      uni.setStorageSync('meetingInfo', JSON.stringify(row));
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('meetingDetails', data => {
        uni.removeStorageSync('meetingInfo');
        this.datasInit('', this.currentTab);
        this.$refs[`mescroll${this.currentTab}`][0].downCallback();
        //清除监听，不清除会消耗资源
        uni.$off('meetingDetails');
      });
      uni.navigateTo({
        url: `/pages/meeting-manage/common/details`
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.meeting_admin {
  height: 100%;
  display: flex;
  flex-direction: column;
  .search-box {
    display: flex;
    position: relative;
    background-color: #ffffff;
    padding: $uni-spacing-col-base $uni-spacing-row-lg;
    margin-bottom: $uni-spacing-col-base;
    align-items: center;
    .screen_icon {
      font-size: 36rpx;
      margin-left: 24rpx;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: $uni-spacing-row-lg;
      left: $uni-spacing-row-lg;
      height: 1px;
      background-color: #eeeeee;
    }
  }
  .swiper-box {
    flex-grow: 1;
  }
}
</style>
