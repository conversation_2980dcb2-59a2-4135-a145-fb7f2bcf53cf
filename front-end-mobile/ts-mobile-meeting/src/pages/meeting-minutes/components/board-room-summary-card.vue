<template>
  <view class="board_room_summary_card" @click="handleClick">
    <view class="left">
      <image src="@/assets/img/word.png"></image>
    </view>
    <view class="right">
      <view class="title text_hidden mb_4">
        {{ dataScource.accessoryName }}
      </view>
      <view class="info mb_4">
        <text class="text_style">
          作者： {{ dataScource.author.employeeName }}
        </text>
        <text class="text_style">
          大小：{{ filterSize(dataScource.accessorySize) }}
        </text>
      </view>
      <view class="date text_style">
        最新更新时间：{{ dataScource.updateDate }}
      </view>
    </view>
  </view>
</template>

<script>
import dayjs from 'dayjs';
// 求次幂
function pow1024(num) {
  return Math.pow(1024, num);
}
export default {
  props: {
    dataScource: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    filterSize(size) {
      if (!size) return '';
      return size < 1024
        ? size + ' B'
        : size < pow1024(2)
        ? (size / 1024).toFixed(2) + ' KB'
        : size < pow1024(3)
        ? (size / pow1024(2)).toFixed(2) + ' MB'
        : size < pow1024(4)
        ? (size / pow1024(3)).toFixed(2) + ' GB'
        : (size / pow1024(4)).toFixed(2) + ' TB';
    },
    handleClick() {
      this.$emit('click', this.dataScource);
    }
  }
};
</script>
<style lang="scss" scoped>
.board_room_summary_card {
  margin-top: 16rpx;
  background: white;
  padding: 16rpx 32rpx;
  display: flex;
  align-items: center;
  .left {
    width: 80rpx;
    max-width: 80rpx;
    min-width: 80rpx;
    image {
      width: 100%;
      height: 80rpx;
    }
  }
  .right {
    flex: 1;
    padding-left: 32rpx;
    .title {
      font-size: 14px;
      font-weight: 400;
      color: #333333;
    }
    .info {
      display: flex;
      align-items: center;
      & > text:last-child {
        margin-left: 48rpx;
      }
    }
    .text_style {
      font-size: 12px;
      font-weight: 400;
      color: #666666;
    }
  }
  .text_hidden {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    margin-right: 26rpx;
  }
}
</style>
