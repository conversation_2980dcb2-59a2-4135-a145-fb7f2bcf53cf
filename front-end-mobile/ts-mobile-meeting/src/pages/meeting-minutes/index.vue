<template>
  <view class="meeting_minutes">
    <u-navbar title="会议纪要" :customBack="onClickBack"> </u-navbar>
    <view class="total_box">
      <text>
        共{{ countBoardRoomSummary.total }}个会议纪要，本周新增{{
          countBoardRoomSummary.weekTotal
        }}个
      </text>
      <i
        @click.stop="handleScreen"
        class="screen_icon oa-icon oa-icon-shaixuan"
      />
    </view>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <board-room-summary-card
          v-for="(boardRoomItem, idx) in boardRoomSummary"
          :key="idx"
          :dataScource="boardRoomItem"
          @click="handleClick"
        ></board-room-summary-card>
      </mescroll>
    </view>
    <bottom-menu
      ref="bottomMenu"
      :actions="bottomMenuActions"
      @preview="handlePreview"
      @download="handleDownload"
      @delete="handleDelete"
    />
    <base-screen
      ref="baseScreen"
      :screenList="screenList"
      :screenData="screenData"
      @save="handleSave"
      @reset="handleReset"
    />
  </view>
</template>

<script>
import BaseScreen from '@/components/base-screen/index.vue';
import Mescroll from '@/components/mescroll-swiper/mescroll.vue';
import BoardRoomSummaryCard from './components/board-room-summary-card.vue';
import BottomMenu from '@/components/bottom-menu/bottom-menu.vue';
import Base64 from '@/common/js/base64.min.js';
export default {
  components: {
    Mescroll,
    BoardRoomSummaryCard,
    BottomMenu,
    BaseScreen
  },
  data() {
    return {
      boardRoomSummary: [],
      bottomMenuActions: [
        { label: '预览', emitName: 'preview' },
        { label: '下载', emitName: 'download' }
      ],
      countBoardRoomSummary: { total: 0, weekTotal: 0 },
      screenList: [
        {
          label: '发起部门',
          prop: 'applyOrgCodeName',
          propVal: 'applyOrgCode',
          type: 'select',
          mode: 'dept',
          selectMode: 'once',
          chooseType: 'radio',
          placeholder: '请选择发起部门'
        },
        { label: '筛选条件', prop: 'isVideo', mode: 'radio', option: [] },
        { label: '主题', prop: 'motif', mode: 'input' },
        {
          label: '会议时间',
          prop: 'timeName',
          propVal: 'time',
          mode: 'dateRange',
          placeholder: '请选择会议开始时间',
          format: 'YYYY-MM-DD',
          type: 'select'
        },
        {
          label: '参与人/预约人',
          prop: 'joinEmpCodeName',
          propVal: 'joinEmpCode',
          type: 'select',
          mode: 'person',
          chooseType: 'checkbox',
          getListType: 'search',
          searchParams: [],
          // searchApi: '/employee/list',
          placeholder: '请选择参与人/预约人'
        }
      ],
      screenData: {
        applyOrgCodeName: '',
        applyOrgCode: [],
        isVideo: '',
        time: [],
        timeName: '',
        joinEmpCodeName: '',
        joinEmpCode: '',
        motif: ''
      }
    };
  },
  onLoad() {
    this.init();
  },
  methods: {
    /**
     * 点击返回页面
     */
    onClickBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    },
    //   初始化数据
    async init() {
      try {
        const countBoardRoomSummary = await this.ajax.countBoardRoomSummary();
        this.countBoardRoomSummary = countBoardRoomSummary.object;
        let meetingRoomTypes = await this.ajax.getDictDatas({
          typeCode: 'RESOURCES_TYPE'
        });
        meetingRoomTypes = meetingRoomTypes.object || [];
        meetingRoomTypes = meetingRoomTypes.map(e => {
          return {
            value: e.itemNameValue,
            name: e.itemName
          };
        });
        this.$set(this.screenList[1], 'option', meetingRoomTypes);
      } catch (error) {}
    },
    // 列表数据
    async getListData(page, successCallback, errorCallback) {
      try {
        let vm = this;
        let params = JSON.parse(JSON.stringify(this.screenData));
        if (params.applyOrgCode) {
          params.applyOrgCode = params.applyOrgCode.join(',');
        }
        if (params.joinEmpCode) {
          params.joinEmpCode = params.joinEmpCode.join(',');
        }
        if (params.time && params.time.length === 2) {
          params.meetingBeninTime = params.time[0] + ' 00:00:00';
          params.meetingEndTime = params.time[1] + ' 00:00:00';
        }
        let boardRoomSummary = await vm.ajax.getListBoardRoomSummary(params);
        vm.boardRoomSummary = boardRoomSummary.object || [];
        successCallback(vm.boardRoomSummary);
      } catch (e) {
        errorCallback();
        //TODO handle the exception
      }
    },
    datasInit() {
      let vm = this;
      vm.boardRoomSummary = [];
    },

    setListData(rows) {
      let vm = this;

      vm.boardRoomSummary = vm.boardRoomSummary.concat(rows);
    },
    handleClick(row) {
      this.$refs.bottomMenu.show(row);
    },
    // 文件预览
    handlePreview(row) {
      let fileId =
        row.fileId ||
        row.id ||
        row.accessoryUrl.split('/')[row.accessoryUrl.split('/').length - 1];
      let filePath = `${
        this.$config.ENABLE_FILE_PREVIEW
          ? this.$config.DOCUMENT_BASE_HOST
          : this.$config.BASE_HOST
      }/ts-basics-bottom/fileAttachment/downloadFile/${fileId}?fullfilename=${fileId}.${
        row.fileExtension
      }`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      } else {
        this.$downloadFile.downloadFile(filePath);
      }
    },
    // 文件下载
    handleDownload(row) {
      let fileId =
        row.fileId ||
        row.id ||
        row.accessoryUrl.split('/')[row.accessoryUrl.split('/').length - 1];
      let filePath = `${this.$config.BASE_HOST}/ts-basics-bottom/fileAttachment/downloadFile/${fileId}`;
      this.$downloadFile.downloadFile(filePath);
    },
    // 打开筛选条件
    handleScreen() {
      this.$refs.baseScreen.open();
    },
    // 筛选确定回调
    handleSave(row) {
      this.screenData = Object.assign(this.screenData, row);
      this.datasInit();
      this.$refs.mescroll.downCallback();
    },
    // 筛选条件重置
    handleReset() {
      this.screenData = this.$options.data().screenData;
      this.datasInit();
      this.$refs.mescroll.downCallback();
    }
  },
  computed: {
    token() {
      return this.$store.state.common.token;
    }
  }
};
</script>
<style lang="scss" scoped>
.meeting_minutes {
  height: 100%;
  display: flex;
  flex-direction: column;
  .mescroll-content {
    flex: 1;
    position: relative;
  }
  .total_box {
    margin-top: 16rpx;
    padding: 0 32rpx;
    font-size: 24rpx;
    display: flex;
    align-items: center;
    & > text {
      flex: 1;
    }
    .screen_icon {
      font-size: 36rpx;
      margin-left: 24rpx;
    }
  }
}
</style>
