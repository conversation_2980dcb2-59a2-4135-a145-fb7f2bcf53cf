<template>
  <view>
    <base-form
      ref="baseForm"
      :formList="formList"
      :formData.sync="form"
      :readOnly="false"
    ></base-form>
  </view>
</template>

<script>
import BaseForm from '@/components/base-form/base-form.vue';
export default {
  components: {
    BaseForm
  },
  data() {
    return {
      formList: [
        {
          type: 'select',
          title: '人员选择',
          placeholder: '人员选择',
          prop: 'selectValueLabel',
          personnlVal: 'personnlValueList',
          organizationalVal: 'organizationalValueList',
          mode: 'personnl-select',
          componentParams: {
            allPersonnlSwitch: true,
            outSideContactTabs: false
          }
        }
      ],
      form: {}
    };
  }
};
</script>

<style></style>
