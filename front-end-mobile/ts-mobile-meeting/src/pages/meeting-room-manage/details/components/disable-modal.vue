<template>
  <u-popup
    v-model="show"
    width="580"
    border-radius="16"
    mode="center"
    class="disable_modal"
  >
    <view class="container">
      <view class="title">禁用资源</view>
      <u-form
        :model="form"
        ref="disableForm"
        label-width="190"
        :error-type="errorType"
      >
        <u-form-item label="禁用时段" :border-bottom="false">
          <view class="switch_box">
            <text class="switch_label">
              永久禁用
            </text>
            <u-switch size="34" v-model="form.disableType"></u-switch>
          </view>
        </u-form-item>
        <u-form-item
          v-if="!form.disableType"
          :required="true"
          prop="rangePickerValueNames"
        >
          <u-input
            trim
            v-model="form.rangePickerValueNames"
            placeholder="请选择禁用时间段"
            type="select"
            @click="chooseRangePicker"
          />
        </u-form-item>
        <u-form-item
          label="禁用原因"
          label-position="top"
          :required="true"
          prop="disableRemark"
        >
          <u-input
            trim
            type="textarea"
            v-model="form.disableRemark"
            placeholder="请输入200字以内"
          />
        </u-form-item>
        <u-form-item label="联系人" :required="true" prop="contactsCode">
          <u-input
            trim
            v-model="form.contactsName"
            placeholder="请选择联系人"
            type="select"
            @click="choosePerson"
          />
        </u-form-item>
        <u-form-item>
          <view class="switch_box" style="justify-content: space-between;">
            <text class="switch_label_2">
              发送通知给预定了该场地的员工
            </text>
            <u-switch
              size="34"
              v-model="form.disableBookingSendInfo"
            ></u-switch>
          </view>
        </u-form-item>
        <view class="btns" v-if="isEnable">
          <text class="btn" style="color:#E24242" @click="handleEnable">
            取消禁用
          </text>
          <view class="line"></view>
          <text class="btn" @click="handleSave">保存</text>
        </view>
        <view class="btns" v-else>
          <text class="btn" style="color:#666666" @click="handleCancel">
            取消
          </text>
          <view class="line"></view>
          <text class="btn" @click="handleSave">
            保存
          </text>
        </view>
      </u-form>
    </view>
    <!-- 时间区间 -->
    <base-time-range-picker
      v-model="rangePickerShow"
      :defaultValue="form.rangePickerValue"
      :format="'YYYY-MM-DD HH:mm'"
      @confirm="timeRangePickerConfirm"
    ></base-time-range-picker>
    <u-select
      v-model="selectModel"
      :list="form.adminList"
      @confirm="selectConfirm"
    ></u-select>
  </u-popup>
</template>

<script>
import BaseTimeRangePicker from '@/components/base-time-range-picker/index';
export default {
  components: { BaseTimeRangePicker },

  data() {
    return {
      errorType: ['toast'],
      show: false,
      rangePickerShow: false,
      form: {
        disableType: false,
        disableBookingSendInfo: true,
        rangePickerValueNames: '',
        rangePickerValue: [],
        disableRemark: '',
        contactsCode: '',
        contactsName: ''
      },
      selectModel: false,
      rules: {
        rangePickerValueNames: [
          {
            required: true,
            message: '请选择禁用时间段',
            // 可以单个或者同时写两个触发验证方式
            trigger: 'blur,change'
          }
        ],
        disableRemark: [
          {
            required: true,
            message: '请输入禁用原因',
            trigger: 'change'
          }
        ],
        contactsName: [
          {
            required: true,
            message: '请选择联系人',
            trigger: 'change'
          }
        ]
      },
      isEnable: false
    };
  },
  methods: {
    open(row, isEnable) {
      this.isEnable = !!isEnable;
      const data = JSON.parse(JSON.stringify(row));
      data.disableBookingSendInfo =
        data.disableBookingSendInfo == 2 ? false : true;
      data.disableType = data.disableType == 2 ? true : false;
      if (data.disableBegintime && data.disableEndtime) {
        data.rangePickerValue = [data.disableBegintime, data.disableEndtime];
        data.rangePickerValueNames = data.rangePickerValue.join(' 至 ');
      }
      data.adminList = data.adminList || [];
      data.adminList = data.adminList.map(e => {
        return {
          ...e,
          label: `${e.employeeName}${
            e.phoneNumber ? '(' + e.phoneNumber + ')' : ''
          }`,
          value: e.employeeNo
        };
      });
      if (data.adminList.length > 0) {
        data.contactsCode = data.adminList[0].employeeNo;
      }
      data.contactsCode = data.disableContactsCode
        ? data.disableContactsCode
        : data.contactsCode;

      let contactsName = data.adminList.find(e => {
        return e.value == data.contactsCode;
      });
      data.contactsName = contactsName ? contactsName.label : '';
      this.form = Object.assign(this.form, data);
      this.show = true;
      this.$nextTick(() => {
        this.$refs.disableForm.setRules(this.rules);
      });
    },
    // 打开时间区间选择器
    chooseRangePicker() {
      this.$nextTick(() => {
        this.rangePickerShow = true;
      });
    },
    // 时间区间选择器回调
    timeRangePickerConfirm(e) {
      if (e) {
        this.form.rangePickerValue = e;
        this.form.rangePickerValueNames = e ? e.join(' 至 ') : '';
      }
    },
    // 联系人
    choosePerson() {
      this.selectModel = true;
    },
    //列选择器确认事件
    selectConfirm(e) {
      if (e.length > 0) {
        this.form.contactsCode = e[0].value;
        this.form.contactsName = e[0].label;
      }
    },

    handleSave() {
      this.$refs.disableForm.validate(async valid => {
        if (valid) {
          try {
            let params = JSON.parse(JSON.stringify(this.form));
            if (params.rangePickerValue.length === 2) {
              params.disableBegintime = params.rangePickerValue[0] + ':00';
              params.disableEndtime = params.rangePickerValue[1] + ':00';
            }
            params.bookingSendInfo = params.disableBookingSendInfo ? 1 : 2;
            params.disableType = params.disableType ? 2 : 1;
            params.boardroomId = params.id;
            params.remark = params.disableRemark;
            if (params.disableType === 2) {
              if (params.disableBegintime) delete params.disableBegintime;
              if (params.disableEndtime) delete params.disableEndtime;
            }
            const res = await this.ajax.disableBoardRoom(params);
            this.$emit('ok');
            this.handleCancel();
          } catch (error) {}
        }
      });
    },
    // 取消禁用
    async handleEnable() {
      try {
        const res = await this.ajax.enableBoardRoom({ id: this.form.id });

        this.$emit('ok');
        this.handleCancel();
      } catch (error) {}
    },
    // 关闭弹窗
    handleCancel() {
      this.show = false;
      this.form = this.$options.data().form;
    }
  }
};
</script>
<style lang="scss" scoped>
.disable_modal {
  .container {
    width: 100%;
    padding: 32rpx 24rpx 0 24rpx;
    .title {
      font-size: 32rpx;
      line-height: 34rpx;
      font-weight: bold;
      color: #333333;
    }
    .switch_box {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .switch_label {
        font-size: 28rpx;
        font-weight: 400;
        color: #bbb;
        margin-right: 24rpx;
      }
      .switch_label_2 {
        font-size: 24rpx;
        font-weight: 400;
        color: #333333;
      }
    }
    .btns {
      width: 100%;
      height: 96rpx;
      display: flex;
      align-items: center;
      .btn {
        flex: 1;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        font-weight: 400;
        color: #005bac;
        border: 0;
        background: #fff;
      }
      .line {
        width: 2rpx;
        height: 100%;
        background: #eee;
      }
    }
  }
}
</style>
