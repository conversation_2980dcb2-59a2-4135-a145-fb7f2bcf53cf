export default {
  data() {
    return {
      // 表单基础设置
      labelWidth: 200,
      // labelStyle: { 'padding-left': '30rpx' },
      formList: [
        {
          title: '资源封面',
          prop: 'emphasis',
          propVal: 'preEmphasis',
          type: 'file',
          name: 'file',
          maxCount: 1,
          align: 'right',
          action: `${this.$config.BASE_HOST}/ts-basics-bottom/fileAttachment/upload?moduleName=meeting`,
          placeholder: '资源封面'
        },
        {
          title: '资源名称',
          prop: 'name',
          type: 'text',
          required: true,
          placeholder: '请输入20字以内名称',
          maxlength: 20
        },
        {
          title: '资源类型',
          prop: 'isVideoLable',
          propVal: 'isVideo',
          required: true,
          type: 'select',
          mode: 'select',
          placeholder: '',
          required: true,
          optionList: null
        },
        {
          title: '使用上限(人)',
          prop: 'capacitance',
          type: 'number',
          required: true,
          placeholder: '请输入使用上限(人)'
        },
        {
          title: '楼栋/位置',
          prop: 'locationName',
          propVal: 'location',
          type: 'select',
          mode: 'select-secondary-page',
          placeholder: '请选择楼栋/位置',
          optionList: null
        },
        {
          title: '楼层(楼)',
          prop: 'floor',
          type: 'text',
          placeholder: '请输入楼层(楼)'
        },
        {
          title: '管理员',
          prop: 'adminNameList',
          propVal: 'adminCodeList',
          type: 'select',
          mode: 'person',
          chooseType: 'checkbox',
          getListType: 'search',
          searchParams: [],
          // searchApi: '/employee/list',
          placeholder: '请选择管理员',
          required: true
        },
        {
          title: '包含设备',
          prop: 'deviceNameList',
          propVal: 'deviceIdList',
          type: 'select',
          mode: 'select-device',
          placeholder: ''
        },
        {
          title: '资源备注',
          prop: 'remark',
          type: 'text',
          maxlength: 50,
          placeholder: '请输入50字以内名称'
        },
        {
          title: '位置指引',
          prop: 'positionPicture',
          propVal: 'prePositionPicture',
          type: 'file',
          name: 'file',
          maxCount: 1,
          align: 'right',
          action: `${this.$config.BASE_HOST}/ts-basics-bottom/fileAttachment/upload?moduleName=meeting`,
          placeholder: '位置指引'
        },
        {
          title: '预定需审批',
          prop: 'needfFlowName',
          propVal: 'needfFlow',
          type: 'select',
          mode: 'select',
          placeholder: '',
          optionList: [
            {
              value: 1,
              label: '是'
            },
            {
              value: 0,
              label: '否'
            }
          ]
        },
        {
          title: '可预订人员',
          prop: 'useAuthorityType',
          type: 'switch',
          switchLabel: '全部人员',
          align: 'right'
        },
        {
          title: '可预订时段',
          prop: 'bookingTimeName',
          propVal: 'bookingTime',
          type: 'select',
          mode: 'range-picker',
          format: 'HH:mm',
          placeholder: '请选择要求完成时间'
        },
        {
          title: '会议开始后30分钟无人签到，自动释放剩余预约时段',
          prop: 'autoOffType',
          type: 'switch',
          align: 'right',
          labelWidth: '360'
        },
        {
          title: '签到、签退二维码自动刷新，防盗刷',
          prop: 'autoRefreshSignInQrCodeType',
          type: 'switch',
          align: 'right',
          labelWidth: '294'
        }
      ],
      form: {
        name: '', // 会议室名称
        isVideo: '', // 资源类型
        isVideoLable: '',
        capacitance: undefined, // 会议室容量
        location: '', // 会议室位置
        locationName: '',
        positionPicture: [], // 位置指引
        prePositionPicture: [],
        floor: '', // 楼层
        adminCodeList: [], // 会议室管理员编码列表
        adminNameList: '',
        deviceNameList: '',
        deviceIdList: [], // 设备列表
        emphasis: [], // 会议室图片
        preEmphasis: [],
        needfFlow: 0, // 预定需审批
        needfFlowName: '否',
        remark: '', // 备注
        useAuthorityType: true, // 全部明细 0全部1明细
        useAuthorityList: [],
        useAuthorityNameList: '',
        useAuthorityList: [],
        bookingTimeName: '07:00-21:00',
        bookingTime: ['07:00', '21:00'],
        disableTime: '',
        autoOffType: false, // 1 自动关闭 2不自动
        autoRefreshSignInQrCodeType: false // 1 刷新 2不刷新
      },
      rules: {
        name: [
          {
            required: true,
            message: '请输入资源名称',
            trigger: ''
          }
        ],
        isVideoLable: [
          {
            required: true,
            message: '请选择资源类型',
            trigger: ''
          }
        ],
        capacitance: [
          {
            required: true,
            message: '请输入使用上限',
            trigger: ''
          }
        ],
        adminNameList: [
          {
            required: true,
            message: '请选择管理员',
            trigger: ''
          }
        ]
      }
    };
  },
  onLoad(opt) {
    this.init();
  },
  onReady() {
    //绑定验证规则，解决通过props传递变量时，微信小程序会过滤掉对象中的方法，导致自定义验证规则无效
    this.$refs.baseForm.$refs.uForm.setRules(this.rules);
  },
  methods: {
    init() {
      return new Promise(async (resolve, reject) => {
        try {
          let locationList = await this.ajax.getLocationList();
          let meetingRoomTypes = await this.ajax.getDictDatas({
            typeCode: 'RESOURCES_TYPE'
          });
          // 资源类型
          meetingRoomTypes = meetingRoomTypes.object || [];
          meetingRoomTypes = meetingRoomTypes.map(e => {
            return {
              value: e.itemNameValue,
              label: e.itemName
            };
          });

          this.$set(this.formList[2], 'optionList', meetingRoomTypes);
          // 楼栋位置
          locationList = locationList.object || [];
          locationList = locationList.map(e => {
            return {
              value: e,
              label: e
            };
          });
          this.$set(this.formList[4], 'optionList', locationList);

          resolve(true);
        } catch (error) {
          reject(error);
        }
      });
    }
  }
};
