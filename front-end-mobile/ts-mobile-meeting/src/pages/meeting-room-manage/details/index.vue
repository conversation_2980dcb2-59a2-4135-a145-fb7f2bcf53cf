<template>
  <view class="meeting_room_manage_details">
    <u-navbar class="header" :title="title" :customBack="handleBack">
    </u-navbar>
    <view class="form_container">
      <view class="scroll_container">
        <base-form
          ref="baseForm"
          :formLabelWidth="labelWidth"
          :formList="formList"
          :formData.sync="form"
          :rules="rules"
          :readOnly="readOnly"
          :showSubmitButton="false"
          @submit="submit"
          @switchChange="switchChange"
        ></base-form>
      </view>
    </view>
    <view class="footer">
      <u-button
        v-if="type === 'ADD'"
        @click="save"
        class="footer_button"
        data-ref="popup"
        :loading="btnLoading"
      >
        保存
      </u-button>
      <view class="footer_nav" v-else-if="type === 'EDIT' && readOnly">
        <text @click="handleDelete">删除</text>

        <text @click="handleDisable">
          {{ isEnable ? '取消禁用' : '禁用' }}
        </text>

        <text style="color: #005bac;" @click="handleEdit">编辑</text>
      </view>
      <view class="footer_nav" v-else>
        <text style="color: #666;" @click="handleCancel">取消</text>
        <text style="color: #005bac;" @click="save">确定</text>
      </view>
    </view>
    <u-modal
      v-model="deleteVisible"
      :show-cancel-button="true"
      confirmColor="#005bac"
      @confirm="confirm"
      ref="uModal"
      :async-close="true"
      content="是否删除该条资源？"
      title="删除"
    ></u-modal>
    <disable-modal ref="disableModal" @ok="disableModalOk"></disable-modal>
    <u-toast ref="uToast" />
  </view>
</template>

<script>
import index from './index.js';
import BaseForm from '@/components/base-form/base-form.vue';
import DisableModal from './components/disable-modal.vue';
import dayjs from 'dayjs';
const deptConfig = {
  title: '',
  type: 'select',
  mode: 'dept',
  selectMode: 'once',
  chooseType: 'checkbox',
  prop: 'useAuthorityNameList',
  propVal: 'useAuthorityList',
  placeholder: '请选择预订人员'
};
const disableConfig = {
  title: '当前禁用',
  prop: 'disableTime',
  type: 'text',
  labelStyle: { color: '#E24242' },
  class: ['red_text']
};
export default {
  components: { BaseForm, DisableModal },
  mixins: [index],
  data() {
    return {
      type: 'ADD',
      meetingRoomId: '',
      title: '',
      readOnly: false,
      btnLoading: false,
      copyForm: {},
      deleteVisible: false,
      isEnable: false,
      isEdit: false
    };
  },
  onLoad: function(option) {
    this.readOnly = option.type === 'EDIT';
    if (option.type === 'ADD') {
      // 新增
      this.type = 'ADD';
      this.title = '新增资源';
    } else {
      this.type = 'EDIT';
      this.title = '资源详情';
      this.meetingRoomId = option.meetingRoomId;
      this.initLoad();
    }
  },
  onUnload: function() {
    uni.removeStorageSync('meetingRoomEntity');
  },
  methods: {
    async initLoad() {
      try {
        const res = await this.ajax.getBoardRoom({ id: this.meetingRoomId });
        let data = res.object;
        data.emphasis =
          data.emphasis && data.emphasis != '' ? [data.emphasis] : [];
        data.emphasis = data.emphasis.map(e => {
          return { url: this.$config.BASE_HOST + e, fileUrl: e };
        });
        data.preEmphasis = data.emphasis;
        // 管理员
        data.adminList = data.adminList || [];
        data.adminCodeList = [];
        let adminNameList = [];
        for (const adminItem of data.adminList) {
          data.adminCodeList.push(adminItem.employeeNo);
          adminNameList.push(adminItem.employeeName);
        }
        data.adminNameList = adminNameList.join(',');

        // 设备
        data.deviceList = data.deviceList || [];
        data.deviceIdList = [];
        let deviceNameList = [];
        for (const deviceItem of data.deviceList) {
          data.deviceIdList.push(deviceItem.id);
          deviceNameList.push(deviceItem.name);
        }
        data.deviceNameList = deviceNameList.join(',');
        // 位置指引
        data.positionPicture =
          data.positionPicture && data.positionPicture != ''
            ? [data.positionPicture]
            : [];

        data.positionPicture = data.positionPicture.map(e => {
          return { url: this.$config.BASE_HOST + e, fileUrl: e };
        });
        data.prePositionPicture = data.positionPicture;
        // 预定审批
        if (data.needfFlow == '1') {
          data.needfFlow = 1;
          data.needfFlowName = '是';
        } else {
          data.needfFlow = 0;
          data.needfFlowName = '否';
        }
        // 可预定人员
        if (data.useAuthorityType == '1') {
          data.useAuthorityType = false;
          this.formList = this.formList.filter(e => {
            return e.propVal != 'useAuthorityList';
          });
          const idx = this.formList.findIndex(e => {
            return e.prop === 'useAuthorityType';
          });
          this.formList.splice(
            idx + 1,
            0,
            JSON.parse(JSON.stringify(deptConfig))
          );
        } else {
          data.useAuthorityType = true;
        }
        // 可预定时间段
        if (data.bookingTimeBegin && data.bookingTimeEnd) {
          data.bookingTime = [data.bookingTimeBegin, data.bookingTimeEnd];
          data.bookingTimeName = data.bookingTime.join('-');
        }
        data.locationName = data.location;

        // 可预订人员
        if (data.useAuthorityList && data.useAuthorityList.length > 0) {
          data.useAuthorityNameList = data.useAuthorityList
            .map(e => {
              return e.objName;
            })
            .join(',');
          data.useAuthorityList = data.useAuthorityList.map(e => {
            return e.objId;
          });
        }
        // 自动释放
        data.autoOffType = parseInt(data.autoOffType) === 1;
        // 二维码刷新
        data.autoRefreshSignInQrCodeType =
          parseInt(data.autoRefreshSignInQrCodeType) === 1;
        // 禁用时间
        this.handlleDisbleData(data);

        this.form = Object.assign(this.form, data);
        this.isEnable = !!this.form.disableType;
      } catch (error) {}
    },
    handlleDisbleData(data) {
      if (data.disableType) {
        if (data.disableType == 1) {
          this.form.disableTime =
            dayjs(data.disableBegintime).format('MM-DD HH:mm') +
            '-' +
            dayjs(data.disableEndtime).format('MM-DD HH:mm');
        } else {
          this.form.disableTime = '永久禁用';
        }
        this.formList.push(JSON.parse(JSON.stringify(disableConfig)));
      } else {
        this.formList = this.formList.filter(e => {
          return e.prop != 'disableTime';
        });
      }
    },
    handleBack() {
      if (this.isEdit) {
        this.onClickBack();
        this.isEdit = false;
      } else {
        uni.navigateBack();
      }
    },
    onClickBack() {
      uni.navigateTo({
        url: `/pages/meeting-room-manage/index?fromPage=workBench&index=0`
      });
    },
    save() {
      this.$refs.baseForm.submit();
    },
    async submit() {
      this.btnLoading = true;
      let params = JSON.parse(JSON.stringify(this.form));
      params.useAuthorityList = params.useAuthorityList || [];
      params.useAuthorityType = params.useAuthorityType ? 0 : 1;
      // 自动释放
      params.autoOffType = params.autoOffType ? 1 : 2;
      // 二维码刷新
      params.autoRefreshSignInQrCodeType = params.autoRefreshSignInQrCodeType
        ? 1
        : 2;
      if (params.bookingTime && params.bookingTime.length === 2) {
        params.bookingTimeBegin = params.bookingTime[0];
        params.bookingTimeEnd = params.bookingTime[1];
      }
      // 资源图片处理
      if (params.emphasis && params.emphasis.length > 0) {
        params.emphasis = params.emphasis[0].fileUrl;
      } else {
        params.emphasis = '';
      }
      // 位置指引
      if (params.positionPicture && params.positionPicture.length > 0) {
        params.positionPicture = params.positionPicture[0].fileUrl;
      } else {
        params.positionPicture = '';
      }
      // 预定人员
      if (params.useAuthorityList && params.useAuthorityList.length > 0) {
        const useAuthorityNameList = params.useAuthorityNameList.split(',');
        params.useAuthorityList = params.useAuthorityList.map((e, idx) => {
          return {
            useAuthorityType: 2,
            objId: e,
            name: useAuthorityNameList[idx]
          };
        });
      }

      let nameList = params.adminNameList.split(',');
      params.adminList = nameList.map((employeeName, index) => {
        return {
          employeeName,
          employeeNo: params.adminCodeList[index]
        };
      });

      delete params.adminNameList;

      try {
        let title = '';
        switch (this.type) {
          case 'ADD':
            title = '新增成功！';
            await this.ajax.addBoardRoom(params);
            this.$refs.uToast.show({
              title: title
            });
            this.btnLoading = false;
            this.onClickBack();
            break;
          case 'EDIT':
            title = '修改成功！';
            await this.ajax.editBoardRoom(params);
            this.$refs.uToast.show({
              title: title
            });
            this.copyForm = {};
            this.readOnly = true;
            this.isEdit = true;
            this.btnLoading = false;

            this.initLoad();
            this.title = '资源详情';
            break;
          default:
            break;
        }
      } catch (error) {
        this.btnLoading = false;
      } finally {
        this.btnLoading = false;
      }
    },
    // 编辑
    handleEdit() {
      this.title = '资源编辑';

      this.copyForm = JSON.parse(JSON.stringify(this.form));
      this.formList = this.formList.filter(e => {
        return e.prop != 'disableTime';
      });
      this.readOnly = false;
    },
    // 取消编辑
    handleCancel() {
      this.title = '资源详情';
      this.handlleDisbleData(this.form);
      this.form = this.copyForm;
      this.copyForm = {};
      this.readOnly = true;
    },
    // 删除
    handleDelete() {
      this.deleteVisible = true;
    },
    // 禁用
    handleDisable() {
      this.$refs.disableModal.open(this.form, this.isEnable);
    },
    // 禁用回调
    disableModalOk() {
      this.isEdit = true;
      this.initLoad();
    },
    async confirm() {
      try {
        const res = await this.ajax.delBoardRoom({ id: this.meetingRoomId });
        this.onClickBack();
      } catch (error) {
      } finally {
        this.deleteVisible = false;
      }
    },
    switchChange(row) {
      if (row.field === 'useAuthorityType') {
        if (row.value) {
          this.formList = this.formList.filter(e => {
            return e.propVal != 'useAuthorityList';
          });
        } else {
          this.$set(this.form, 'useAuthorityList', '');
          this.$set(this.form, 'useAuthorityNameList', '');
          const idx = this.formList.findIndex(e => {
            return e.prop === 'useAuthorityType';
          });
          this.formList.splice(
            idx + 1,
            0,
            JSON.parse(JSON.stringify(deptConfig))
          );
        }
      }
    }
  }
};
</script>
<style lang="scss" scoped>
@import '@/assets/css/flex.scss';
.meeting_room_manage_details {
  @include vue-flex;
  height: 100%;
  flex-direction: column;
  flex-wrap: wrap;
  background: white;
  .header {
    width: 100%;
  }
  .form_container {
    width: 100%;
    flex: 1;
    overflow-y: auto;
    .scroll_container {
      background: white;
      .slot-btn {
        @include vue-flex;
        flex-direction: column;
        width: 240rpx;
        height: 140rpx;
        background: #fafafa;
        border-radius: 8rpx;
        border: 1px solid #eee;
        display: flex;
        justify-content: center;
        align-items: center;
        .slot-btn_text {
          font-size: 24rpx;
          font-weight: 400;
          color: #bbb;
        }
      }

      .slot-btn__hover {
        background-color: rgb(235, 236, 238);
      }
    }
  }
  .footer {
    width: 100%;
    .footer_button {
      margin: 32rpx 30rpx;
      height: 88rpx;
      background: #005bac;
      border-radius: 8rpx;
      font-size: 36rpx;
      font-weight: 400;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      &.u-default-hover {
        color: #fff !important;
        background-color: #005bac !important;
      }
    }
    .footer_nav {
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: space-around;
      background: #ffffff;
      font-size: 32rpx;
      font-weight: 400;
      color: #e24242;
      margin-top: 16rpx;
      box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
