<template>
  <view class="meeting_room_manage">
    <u-navbar class="header" title="资源维护" :customBack="onClickBack">
      <text @click="handleOpenDetails('ADD')" class="right_slot" slot="right">
        新增资源
      </text>
    </u-navbar>
    <view class="search-box">
      <u-search
        shape="square"
        :show-action="false"
        placeholder="搜索"
        v-model="queryParam.name"
        @search="search"
        @clear="clear"
      ></u-search>
    </view>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <meeting-room-card
          v-for="(boardRoomItem, idx) in boardRoomList"
          :key="idx"
          :dataScource="boardRoomItem"
          @click="handleClick"
        >
        </meeting-room-card>
      </mescroll>
    </view>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import MeetingRoomCard from '@/pages/common/meeting-room-card/index.vue';
export default {
  components: {
    mescroll,
    MeetingRoomCard
  },
  data() {
    return {
      // 搜索条件
      queryParam: {
        name: ''
      },
      boardRoomList: []
    };
  },
  // async onLoad(opt) {
  //   await this.$refs['mescroll'].downCallback();
  // },
  methods: {
    search() {
      this.datasInit();
      this.$refs['mescroll'].downCallback();
    },
    clear() {
      this.datasInit();
      this.$refs['mescroll'].downCallback();
    },
    /**
     * 点击返回页面
     */
    onClickBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    },
    onClickRight() {
      // do something...
    },
    async downCallback() {
      this.datasInit();
      await this.$refs['mescroll'].downCallback();
    },
    // 列表数据
    async getListData(page, successCallback, errorCallback) {
      try {
        let vm = this;
        let boardRoomList = await vm.ajax.getBoardRoomList(vm.queryParam);
        vm.boardRoomList = boardRoomList.object || [];
        successCallback(vm.boardRoomList);
      } catch (e) {
        errorCallback();
        //TODO handle the exception
      }
    },
    datasInit() {
      let vm = this;
      vm.boardRoomList = [];
    },
    setListData(rows) {
      let vm = this;

      vm.boardRoomList = vm.boardRoomList.concat(rows);
    },
    // 新增资源
    handleOpenDetails(type, id) {
      uni.navigateTo({
        url: `/pages/meeting-room-manage/details/index?type=${type}${
          id ? '&meetingRoomId=' + id : ''
        }`
      });
    },
    handleClick(row) {
      this.handleOpenDetails('EDIT', row.id);
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/flex.scss';

.meeting_room_manage {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .header {
    .right_slot {
      padding-right: 30rpx;
      font-size: 28rpx;
      color: #333333;
    }
  }

  .search-box {
    @include vue-flex;
    position: relative;
    background-color: #ffffff;
    padding: $uni-spacing-col-base $uni-spacing-row-lg;
    margin-bottom: $uni-spacing-col-base;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: $uni-spacing-row-lg;
      left: $uni-spacing-row-lg;
      height: 1px;
      background-color: #eeeeee;
    }
  }

  .mescroll-content {
    flex: 1;
    position: relative;
  }
}
</style>
