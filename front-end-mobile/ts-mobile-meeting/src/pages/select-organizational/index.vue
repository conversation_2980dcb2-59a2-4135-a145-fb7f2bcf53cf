<template>
  <view class="select_organizational">
    <u-input v-model="inpuVal" @input="handleInput"></u-input>
    <dept-tree
      ref="trees"
      :lazyLoading="true"
      :data="[{ ...list[0], children: undefined }]"
      :filterMethod="filterMethod"
      @async-source-load="handleDeptLoading"
    ></dept-tree>
  </view>
</template>

<script>
import DeptTree from './dept-tree.vue';
export default {
  components: { DeptTree },
  data() {
    return {
      list: [],
      inpuVal: ''
    };
  },
  onLoad() {
    this.init();
  },
  methods: {
    handleInput(e) {
      this.$nextTick(() => {
        this.$refs.trees.filter('重症');
      });
    },
    async init() {
      try {
        const res = await this.ajax.getDeptList('/organization/getTree');
        this.list = res.object || [];
      } catch (error) {}
    },
    async handleDeptLoading(data, callback) {
      let res = await this.ajax.getPersonByDept({
        data: { eqOrgId: data.id },
        pageNum: 1,
        pageSize: 9999
      });
      let list = [];
      res.rows.forEach(item => {
        list.push({
          name: item.employeeName,
          id: item.employeeId,
          ...item,
          children: []
        });
      });

      let child = (this.findChild(data) || []).map(item => {
        return { ...item, children: [] };
      });
      callback([...child, ...list]);
    },
    findChild(data, node = { children: this.list }) {
      if (node.id == data.id) {
        return node.children || [];
      }

      if (node.children) {
        let result = false;
        node.children.forEach(item => {
          let child = this.findChild(data, item);
          if (child) {
            result = child;
          }
        });
        if (result) {
          return result;
        }
      }
    },

    filterMethod(val, data) {
      if (data.name.indexOf(val) >= 0) {
        return true;
      }
      return false;
    }
  },
  watch: {
    inpuVal(newVal) {}
  }
};
</script>

<style></style>
