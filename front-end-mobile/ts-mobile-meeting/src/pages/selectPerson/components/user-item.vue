<template>
  <view>
    <view class="user-item" @click="clickItem">
      <uni-icons
        class="user-item-check-icon"
        v-if="showChecked"
        :type="checked ? 'checkbox-filled' : 'circle'"
        :color="checked ? '#005BAC' : '#aaa'"
        size="48"
      />
      <image
        class="user-item-icon"
        v-if="headImg ? true : false"
        :src="headImg"
        mode="aspectFill"
      ></image>
      <view
        v-else-if="id != 'all'"
        class="user-item-icon"
        :class="sex == '0' ? 'sex-man' : 'sex-woman'"
      >
        {{ firstName }}
      </view>
      <view class="user-item-info">
        <text class="user-item-name">{{ name }}</text>
        <text class="user-item-description" v-if="deptName || dutyName">
          {{ deptName }}&nbsp;&nbsp;{{ dutyName }}
        </text>
      </view>
    </view>
    <slot></slot>
  </view>
</template>

<script>
export default {
  name: 'UserItem',
  props: {
    showChecked: {
      type: Boolean,
      default: false
    },
    checked: {
      type: Boolean,
      default: false
    },
    disable: {
      type: Boolean,
      default: false
    },
    id: {
      type: [Number, String],
      default: ''
    },
    headImg: {
      type: String,
      default: ''
    },
    sex: {
      type: [Number, String],
      default: ''
    },
    name: {
      type: String,
      default: ''
    },
    deptName: {
      type: String,
      default: ''
    },
    dutyName: {
      type: String,
      default: ''
    }
  },
  computed: {
    firstName() {
      if (!this.name) {
        return '';
      }
      return this.name.substring(this.name.length - 2);
    }
  },
  methods: {
    clickItem() {
      if (this.showChecked && !this.disable) {
        this.$emit('update:checked', !this.checked);
      }
      this.$emit('click');
    }
  }
};
</script>

<style lang="scss" scoped>
.user-item {
  display: flex;
  flex: 1;
  color: #333333;
  align-items: center;
}
.user-item-check-icon {
  margin-right: 20rpx;
  line-height: 1;
}
.user-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.user-item-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  border-radius: 100%;
  color: #ffffff;
  text-align: center;
  line-height: 2.8;
  font-size: 28rpx;
}
.user-item-description {
  color: #666666;
  font-weight: normal;
  font-size: 28rpx;
}
.sex-man {
  background-color: $sexman-color;
}
.sex-woman {
  background-color: $sexwoman-color;
}
</style>
