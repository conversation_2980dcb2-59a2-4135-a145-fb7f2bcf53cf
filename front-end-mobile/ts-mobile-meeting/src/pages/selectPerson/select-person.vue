<template>
  <view class="ts-content">
    <page-head title="人员选择" @clickLeft="returnBack"></page-head>
    <view v-if="approval" class="approval-wrap">
      <mescroll
        ref="approvaMescroll"
        :searchInput="approvalAllList.length > 15"
        @getDatas="getApprovalListData"
        @setDatas="setApprovalListData"
        @datasInit="initApprovalDatas"
      >
        <user-item
          v-if="approvalList.length > 1 && checkType == 'checkBox'"
          class="contact-item"
          :id="approvalAllSelected.id"
          :name="approvalAllSelected.name"
          :showChecked="true"
          :checked.sync="approvalAllSelected.checked"
          style="margin-bottom: 20rpx"
          @click="handleApprovalAllPerson"
        ></user-item>
        <user-item
          class="contact-item"
          v-for="item in approvalList"
          :key="item.id"
          :id="item.id"
          :headImg="item.empHeadImg ? $config.BASE_HOST + item.empHeadImg : ''"
          :sex="item.empSex"
          :name="item.name"
          :deptName="item.empDeptName"
          :dutyName="item.empDutyName"
          :showChecked="true"
          :checked="handleStatus(item.id)"
          @click="clickItem(item)"
        ></user-item>
      </mescroll>
    </view>
    <view v-else class="common-wrap">
      <scroll-view class="swiper-head" :scroll-x="true" :show-scrollbar="false">
        <view
          v-for="(tab, index) in tabBars"
          :key="tab.id"
          class="uni-tab-item"
          :id="tab.id"
          :data-current="index"
          @click="ontabtap"
        >
          <text
            class="uni-tab-item-title"
            :class="tabIndex == index ? 'uni-tab-item-title-active' : ''"
            >{{ tab.name }}</text
          >
        </view>
      </scroll-view>
      <swiper
        :current="tabIndex"
        class="swiper-box"
        :duration="300"
        @change="ontabchange"
      >
        <swiper-item class="swiper-item">
          <mescroll
            ref="mescroll"
            :searchInput="true"
            placeholder="输入姓名、科室搜索"
            @getDatas="getListData"
            @setDatas="setListData"
            @datasInit="datasInit"
          >
            <user-item
              class="contact-item"
              v-for="item in tabBars[0]['list']"
              :key="item.id"
              :headImg="
                item.empHeadImg ? $config.BASE_HOST + item.empHeadImg : ''
              "
              :sex="item.empSex"
              :name="item.name"
              :deptName="item.empDeptName"
              :dutyName="item.empDutyName"
              :showChecked="true"
              :checked="handleStatus(item.id)"
              @click="clickItem(item)"
            ></user-item>
          </mescroll>
        </swiper-item>
        <swiper-item class="swiper-item">
          <view class="group-box">
            <uni-search-bar
              radius="100"
              placeholder="搜索群组"
              searchBgColor="#fff"
              bgColor="#f4f4f4"
              cancelButton="none"
              @confirm="systemGroupSearch"
            />
            <view class="group-list">
              <uni-collapse>
                <uni-collapse-item
                  v-for="row in tabBars[1]['list']"
                  v-show="!row.hide"
                  :title="row.groupName"
                  :key="row.groupId"
                >
                  <template v-slot:subtext>
                    <view
                      v-if="checkType == 'checkBox'"
                      @click.stop="handleGroupAllPerson(row)"
                    >
                      <uni-icons
                        v-if="checkType === 'checkBox'"
                        class="all-person-checkbox"
                        :type="row.checked ? 'checkbox-filled' : 'circle'"
                        :color="row.checked ? '#005BAC' : '#aaa'"
                        size="48"
                      />
                    </view>
                  </template>
                  <user-item
                    class="contact-item"
                    v-for="item in row['employeeList']"
                    :key="item.id"
                    :headImg="
                      item.empHeadImg ? $config.BASE_HOST + item.empHeadImg : ''
                    "
                    :sex="item.empSex"
                    :name="item.name"
                    :deptName="item.empDeptName"
                    :dutyName="item.empDutyName"
                    :showChecked="true"
                    :checked="handleStatus(item.id)"
                    @click="clickItem(item)"
                  ></user-item>
                </uni-collapse-item>
              </uni-collapse>
            </view>
          </view>
        </swiper-item>
        <swiper-item class="swiper-item">
          <!-- <collapse-person ref="collapseChild" :list="tabBars[1]['list']" :checkType="checkType" :selectedArr="selectedPerson" @changeSelect="collapseChangeSelect"></collapse-person> -->
          <view class="group-box">
            <uni-search-bar
              radius="100"
              placeholder="搜索群组"
              searchBgColor="#fff"
              bgColor="#f4f4f4"
              cancelButton="none"
              @confirm="systemGroupSearch"
            />
            <view class="group-list">
              <uni-collapse>
                <uni-collapse-item
                  v-for="row in tabBars[2]['list']"
                  v-show="!row.hide"
                  :title="row.groupName"
                  :key="row.groupId"
                >
                  <template v-slot:subtext>
                    <view
                      v-if="checkType == 'checkBox'"
                      @click.stop="handleGroupAllPerson(row)"
                    >
                      <uni-icons
                        v-if="checkType === 'checkBox'"
                        class="all-person-checkbox"
                        :type="row.checked ? 'checkbox-filled' : 'circle'"
                        :color="row.checked ? '#005BAC' : '#aaa'"
                        size="48"
                      />
                    </view>
                  </template>
                  <user-item
                    class="contact-item"
                    v-for="item in row['employeeList']"
                    :key="item.id"
                    :headImg="
                      item.empHeadImg ? $config.BASE_HOST + item.empHeadImg : ''
                    "
                    :sex="item.empSex"
                    :name="item.name"
                    :deptName="item.empDeptName"
                    :dutyName="item.empDutyName"
                    :showChecked="true"
                    :checked="handleStatus(item.id)"
                    @click="clickItem(item)"
                  ></user-item>
                </uni-collapse-item>
              </uni-collapse>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
    <view class="button-groups">
      <button
        class="button-item selected"
        type="default"
        :disabled="isDisabled"
        @click="togglePopup('allBottom', 'popup')"
      >
        已选 {{ selectNum ? selectNum : '' }}
      </button>
      <button class="button-item" type="primary" @click="confirmBtn">
        确定
      </button>
    </view>
    <uni-popup ref="showpopup" :type="popupType">
      <page-head
        :title="`已选择(${selectNum})`"
        :isleft="false"
        left-text="清空"
        right-text="收起"
        @clickLeft="emptySelectedPerson"
        @clickRight="popupConfirm"
      ></page-head>
      <view v-if="popupSearchInput" class="search">
        <uni-search-bar
          radius="100"
          placeholder="搜索"
          searchBgColor="#eeeeee"
          borderColor="transparent"
          bgColor="#FFFFFF"
          cancelButton="none"
          @confirm="popupSearch"
        />
      </view>
      <view class="popup-content">
        <!-- 数据列表 -->
        <view class="contact-list" v-if="resource.length > 0">
          <user-item
            class="contact-item"
            v-for="(item, index) in resource"
            :key="item.id"
            :headImg="
              item.empHeadImg ? $config.BASE_HOST + item.empHeadImg : ''
            "
            :sex="item.empSex"
            :name="item.name"
            :deptName="item.empDeptName"
            :dutyName="item.empDutyName"
          >
            <uni-icons
              @tap="deletItem(item, index)"
              type="close"
              color="#aaa"
              size="48"
            />
          </user-item>
        </view>
        <view
          v-else-if="resource.length == 0 && selectedPerson.length == 0"
          class="nothing"
          >暂无人员</view
        >
        <view v-else class="nothing">没有找到相关人员</view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
//导入vuex的mapState值和mapMutations方法
import { mapState } from 'vuex';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import uniSearchBar from '@/components/uni-search-bar/uni-search-bar.vue';
import uniCollapse from '@/components/uni-collapse/uni-collapse.vue';
import uniCollapseItem from '@/components/uni-collapse-item/uni-collapse-item.vue';
import userItem from './components/user-item.vue';
import { apiPerson } from './apiPerson.js';
export default {
  components: {
    mescroll,
    uniPopup,
    uniSearchBar,
    uniCollapse,
    uniCollapseItem,
    userItem
  },
  data() {
    return {
      approvalAllList: [],
      approvalList: [], //分页显示的人员
      tabIndex: 0, //当前选中的tab索引值，从0计数
      tabBars: [
        {
          name: '全部人员',
          id: 'all',
          list: []
        },
        {
          name: '系统群组',
          id: 'system',
          list: []
        },
        {
          name: '个人群组',
          id: 'group',
          list: []
        }
      ],
      selectedPerson: [], //已选人员数组
      selectNum: null, //已选数量
      isDisabled: true, //按钮是否禁用
      popupType: '', //弹出层类型
      popupSearchInput: true, //弹出层是否显示搜索框
      resource: [], //搜索数据
      checkType: '',
      approvalAllSelected: {
        checked: false,
        name: '全部',
        id: 'all'
      },
      approval: false
    };
  },
  computed: {
    ...mapState(['empid']),
    defaultSort() {
      let sortDatas = this.$store.state.common?.personalSortData ?? {},
        { sidx = 'create_date', sord = 'desc' } = sortDatas;
      return { sord, sidx };
    }
  },
  watch: {
    selectedPerson(newVal) {
      if (newVal.length) {
        this.isDisabled = false;
        this.selectNum = newVal.length;
      } else {
        this.selectNum = 0;
        this.isDisabled = true;
      }
      this.handleGroupStatus(1);
      this.handleGroupStatus(2);
    }
  },
  onLoad(opt) {
    let person_list = uni.getStorageSync('person_list');
    if (person_list != '[]') {
      this.selectedPerson = JSON.parse(person_list);
      this.selectNum = this.selectedPerson.length;
    }
    this.checkType = opt.checkType;
    if (opt.approval != undefined && eval(opt.approval)) {
      this.approval = true;
      let list = JSON.parse(uni.getStorageSync('approval_person_list'));

      this.approvalAllList = list.filter(item => {
        return item.name;
      });
    }
  },
  methods: {
    handleStatus(id) {
      return this.selectedPerson.some(item => {
        return item.id == id;
      });
    },
    handleGroupStatus(index) {
      this.tabBars[index]['list'].forEach(listItem => {
        let employeeList = listItem.employeeList;
        let unSelectedIndex = employeeList.findIndex(item => {
          let checked = this.selectedPerson.some(one => {
            return one.id === item.id;
          });
          return !checked;
        });
        //当群组中存在未被选中的人员时取消群组被全选的状态
        if (unSelectedIndex != -1) listItem.checked = false;
      });
    },
    //tab点解切换
    ontabtap(e) {
      let index = e.target.dataset.current || e.currentTarget.dataset.current;
      this.switchTab(Number(index));
    },
    //tab滑动切换
    ontabchange(e) {
      let index = e.target.current || e.detail.current;
      this.switchTab(Number(index));
    },
    switchTab(index) {
      if (this.tabBars[index]['list'].length === 0 && this.tabIndex != index) {
        this.getList(index);
      }
      if (this.tabIndex === index) {
        return;
      }
      this.tabIndex = index;
    },
    getList(index) {
      switch (index) {
        case 1:
          this.handleGetSystemList();
          break;
        case 2:
          this.getOrggroup_list();
          break;
      }
    },
    getApprovalListData(page, successCallback, errorCallback, keywords) {
      apiPerson(this.approvalAllList, page.num, page.size, keywords)
        .then(res => {
          let rows = res.rows;
          successCallback(rows);
        })
        .catch(e => {
          errorCallback();
        });
    },
    setApprovalListData(rows) {
      this.approvalList = this.approvalList.concat(rows);
      if (this.approvalAllSelected.checked) {
        // rows.forEach(item => {
        //   let index = this.selectedPerson.findIndex(one => {
        //     return one.id == item.id;
        //   });
        //   if (index == -1) {
        //     this.selectedPerson.push(item);
        //   }
        // });
        this.selectedPerson = [].concat(this.approvalList);
      }
    },
    initApprovalDatas() {
      this.approvalList = [];
    },
    //获取全部人员分页回调
    getListData(page, successCallback, errorCallback, keywords) {
      this.ajax
        .getEmployeeList({
          pageSize: page.size,
          pageNo: page.num,
          searchKey: keywords,
          ...this.defaultSort
        })
        .then(res => {
          let rows = res.rows;
          successCallback(rows);
        })
        .catch(e => {
          errorCallback();
        });
    },
    setListData(rows) {
      rows.forEach(item => {
        this.tabBars[0]['list'].push(this.formatListUser(item));
      });
    },
    datasInit() {
      this.tabBars[0]['list'] = [];
    },
    formatListUser(item) {
      return {
        id: item.empCode,
        name: item.empName,
        empHeadImg: item.empHeadImg,
        empDeptName: item.empDeptName,
        empDutyName: item.empDutyName,
        empSex: item.empSex
      };
    },
    //获取系统群组
    handleGetSystemList() {
      this.ajax
        .getOrgGroupList({
          pageSize: 20000,
          pageNo: 1,
          groupType: 0,
          empName: this.empid
        })
        .then(res => {
          this.setGroupDatas(res, 1);
        });
    },
    //获取个人数据
    getOrggroup_list() {
      this.ajax
        .getOrgGroupList({
          pageSize: 20000,
          pageNo: 1,
          groupType: 1,
          empName: this.empid
        })
        .then(res => {
          this.setGroupDatas(res, 2);
        });
    },
    setGroupDatas(datas, index) {
      datas.forEach(listItem => {
        let employeeList = listItem.employeeList;
        let groupPerson = [];
        employeeList.forEach(item => {
          groupPerson.push(this.formatListUser(item));
        });
        this.tabBars[index]['list'].push({
          groupId: listItem.groupId,
          groupName: listItem.groupName,
          checked: false,
          employeeList: groupPerson
        });
      });
    },
    //选择事件
    clickItem(item) {
      if (this.checkType == 'radio') {
        this.selectedPerson = [item];
      } else if (this.checkType == 'checkBox') {
        let index = this.selectedPerson.findIndex(one => {
          return one.id == item.id;
        });
        if (index != -1) {
          this.selectedPerson.splice(index, 1);
        } else {
          this.selectedPerson.push(item);
        }
      }
    },
    handleGroupAllPerson(data) {
      data.checked = !data.checked;
      data.employeeList.forEach(item => {
        if (data.checked) {
          // 如果全选群组 但已有人员选中 则不进入选中列表
          let index = this.selectedPerson.findIndex(select => {
            return select.id == item.id;
          });
          if (index == -1) this.selectedPerson.push(item);
        } else {
          this.selectedPerson = this.selectedPerson.filter(
            i => i.id != item.id
          );
        }
      });
    },
    handleApprovalAllPerson() {
      if (this.approvalAllSelected.checked) {
        this.selectedPerson = [].concat(this.approvalList);
      } else {
        this.selectedPerson = [];
      }
    },
    //确定按钮点击事件
    confirmBtn() {
      uni.$emit('personlist', this.selectedPerson);
      uni.navigateBack({
        delta: 1
      });
    },
    //弹出层切换
    togglePopup(type, open) {
      this.popupType = type;
      this.$nextTick(() => {
        this.resource = JSON.parse(JSON.stringify(this.selectedPerson));
        this.$refs[`show${open}`].open();
      });
    },
    //删除已选人员
    deletItem(item, i) {
      this.resource.splice(i, 1);
      let index = this.selectedPerson.findIndex(one => {
        return one.id == item.id;
      });
      this.selectedPerson.splice(index, 1);
    },
    systemGroupSearch(res) {
      this.tabBars[this.tabIndex]['list'].forEach(listItem => {
        listItem.hide = listItem.groupName.indexOf(res.value) === -1;
      });
      this.$forceUpdate();
    },
    //弹出层搜索事件
    popupSearch(res) {
      let temp = [];
      this.selectedPerson.forEach(e => {
        if (e.name.indexOf(res.value) > -1) temp.push(e);
      });
      this.resource = temp;
    },
    //弹出层确定按钮点击事件
    popupConfirm() {
      this.$refs['showpopup'].close();
    },
    //清空已选人员
    emptySelectedPerson() {
      this.approvalAllSelected.checked = false;
      this.resource = [];
      this.selectedPerson = [];
    },
    //返回按钮点击事件
    returnBack() {
      uni.removeStorageSync('person_list');
      uni.$off('personlist');
      this.$nextTick(() => {
        uni.navigateBack();
      });
    }
  }
};
</script>
<style lang="scss" scoped>
/* #ifndef APP-PLUS */
page {
  width: 100%;
  min-height: 100%;
  display: flex;
}
/* #endif */
.ts-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  width: 100%;
  height: 100%;
  /* #ifdef MP-ALIPAY || MP-BAIDU */
  height: 100vh;
  /* #endif */
  .approval-wrap {
    position: relative;
    height: 100%;
  }
  .common-wrap {
    display: flex;
    flex: 1;
    flex-direction: column;
    .swiper-head {
      position: relative;
      width: 750rpx;
      height: 80rpx;
      background-color: #ffffff;
      flex-direction: row;
      box-sizing: border-box;
      /* #ifndef APP-PLUS */
      white-space: nowrap;
      /* #endif */
      /* flex-wrap: nowrap; */
      /* border-color: #cccccc;
				border-bottom-style: solid;
				border-bottom-width: 1px; */
      &::before,
      &::after {
        position: absolute;
        z-index: 10;
        right: 0;
        left: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #eeeeee;
      }
      &::before {
        top: 0;
      }
      &::after {
        bottom: 0;
      }
      /deep/ .uni-scroll-view-content {
        display: flex;
        justify-content: space-between;
      }
      .uni-tab-item {
        /* #ifndef APP-PLUS */
        display: inline-block;
        /* #endif */
        flex-wrap: nowrap;
        flex: 1;
        padding-left: 34rpx;
        padding-right: 34rpx;
        margin: 0 2%;
        box-sizing: border-box;
        text-align: center;
        .uni-tab-item-title {
          color: #555;
          font-size: 30rpx;
          height: 80rpx;
          line-height: 76rpx;
          flex-wrap: nowrap;
          display: block;
          box-sizing: border-box;
          /* #ifndef APP-PLUS */
          white-space: nowrap;
          /* #endif */
        }

        .uni-tab-item-title-active {
          color: $theme-color;
          border-bottom: 2px solid $theme-color;
        }
      }
    }
    .swiper-box {
      flex: 1;
      .swiper-item {
        flex: 1;
        flex-direction: row;
        .group-box {
          height: 100%;
          width: 100%;
          display: flex;
          flex-direction: column;
        }
        .group-list {
          flex: 1;
          overflow: auto;
          /deep/ .uni-collapse {
            position: relative;
            .all-person-checkbox {
              position: absolute;
              left: 10px;
              top: 10px;
            }
          }
          /deep/ .uni-collapse-cell__title {
            padding-left: 50px !important;
          }
        }
      }
    }
  }

  .button-groups {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    background-color: #ffffff;
    box-shadow: 0 1px 6px #cccccc;
    z-index: 10;
    .button-item {
      font-size: 28rpx;
      margin: 0 20rpx;
      width: 200rpx;
      &:first-child::after {
        border: 1px solid #005bac;
      }
      &::after {
        width: 100%;
        height: 100%;
        transform: none;
        border-radius: 10rpx;
      }
    }
    .button-item.selected {
      color: #005bac;
      background-color: #ffffff;
    }
  }
  .popup-content {
    position: absolute;
    top: 96px;
    bottom: 0;
    left: 0;
    right: 0;
  }
  .nothing {
    color: #999999;
    text-align: center;
  }
  .contact-list {
    height: 100%;
    overflow: scroll;
  }
  .contact-item {
    padding: 20rpx;
    display: flex;
    align-items: center;
    background-color: #ffffff;
  }
}
</style>
