## 0.2.5（2021-09-09）
* 修复 wxs使用了es6语法导致编译到微信小程序出错 感谢 @小小贝 反馈
## 0.2.4（2021-09-01）
* 修复 iOS在整行拖拽情况下，触感反馈与点击事件冲突的问题 感谢 @粲然 反馈
## 0.2.3（2021-08-09）
* 修复 修改list导致拖拽报错
## 0.2.2（2021-07-06）
更新confirm的bug问题，这是我手贱写出的bug。
## 0.2.1（2021-07-02）
* 修复 数据中传入id导致不触发回调事件的问题 感谢@layu反馈  
* 优化 拖拽和位置交换动画使用translate3d 感谢@pwiz反馈
## 0.2.0（2021-06-23）
* 修复 页面滚动后拖拽位置不正确问题
* 修复 页面使用多个组件时，组件间互相影响问题
* 修复 微信小程序设置列表高度不生效的问题
## 0.1.2（2021-02-02）
* 修复moveRow取值错误问题 感谢@诗人的咸鱼 反馈
## 0.1.1（2021-02-02）
* 增加开关触感反馈参数feedbackGeneratorState
* 发布uni_modules版本(需HX3.1.0以上)
