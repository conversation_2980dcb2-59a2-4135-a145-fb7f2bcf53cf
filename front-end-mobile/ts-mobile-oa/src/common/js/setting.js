let BASE_HOST = '',
  LOGIN_BASE_HOST = '',
  DOCUMENT_BASE_HOST = 'http://127.0.0.1:9004',
  ENABLE_FILE_PREVIEW = true,
  ENABLE_ACCOUNT_LOGIN = true,
  ENABLE_IMPROVE_PERSON_INFO = false,
  ENABLE_SMS_VERICATION = true;
if (process.env.NODE_ENV === 'development') {
  // 开发环境
  // BASE_HOST = 'http://newoa.liuyang120.com:9000';
  // LOGIN_BASE_HOST = 'http://newoa.liuyang120.com:9000';
  // BASE_HOST = 'http://*************:9088';
  // LOGIN_BASE_HOST = 'http://*************:9088';
  BASE_HOST = window.location.origin;
  LOGIN_BASE_HOST = window.location.origin;
} else if (process.env.NODE_ENV === 'production') {
  // 生产环境
  BASE_HOST = window.location.origin;
  LOGIN_BASE_HOST = window.location.origin;
  ENABLE_ACCOUNT_LOGIN = false;
}
export default {
  BASE_HOST,
  LOGIN_BASE_HOST, //登录请求路径
  DOCUMENT_BASE_HOST, //文档预览路径
  ENABLE_IMPROVE_PERSON_INFO, //是否强制维护个人信息
  ENABLE_FILE_PREVIEW, //文件是否预览
  ENABLE_ACCOUNT_LOGIN, //是否需要账号登录
  ENABLE_SMS_VERICATION, //是否开启短信验证
  HIS: [
    {
      url: '/ts-external/emrApi/getInpatientInfoById',
      method: 'POST',
      multiselect: false //是否多选
    },
    {
      url: '/ts-external/emrApi/getMedicalRecord',
      method: 'POST',
      multiselect: true //是否多选
    }
  ]
};
