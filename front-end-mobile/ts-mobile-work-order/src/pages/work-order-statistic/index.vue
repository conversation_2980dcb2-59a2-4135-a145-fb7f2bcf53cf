<template>
  <view class="ts-container safe-area-inset-bottom">
    <u-navbar title="工单统计" title-bold :custom-back="goBack"></u-navbar>
    <view class="date-box">
      <view class="date-quick-button-box">
        <view
          class="date-quick-button-item"
          v-for="(item, index) in dateQuickButtons"
          :key="index"
          :class="
            item.start == selectedDate.beginTime &&
            item.end == selectedDate.endTime
              ? 'date-quick-active'
              : ''
          "
          @click="dateQuickClick(item)"
        >
          {{ item.name }}
        </view>
      </view>
      <view class="date-picker-box">
        <view class="date-picker-title">
          统计时间
        </view>
        <view class="date-picker">
          <view class="date-picker-item" @click="clickDatePicker('beginTime')">
            <u-input
              placeholder="开始时间"
              type="select"
              v-model="selectedDate.beginTime"
              :show-right-icon="false"
              @click="clickDatePicker('beginTime')"
            />
            <u-icon
              name="riqi"
              custom-prefix="work-icon"
              color="#E0E6F0"
            ></u-icon>
          </view>
          <view class="date-picker-connection">
            -
          </view>
          <view class="date-picker-item" @click="clickDatePicker('endTime')">
            <u-input
              placeholder="结束时间"
              type="select"
              v-model="selectedDate.endTime"
              :show-right-icon="false"
              @click="clickDatePicker('endTime')"
            />
            <u-icon
              name="riqi"
              custom-prefix="work-icon"
              color="#E0E6F0"
            ></u-icon>
          </view>
          <u-button
            class="date-picker-search-button"
            type="primary"
            size="mini"
            @click="clickSearch"
          >
            搜索
          </u-button>
        </view>
      </view>
    </view>
    <view class="statistic-box">
      <u-cell-group :border="false">
        <u-cell-item
          icon="qingkuang"
          icon-custom-prefix="work-icon"
          :icon-style="{ color: '#f2c258' }"
          :title="`工单情况(共${workOrderTotalCount}个工单)`"
          :title-style="{
            fontSize: '32rpx',
            fontWeight: 'bold',
            color: '#333333'
          }"
          :border-bottom="false"
          :arrow="false"
        >
        </u-cell-item>
      </u-cell-group>
      <view class="">
        <u-grid :col="4" :border="false">
          <u-grid-item
            v-for="(item, index) in statistic"
            :index="index"
            :key="index"
            :custom-style="{ padding: '0 0 20rpx' }"
          >
            <text class="grid-value" :style="{ color: item.color }">{{
              item.value
            }}</text>
            <text class="grid-label">{{ item.name }}</text>
          </u-grid-item>
        </u-grid>
      </view>
    </view>
    <view class="statistic-box">
      <u-cell-group :border="false">
        <u-cell-item
          icon="qingkuang"
          icon-custom-prefix="work-icon"
          :icon-style="{ color: '#f2c258' }"
          :title-style="{
            fontSize: '32rpx',
            fontWeight: 'bold',
            color: '#333333'
          }"
          :border-bottom="false"
          :arrow="false"
        >
          <template slot="label">
            <div class="title-person">
              人员接单情况<span>(可滑动查看)</span>
            </div>
          </template>
        </u-cell-item>
      </u-cell-group>
      <view class="work-order-list-box">
        <mescroll ref="mescroll" :upUse="false">
          <!-- <view class="list">
            <view class="item_comtent">
              <view class="content header fixed">姓名</view>
              <view class="content header fixedSecond">单位/科室</view>
              <view class="content header">合计</view>
              <view class="content header">待接单</view>
              <view class="content header">处理中</view>
              <view class="content header">待验收</view>
              <view class="content header">待评价</view>
              <view class="content header">已完结</view>
              <view class="content header">已暂停</view>
              <view class="content header">已终止</view>
            </view>
            <view v-for="item in list" :key="item.id" class="item_comtent">
              <view class="content fixed">{{ item.fk_user_name }}</view>
              <view class="content fixedSecond">{{ item.fk_user_dept_name }}</view>
              <view class="content">{{ item.sum }}</view>
              <view class="content">{{ item.waiting }}</view>
              <view class="content">{{ item.processing }}</view>
              <view class="content">{{ item.acceptance }}</view>
              <view class="content">{{ item.evaluate }}</view>
              <view class="content">{{ item.completed }}</view>
              <view class="content">{{ item.suspended }}</view>
              <view class="content">{{ item.terminat }}</view>
            </view>
          </view> -->
          <fixed-table-dept
            :data="tableData"
            :header="columns"
            :fixed="true"
            :fixedFirstAndSecond="true"
            :border="true"
            :stripe="true"
          />
        </mescroll>
      </view>
    </view>
    <view
      class="statistic-box"
      v-for="(item, index) in echartsList"
      :key="index"
    >
      <u-cell-group :border="false">
        <u-cell-item
          icon="bingzhuangtu"
          icon-custom-prefix="work-icon"
          :icon-style="{ color: '#f2c258' }"
          :title="item.name"
          :title-style="{
            fontSize: '32rpx',
            fontWeight: 'bold',
            color: '#333333'
          }"
          :arrow="false"
        ></u-cell-item>
      </u-cell-group>
      <view class="charts-box" v-if="JSON.stringify(item.chartsData) != '{}'">
        <qiun-data-charts
          type="ring"
          :chartData="item.chartsData"
          :loadingType="1"
          :disableScroll="false"
          :canvasId="item.type"
          directory="/ts-mobile-work-oder/"
          :canvas2d="true"
          background="none"
          :animation="true"
          :ontouch="true"
          :opts="uChartsOpts"
        />
      </view>
    </view>
    <u-picker
      mode="time"
      v-model="showPicker"
      :default-time="pickerVal"
      :params="pickerParams"
      @confirm="pickerConfirm"
    ></u-picker>
  </view>
</template>

<script>
import index from './index.js';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import FixedTableDept from './fixed-table-dept.vue';
export default {
  name: 'work-order-suspend',
  mixins: [index],
  components: { FixedTableDept, mescroll }
};
</script>

<style lang="scss">
@import '../../assets/css/flex.scss';
.date-box {
  background-color: #ffffff;
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
}
.date-quick-button-box {
  border-radius: $uni-border-radius-base;
  border: 1px solid #e0e6f0;
  @include vue-flex;
}
.date-quick-button-item {
  flex: 1;
  text-align: center;
  font-size: $uni-font-size-base;
  color: $uni-text-color;
  position: relative;
  &::after {
    content: '';
    position: absolute;
    right: 0;
    top: 12rpx;
    bottom: 12rpx;
    width: 1px;
    background-color: #e0e6f0;
  }
  &:last-child {
    &::after {
      width: 0;
    }
  }
}
.date-quick-active {
  color: $uni-color-primary;
  font-weight: bold;
}
.date-picker-title {
  color: $uni-text-content-color;
  font-size: $uni-font-size-base;
}
.date-picker,
.date-picker-item {
  @include vue-flex;
  flex: 1;
  align-items: center;
}
.date-picker-item {
  padding: 0 $uni-spacing-row-base;
  border: 1px solid #e0e6f0;
  border-radius: $uni-border-radius-base;
}
.date-picker-search-button {
  margin-left: $uni-spacing-row-base;
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
  height: 60rpx;
  line-height: 60rpx;
}
.statistic-box {
  margin-top: $uni-spacing-col-base;
}
.grid-value {
  font-size: 36rpx;
  line-height: 1;
  font-weight: bold;
}
.grid-label {
  font-size: 28rpx;
  color: #333333;
}
.charts-box {
  background-color: #ffffff;
  height: 300px;
  width: 100%;
}
.work-order-list-box {
  position: relative;
  min-height: 200px;
  max-height: 200px;
  width: 100%;
  background: #fff;
  /deep/ .list {
    overflow-x: auto;
    .header {
      background: #ccc;
      font-weight: 600;
    }
  }
  /deep/ .item_comtent {
    display: flex;
    .content {
      padding: 0 5px;
      font-size: 14px;
      line-height: 25px;
      min-width: 75px;
      text-align: center;
      border: 0.5px solid #ccc;
      &.fixed {
        position: sticky;
        left: 0px;
      }
      &.fixedSecond {
        position: sticky;
        left: 75px;
      }
    }
  }
}
.title-person {
  font-size: 32rpx;
  font-weight: 'bold';
  color: #333333;
  span {
    font-size: 16rpx;
    color: #ccc;
  }
}
</style>
