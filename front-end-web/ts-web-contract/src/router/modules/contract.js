export default [
  {
    path: '/contractCreation',
    component: resolve =>
      require([`@/views/contractCreation/index.vue`], resolve),
    styleName: '',
    name: '合同起草'
  },
  {
    path: '/contractFormDesign',
    component: resolve =>
      require([`@/views/contractFormDesign/index.vue`], resolve),
    styleName: '',
    name: '合同表单设计'
  },
  {
    path: '/counterpartSetting',
    component: resolve =>
      require([
        `@/views/contractSetting/counterpartSetting/index.vue`
      ], resolve),
    styleName: '',
    name: '相对方设置'
  },
  {
    path: '/templateManagement',
    component: resolve =>
      require([
        `@/views/contractSetting/templateManagement/index.vue`
      ], resolve),
    styleName: '',
    name: '模版管理'
  },
  {
    path: '/typeManagement',
    component: resolve =>
      require([`@/views/contractSetting/typeManagement/index.vue`], resolve),
    styleName: '',
    name: '分类管理'
  },
  {
    path: '/basicsManagement',
    component: resolve =>
      require([`@/views/contractSetting/basicsManagement/index.vue`], resolve),
    styleName: '',
    name: '基础管理'
  },
  {
    path: '/contractApproval',
    component: resolve =>
      require([`@/views/processApproval/contractApproval/index.vue`], resolve),
    styleName: '',
    name: '基础管理'
  },
  {
    path: '/contractManagement',
    component: resolve =>
      require([
        `@/views/contractPerformance/contractManagement/index.vue`
      ], resolve),
    styleName: '',
    name: '合同管理'
  },
  {
    path: '/contractChange',
    component: resolve =>
      require([
        `@/views/contractPerformance/contractChange/index.vue`
      ], resolve),
    styleName: '',
    name: '合同变更'
  },
  {
    path: '/controlOverInvoices',
    component: resolve =>
      require([
        `@/views/contractPerformance/controlOverInvoices/index.vue`
      ], resolve),
    styleName: '',
    name: '发票管理'
  },
  {
    path: '/collectionPaymentManagement',
    component: resolve =>
      require([
        `@/views/contractPerformance/collectionPaymentManagement/index.vue`
      ], resolve),
    styleName: '',
    name: '收付款管理'
  },
  {
    path: '/fundManagement/enterFunds',
    component: resolve =>
      require([`@/views/fundManagement/enterFunds/index.vue`], resolve),
    styleName: '',
    name: '录入经费'
  },
  {
    path: '/fundManagement/usingFunds',
    component: resolve =>
      require([`@/views/fundManagement/usingFunds/index.vue`], resolve),
    styleName: '',
    name: '使用经费'
  },
  {
    path: '/fundManagement/fundsTypeSetting',
    component: resolve =>
      require([`@/views/fundManagement/fundsTypeSetting/index.vue`], resolve),
    styleName: '',
    name: '经费类型维护'
  },
  {
    path: '/fundManagement/applicationSupportingFunds',
    component: resolve =>
      require([
        `@/views/fundManagement/applicationSupportingFunds/index.vue`
      ], resolve),
    styleName: '',
    name: '申请配套经费'
  },
  {
    path: '/fundManagement/fundsStatistic',
    component: resolve =>
      require([`@/views/fundManagement/fundsStatistic/index.vue`], resolve),
    styleName: '',
    name: '经费统计'
  },
  {
    path: '/fundManagement/reportFunds',
    component: resolve =>
      require([`@/views/fundManagement/reportFunds/index.vue`], resolve),
    styleName: '',
    name: '项目上报'
  }
];
