import { request } from '@/api/ajax';

//获取常用语
const getMyOfficaldiction = function() {
  return request({
    url: '/ts-oa/employee/officaldiction/getMyOfficaldiction',
    method: 'get'
  });
};

// 删除流程
const deleteWorkflow = function(params) {
  return request({
    url: '/ts-form/form/api/deleteById',
    method: 'post',
    params
  });
};

//批量删除退回流程
const batchDeleteWorkflow = function(data) {
  return request({
    url: `/ts-device/api/procureApply/batchDelete`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 获取当前节点表单字段权限
const getFiledPermissions = function(data) {
  return request({
    url: `/ts-workflow/workflow/form/filedPermissions`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 根据流程定义id获取下一节点信息
const getNextWfStepListByWfDefId = function(data) {
  return request({
    url: `/ts-workflow/workflow/task/getNextWfStepListByWfDefId`,
    method: 'post',
    data
  });
};

// 根据流程任务id获取下一节点信息
const getNextWfStepListByTaskId = function(data) {
  return request({
    url: `/ts-workflow/workflow/task/getNextWfStepListByTaskId`,
    method: 'post',
    data
  });
};

// 获取历史节点信息
const getHisTaskNodeList = function(data) {
  return request({
    url: `/ts-workflow/workflow/taskHis/getHisTaskNodeList`,
    method: 'post',
    data
  });
};

// 批量同意提交
const batchExamination = function(data) {
  return request({
    url: `/ts-device/api/procureApply/batchExamination`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 批量退回提交
const batchTravelReject = function(data) {
  return request({
    url: `/ts-device/api/procureApply/batchTravelReject`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 上会事项表批量同意提交
const shBatchExamination = function(data) {
  return request({
    url: `/ts-device/api/procureApply/shBatchExamination`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

//强制结束流程
const forceEndWorkflow = function(data) {
  return request({
    url: `/ts-workflow/workflow/instance/doTerminateProcessInstance`,
    method: 'post',
    data
  });
};

//销毁流程
const destroyWorkflow = function(data) {
  return request({
    url: `/ts-workflow/workflow/instance/destructionProcessInstance`,
    method: 'post',
    data
  });
};

//获取流程节点信息列表
const getWfStepListByDefinitionId = function(definitionId) {
  return request({
    url: `/ts-device/api/procureApply/getStepInfoByWfDefinitionId`,
    method: 'post',
    data: {
      wfDefinitionId: definitionId
    }
  });
};

//获取节点子表单权限
const getChildFormFieldListByStepId = function(data) {
  return request({
    url: `/ts-workflow/workflow/form/selectChildFormFieldList`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

export {
  getMyOfficaldiction,
  deleteWorkflow,
  batchDeleteWorkflow,
  getFiledPermissions,
  getNextWfStepListByWfDefId,
  getNextWfStepListByTaskId,
  batchExamination,
  batchTravelReject,
  shBatchExamination,
  forceEndWorkflow,
  destroyWorkflow,
  getWfStepListByDefinitionId,
  getChildFormFieldListByStepId,
  getHisTaskNodeList
};
export default {
  getMyOfficaldiction,
  deleteWorkflow,
  batchDeleteWorkflow,
  getFiledPermissions,
  getNextWfStepListByWfDefId,
  getNextWfStepListByTaskId,
  batchExamination,
  batchTravelReject,
  shBatchExamination,
  forceEndWorkflow,
  destroyWorkflow,
  getWfStepListByDefinitionId,
  getChildFormFieldListByStepId,
  getHisTaskNodeList
};
