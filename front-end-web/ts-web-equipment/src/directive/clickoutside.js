// clickoutside.js
export default {
  bind(el, binding, vnode) {
    function documentClick(e) {
      if (!vnode.context.$refs[binding.expression]) return;
      if (!el.contains(e.target) && el !== e.target) {
        binding.value(e);
      }
    }
    el.__clickoutside__ = documentClick;
    document.addEventListener('click', documentClick);
  },
  unbind(el) {
    document.removeEventListener('click', el.__clickoutside__);
    delete el.__clickoutside__;
  }
};
