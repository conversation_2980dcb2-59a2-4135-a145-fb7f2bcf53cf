<template>
  <vxe-modal
    className="dialog-batch-set-install-acceptance-info"
    width="460px"
    height="520px"
    title="批量录入信息"
    v-model="visible"
    showFooter
    :before-hide-method="close"
  >
    <template #default>
      <ts-form ref="form" :model="form" label-width="140px">
        <ts-form-item
          label="是否覆盖已有数据"
          prop="fill"
          :rules="rules.required"
        >
          <ts-switch v-model="form.fill" inactive-value="0" active-value="1" />
        </ts-form-item>

        <ts-form-item label="生产日期">
          <ts-date-picker
            v-model="form.birthDate"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </ts-form-item>

        <ts-form-item label="启用日期" prop="useDate" :rules="rules.required">
          <ts-date-picker
            v-model="form.useDate"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </ts-form-item>

        <!-- 周期列 -->
        <ts-form-item
          v-for="item in cycleColumns"
          :key="item.prop"
          class="flex-item"
          :label="item.label"
          :prop="`${item.prop}`"
          :rules="item.requiredIcon ? rules.required : undefined"
        >
          <template v-if="item.render === 'switch'">
            <vxe-switch
              :key="item.prop"
              v-model="form[item.prop]"
              open-value="1"
              close-value="2"
            />
          </template>
          <template v-else>
            <ts-input
              placeholder="请输入"
              v-model="form[item.prop]"
              maxlength="2"
              @input="
                value => (form[item.prop] = (value.match(/\d+/g) || [''])[0])
              "
              v-enter-next-input
            />
            <ts-select
              class="cycle-time-unit-select"
              v-model="form[`${item.prop.replace('Val', 'Unit')}`]"
              :options="ymdList"
              placeholder="请选择"
            >
              <el-option
                v-for="item in ymdList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </ts-select>
          </template>
        </ts-form-item>
      </ts-form>
    </template>

    <template #footer>
      <ts-button type="primary" :loading="submitLoading" @click="handleSubmit">
        确 定
      </ts-button>
      <ts-button :disabled="submitLoading" class="shallowButton" @click="close">
        关 闭
      </ts-button>
    </template>
  </vxe-modal>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import enterNextInput from '@/directive/enterNextInput';
export default {
  directives: {
    enterNextInput
  },
  props: {
    ymdList: {
      type: Array,
      default: () => []
    },
    cycleColumns: {
      type: Array,
      default: () => [],
      required: true
    }
  },
  data() {
    return {
      visible: false,
      submitLoading: false,
      deviceIds: [],
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    show({ deviceIds }) {
      this.deviceIds = deviceIds;
      this.$set(this, 'form', {
        fill: '1',
        birthDate: '',
        useDate: this.$dayjs().format('YYYY-MM-DD'),
        calibrationType: '1',

        lifespanUnit: '1',
        warrantyPeriodUnit: '1',
        maintCycleUnit: '1',
        inspectionCycleUnit: '1',
        calibrationCycleUnit: '1'
      });
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
    },

    async handleSubmit() {
      try {
        this.submitLoading = true;
        await this.$refs.form.validate();
        let data = deepClone(this.form);
        this.$emit('submit', this.deviceIds, data);
        this.close();
      } catch (e) {
        this.submitLoading = false;
      } finally {
        this.submitLoading = false;
      }
    },

    close() {
      this.submitLoading = false;
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-batch-set-install-acceptance-info {
  ::v-deep .flex-item {
    .el-form-item__content {
      display: flex;
      align-items: center;
      .cycle-time-unit-select {
        width: 56px;
        .el-input {
          width: 56px;
          min-width: auto;
        }
      }
    }
  }
}
</style>
