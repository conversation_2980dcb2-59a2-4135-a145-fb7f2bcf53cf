<template>
  <vxe-modal
    className="dialog-device-return-to-warehouse"
    width="1080"
    height="85%"
    :title="typeTitle"
    v-model="visible"
    showFooter
    :before-hide-method="close"
  >
    <template #default>
      <ts-form ref="form" :model="form" label-width="80px">
        <ts-row>
          <ts-col :span="12">
            <ts-form-item
              label="入库类型"
              prop="inboundOrder.type"
              :rules="rules.required"
            >
              <ts-select
                style="width: 100%"
                disabled
                v-model="form.inboundOrder.type"
                placeholder="请选择"
              >
                <ts-option
                  v-for="item in typeList"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </ts-select>
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item
              label="资产类别"
              prop="inboundOrder.skuType"
              :rules="rules.required"
            >
              <ts-select
                style="width: 100%"
                v-model="form.inboundOrder.skuType"
                placeholder="请选择"
                :disabled="!isAdd || isQuick"
                clearable
                @change="handleChangeSkyType"
              >
                <ts-option
                  v-for="item in skuTypeList"
                  :key="item.itemNameValue"
                  :value="item.itemNameValue"
                  :label="item.itemName"
                />
              </ts-select>
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-row>
          <ts-col :span="12">
            <ts-form-item label="库存位置">
              <ts-input
                v-model="form.inboundOrder.loc"
                clearable
                placeholder="请输入"
              ></ts-input>
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-form-item label="备注">
          <ts-input
            type="textarea"
            v-model="form.inboundOrder.note"
            clearable
            placeholder="请输入备注"
          ></ts-input>
        </ts-form-item>

        <ts-form-item label="选择资产" v-show="!isQuick">
          <template #label>
            <span class="form-item-label" title="选择资产">
              <span class="req">*</span>
              选择资产
            </span>
          </template>
          <span class="primary-text" @click="handleClickSelectDevice">
            添加
          </span>
        </ts-form-item>

        <form-table
          class="form-table"
          :formData="form"
          operateDataKey="inboundOrderDetailList"
          disabled
          hideOperation
          :columns="businessInfoColumns"
          showSummary
          :summaryColumns="['num', 'price']"
        >
          <template v-slot:name="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tac100w">{{ row[column.property] }}</div>
            </ts-form-item>
          </template>
          <template v-slot:assetCode="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tac100w">{{ row[column.property] }}</div>
            </ts-form-item>
          </template>
          <template v-slot:uniqueNo="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tac100w">
                {{ row[column.property] }}
              </div>
            </ts-form-item>
          </template>
          <template v-slot:belongToOrgName="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tac100w">{{ row[column.property] }}</div>
            </ts-form-item>
          </template>
          <template v-slot:category22Name="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tac100w">{{ row[column.property] }}</div>
            </ts-form-item>
          </template>
          <template v-slot:model="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tac100w">{{ row[column.property] }}</div>
            </ts-form-item>
          </template>
          <template v-slot:brandName="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tac100w">{{ row[column.property] }}</div>
            </ts-form-item>
          </template>
          <template v-slot:num="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tac100w right">
                {{
                  !isNaN(parseFloat(row[column.property]))
                    ? row[column.property].toLocaleString('zh-CN')
                    : row[column.property] || ''
                }}
              </div>
            </ts-form-item>
          </template>

          <template v-slot:price="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tar100w">
                {{
                  !isNaN(parseFloat(row[column.property]))
                    ? row[column.property].toLocaleString('zh-CN')
                    : row[column.property] || ''
                }}
              </div>
            </ts-form-item>
          </template>

          <template v-slot:operate="{ row, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              v-show="!isQuick"
            >
              <!-- row.select === 'local' 选择的明细状态为入库中 禁止删除 因为选择资产查询不到 -->
              <div
                class="tac100w delete-device"
                v-show="row.select === 'local'"
                @click="() => handleDeleteDeviceRow(index)"
              >
                删除
              </div>
            </ts-form-item>
          </template>
        </form-table>
      </ts-form>

      <dialog-multiple-device
        ref="DialogMultipleDevice"
        renderType="2"
        :otherSearchParams="{
          skuType: form.inboundOrder.skuType
        }"
        @submit="handleSelectDeviceEnd"
      />
    </template>

    <template #footer>
      <ts-button
        v-show="!isQuick"
        class="more-text-btn"
        type="primary"
        :loading="submitLoading"
        @click="() => handleSubmit('callback')"
      >
        确定并打印入库单
      </ts-button>
      <ts-button type="primary" :loading="submitLoading" @click="handleSubmit">
        确 定
      </ts-button>
      <ts-button :disabled="submitLoading" class="shallowButton" @click="close">
        关 闭
      </ts-button>
    </template>
  </vxe-modal>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import FormTable from '@/components/form-table.vue';
import DialogMultipleDevice from '@/views/inventoryManagement/components/dialog-multiple-device.vue';
export default {
  components: {
    DialogMultipleDevice,
    FormTable
  },
  props: {
    typeList: {
      type: Array,
      default: () => []
    },
    useType: {
      type: String,
      default: 'usual'
    }
  },
  computed: {
    typeTitle() {
      let title = this.actionType === 'add' ? '新增' : '编辑';
      return title + '退回入库';
    },
    isQuick() {
      return this.useType === 'quick';
    },
    isAdd() {
      return this.actionType === 'add';
    }
  },
  data() {
    return {
      visible: false,
      submitLoading: false,
      actionType: '',

      skuTypeList: [],
      form: {
        inboundOrder: {},
        inboundOrderDetailList: []
      },
      rules: {
        required: { required: true, message: '必填' }
      },
      businessInfoColumns: [
        {
          prop: 'name',
          label: '资产名称',
          align: 'center'
        },
        {
          prop: 'assetCode',
          label: '资产编码',
          align: 'center',
          width: '100px'
        },
        {
          prop: 'belongToOrgName',
          label: '所属科室',
          align: 'center'
        },
        {
          prop: 'category22Name',
          label: '医疗器械分类',
          align: 'center'
        },
        {
          prop: 'model',
          label: '规格型号',
          align: 'center',
          width: 110
        },
        {
          prop: 'brandName',
          label: '资产品牌',
          align: 'center',
          width: 120
        },
        {
          prop: 'num',
          label: '数量',
          align: 'center',
          width: 85
        },
        {
          prop: 'price',
          label: '金额(元)',
          align: 'center',
          width: 120
        },
        {
          prop: 'operate',
          label: '操作',
          width: 80
        }
      ]
    };
  },
  methods: {
    async show({ type = 'add', data = {} }) {
      this.actionType = type;

      this.$set(this, 'form', deepClone(data));
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
      await this.getSkuTypeList();
      this.visible = true;
    },

    async getSkuTypeList() {
      let res = await this.ajax.getSkuTypeList('inboundOrderCreate');
      if (!res.success) {
        this.$newMessage('error', res.message || '资产类别数据获取失败!');
        return;
      }
      this.skuTypeList = res.object || [];
    },

    handleChangeSkyType() {
      this.form.inboundOrderDetailList = [];
    },

    handleClickSelectDevice() {
      if (!this.form.inboundOrder.skuType) {
        this.$newMessage('warning', '请先选择资产类别!');
        this.$refs.form.validateField('inboundOrder.skuType');
        return;
      }

      let ignoreId = this.form.inboundOrderDetailList
        .map(m => m.deviceId)
        .join(',');
      this.$refs.DialogMultipleDevice.show({
        ignoreId
      });
    },

    handleSelectDeviceEnd(list = []) {
      if (list.length === 0) {
        return;
      }
      list.forEach(f => {
        f.deviceId = f.id;
        f.price = f.originalVal;
        f.num = 1;
        f.select = 'local';
      });
      this.form.inboundOrderDetailList.push(...list);
    },

    async handleSubmit(callBackType) {
      try {
        this.submitLoading = true;
        await this.$refs.form.validate();

        let data = Object.assign({}, this.form);
        if (data.inboundOrderDetailList.length === 0) {
          this.$newMessage('warning', '资产明细不能为空，请选择!');
          this.submitLoading = false;
          return false;
        }

        await this.ajax[
          this.isAdd ? 'deviceInboundOrderSave' : 'deviceInboundOrderUpdate'
        ](data).then(res => {
          this.submitLoading = false;

          if (res.success && res.statusCode === 200) {
            this.$newMessage(
              'success',
              res.message || this.typeTitle + '成功!'
            );
            this.$emit('refresh');
            if (callBackType === 'callback' || this.isQuick) {
              this.$emit('callback', res.object);
            }
            this.close();
          } else {
            this.$newMessage('error', res.message || this.typeTitle + '失败!');
          }
        });
      } catch (error) {
        this.submitLoading = false;
        console.error(error);
      }
    },

    async handleDeleteDeviceRow(index) {
      try {
        await this.$newConfirm(
          `您是否确认【<span style="color: red">删除</span>】当前数据？`
        );
        this.form.inboundOrderDetailList.splice(index, 1);
      } catch (e) {
        console.error(e);
      }
    },

    close() {
      this.submitLoading = false;
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-device-return-to-warehouse {
  //::v-deep {
  //  .vxe-modal--body {
  //    height: 520px;
  //    overflow: auto;
  //  }
  //}

  .tar100w {
    text-align: right;
    width: 100%;
  }
  .tac100w {
    text-align: center;
    width: 100%;

    &.right {
      text-align: right;
    }
  }

  .delete-device {
    color: red;
    cursor: pointer;
  }

  .form-item-label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    .req {
      color: rgb(245, 108, 108);
      margin-right: 2px;
    }
  }

  ::v-deep .more-text-btn {
    width: 140px;
    > span {
      max-width: 140px !important;
    }
  }

  .primary-text {
    color: $primary-blue;
    cursor: pointer;
  }

  .flex-item {
    ::v-deep {
      .el-form-item__content {
        min-height: 30px;
        display: flex;
        align-items: center;
      }
    }
  }

  .required-icon {
    color: #f56c6c;
    margin-right: 4px;
  }

  ::v-deep {
    .form-table {
      .el-table__footer-wrapper {
        .cell {
          font-weight: bold;
          color: $warning-color;
        }
      }
    }

    .min-select {
      width: 100px !important;
      min-width: 100px !important;
      .el-input__inner {
        width: 100px !important;
        padding-right: 0px;
        text-align: right;
      }
      .el-input__suffix {
        right: 20px;
      }

      &.number {
        width: 65px !important;
        min-width: 65px !important;
        .el-input__inner {
          width: 65px !important;
        }
      }
    }
  }
  .custom-styles {
    transform: translate(8px, 4px);
    color: #9a9898;
  }
}
</style>
