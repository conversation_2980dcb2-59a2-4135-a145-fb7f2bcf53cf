<template>
  <el-drawer
    custom-class="ts-custom-default-drawer darwer-radiation-add"
    direction="rtl"
    size="60%"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <template slot="title">
      <span class="dialog-title">{{ title }}</span>
    </template>
    <div class="content-container">
      <el-scrollbar
        style="width: 100%;height: calc(100% - 45px);"
        wrap-style="overflow-x: hidden;"
      >
        <div class="form-container">
          <ts-form ref="ruleForm" :model="form" labelWidth="110px">
            <!-- 基本信息 -->
            <div class="form-card-box">
              <colmun-head title="基本信息" />
              <ts-row class="mrgT8 pdR8">
                <ts-col :span="8">
                  <ts-form-item label="姓名">
                    <ts-input v-model="form.applyName" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="工号">
                    <ts-input v-model="form.applyCode" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="申请日期">
                    <ts-input v-model="form.applyDate" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="院区">
                    <ts-input v-model="form.applyAreaText" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="质控科室">
                    <ts-input v-model="form.orgName" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="身份证号">
                    <ts-input v-model="form.applyIdcard" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="技术职称">
                    <ts-input v-model="form.applyTechnical" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="联系方式">
                    <ts-input v-model="form.applyPhone" disabled />
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </div>
            <!-- 任期信息 -->
            <div class="form-card-box">
              <colmun-head title="任期信息" />
              <ts-row class="mrgT8 pdR8">
                <ts-col :span="8">
                  <ts-form-item label="是否超期任职">
                    <ts-select
                      style="width: 100%;"
                      v-model="form.isOverdue"
                      disabled
                    >
                      <ts-option label="是" value="1"></ts-option>
                      <ts-option label="否" value="0"></ts-option>
                    </ts-select>
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="任期开始日期">
                    <ts-input v-model="form.applyStartDate" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="任期结束日期">
                    <ts-input v-model="form.applyEndDate" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="应任期时长">
                    <ts-input
                      class="right-aligned-input"
                      v-model="form.applyTermTime"
                      disabled
                    >
                      <template slot="append">月</template>
                    </ts-input>
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </div>
            <!-- 处罚信息 -->
            <div class="form-card-box">
              <colmun-head title="处罚信息" />
              <TsVxeTemplateTable
                id="table_darwer-details"
                class="form-table"
                ref="table"
                :columns="columns"
                :hasPage="false"
                @refresh="handleRefreshTable"
              />
              <ts-row>
                <ts-col :span="24">
                  <ts-form-item prop="key4" label="相关附件">
                    <file-list-batch ref="fileListBatch" :fileList="fileList" />
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </div>
            <!-- 考核情况 -->
            <div class="form-card-box" v-if="indexTab == 1">
              <colmun-head title="考核情况" />
              <ts-row>
                <ts-col :span="8">
                  <ts-form-item label="考核结果">
                    <ts-select
                      style="width: 100%;"
                      v-model="form.applyResult"
                      disabled
                    >
                      <ts-option label="合格" value="1"></ts-option>
                      <ts-option label="不合格" value="2"></ts-option>
                    </ts-select>
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="考核日期">
                    <ts-input v-model="form.optDate" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="已担任时长">
                    <ts-input
                      class="right-aligned-input"
                      v-model="form.applyRealTime"
                      disabled
                    >
                      <template slot="append">月</template>
                    </ts-input>
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </div>
            <!-- 中止信息 -->
            <div class="form-card-box" v-if="indexTab == 2">
              <colmun-head title="考核情况" />
              <ts-row>
                <ts-col :span="8">
                  <ts-form-item label="中止日期">
                    <ts-input v-model="form.optDate" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="禁用申请期限">
                    <ts-input
                      class="right-aligned-input"
                      v-model="form.disableTime"
                      disabled
                    >
                      <template slot="append">月</template>
                    </ts-input>
                  </ts-form-item>
                </ts-col>
                <ts-col :span="24">
                  <ts-form-item label="中止原因">
                    <ts-input
                      v-model="form.stopRemark"
                      disabled
                      type="textarea"
                      class="textarea"
                    />
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </div>
          </ts-form>
        </div>
      </el-scrollbar>
      <div class="drawer-footer">
        <ts-button @click="close" class="shallowButton">关 闭</ts-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import moment from 'moment';
import darwerDetails from '../mixins/darwer-details';
import fileListBatch from './file-list-batch.vue';
export default {
  mixins: [darwerDetails],
  components: { fileListBatch },
  data() {
    return {
      visible: false,
      isDetail: false,
      title: '',
      indexTab: null,
      form: {},
      rules: {}
    };
  },
  methods: {
    async getDetail(data) {
      let res = await this.ajax.getQualityApplyDetail(data.id);
      this.form = res.object || {};
    },
    async open({ data = null, title, indexTab = 1 }) {
      if (data) {
        await this.getDetail(data);
        await this.getFileList(data.applyFiles);
      } else {
        this.form.applyDate = moment().format('YYYY-MM-DD');
      }
      this.indexTab = indexTab;
      this.title = title;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.ruleForm?.clearValidate();
        this.handleRefreshTable();
      });
    },
    close() {
      this.visible = false;
      this.form = {};
      this.fileList = [];
      this.indexTab = null;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .darwer-radiation-add {
    .form-table {
      flex: 1;
      overflow: hidden;
      transform: scale(1);
    }
  }
  .right-aligned-input {
    margin-top: 3px;
  }
  .textarea {
    .el-textarea__inner {
      min-height: 120px !important;
      max-height: 120px !important;
    }
  }
}
</style>
