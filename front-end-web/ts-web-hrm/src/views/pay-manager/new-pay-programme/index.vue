<template>
  <div class="trasen-container flex-column">
    <ts-tabs v-model="activeTab">
      <ts-tab-pane label="薪酬方案" name="1"></ts-tab-pane>
      <ts-tab-pane label="工资条模版" name="2"></ts-tab-pane>
    </ts-tabs>

    <components-programme ref="ComponentsProgramme" v-if="activeTab == '1'" />
    <components-salary-slip-template
      ref="ComponentsSalarySlipTemplate"
      v-if="activeTab == '2'"
    />
  </div>
</template>

<script>
import ComponentsProgramme from './components/components-programme.vue';
import ComponentsSalarySlipTemplate from './components/components-salary-slip-template.vue';
export default {
  components: {
    ComponentsProgramme,
    ComponentsSalarySlipTemplate
  },
  data() {
    return {
      activeTab: '1'
    };
  },
  watch: {
    activeTab() {
      this.refresh();
    }
  },
  methods: {
    refresh() {
      let dic = {
        1: 'ComponentsProgramme',
        2: 'ComponentsSalarySlipTemplate'
      };

      this.$nextTick(() => {
        this.$refs[dic[this.activeTab]].handleRefreshTable();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.trasen-container {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
