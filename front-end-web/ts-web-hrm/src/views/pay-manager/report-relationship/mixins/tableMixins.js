export default {
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '姓名',
          value: 'employeeName',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入姓名'
          }
        },
        {
          label: '工号',
          value: 'employeeNo',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入工号'
          }
        },
        {
          label: '科室',
          value: 'orgName',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入科室'
          }
        }
      ],
      columns: [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 50
        },
        {
          label: '考核人姓名',
          prop: 'timeEmployeeName',
          align: 'center'
        },
        {
          label: '考核人工号',
          prop: 'timeEmployeeNo',
          align: 'center'
        },
        {
          label: '考核人科室',
          prop: 'timeOrgName',
          align: 'center'
        },
        {
          label: '上报人工号',
          prop: 'byEmployeeNo',
          align: 'center'
        },
        {
          label: '上报人姓名',
          prop: 'byEmployeeName',
          align: 'center'
        },
        {
          label: '上报人科室',
          prop: 'byOrgName',
          align: 'center'
        },
        {
          label: '操作',
          align: 'center',
          width: 80,
          headerSlots: 'action',
          formatter: row => {
            let actionList = [];
            if (row.children) {
              actionList = [
                {
                  label: '清空',
                  event: this.handleEmpty
                }
              ];
            }

            return (
              <BaseActionCell
                actions={actionList}
                on={{ 'action-select': event => event(row) }}
              />
            );
          }
        }
      ]
    };
  }
};
