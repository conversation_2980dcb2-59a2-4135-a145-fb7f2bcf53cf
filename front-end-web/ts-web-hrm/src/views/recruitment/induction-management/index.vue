<template>
  <div class="trasen-container">
    <ts-tabs v-model="zpglEmployeeStatus">
      <ts-tab-pane label="待入职" name="1" />
      <ts-tab-pane label="已入职" name="2" />
    </ts-tabs>

    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :resetData="{
        interviewJobtext: '',
        interviewJob: ''
      }"
      @search="search"
    >
      <template v-slot:right>
        <ts-button
          v-show="zpglEmployeeStatus === '2'"
          type="primary"
          @click="handleExport"
          >导出</ts-button
        >
      </template>
    </ts-search-bar>

    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      :propPageSize="40"
      :pageSizes="[20, 40, 60, 80, 100, 200]"
      v-loading="loading"
      :columns="
        columns(true, inductionColumns, {
          label: '拟定岗位',
          prop: 'resInterviewPath'
        })
      "
      :default-sort="{ prop: 'age', order: 'descending' }"
      @refresh="handleRefreshTable"
    />

    <dialog-add
      v-model="dialogAddBoolean"
      formTemplate="cscjk-form"
      :eachData="addEachData"
      :treeData="treeData"
      :titleList="titleList"
      :successCallBack="handleRefreshTable"
    />

    <dialog-person-details
      ref="DialogPersonDetails"
      formTemplate="cscjk-form"
      :treeData="treeData"
      :titleList="titleList"
      @refresh="handleRefreshTable"
    />

    <dialog-change
      ref="DialogChange"
      :treeData="treeData"
      @successCallBack="handleRefreshTable"
    />

    <dialog-add-talent-pool
      ref="DialogAddTalentPool"
      :treeData="treeData"
      @successCallBack="handleRefreshTable"
    />

    <dialog-track ref="DialogTrack" @successCallBack="handleRefreshTable" />
  </div>
</template>

<script>
import SearchTable from '@/views/recruitment/Mixins/search-table.js';
import SearchData from '@/views/recruitment/Mixins/search-data.js';
import RecruitmentMethods from '@/views/recruitment/Mixins/recruitment-methods.js';

import { talentPoolTemplateTree } from '@/api/ajax/NewRecruit/template.js';
import { getZpglInterviewMessageList } from '@/api/ajax/NewRecruit/induction.js';

import DialogAdd from '@/views/recruitment/resume-management/dialog/dialog-add.vue';
import DialogPersonDetails from '@/views/recruitment/components/dialog-person-details/dialog-person-details.vue';
import DialogChange from '@/views/recruitment/induction-management/dialog/dialog-change.vue';
import DialogAddTalentPool from '@/views/recruitment/components/dialog-add-talent-pool/dialog-add-talent-pool.vue';
import DialogTrack from '@/views/recruitment/new-talent-pool-management/dialog/dialog-track.vue';

export default {
  mixins: [SearchTable, SearchData, RecruitmentMethods],
  components: {
    DialogAdd,
    DialogPersonDetails,
    DialogChange,
    DialogTrack,
    DialogAddTalentPool
  },
  data() {
    return {
      loading: false,

      zpglEmployeeStatus: '1',
      treeData: []
    };
  },
  watch: {
    zpglEmployeeStatus: {
      handler() {
        this.refresh();
      }
    }
  },
  async created() {
    this.handleGetTreeData();
  },
  methods: {
    refresh() {
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    },
    async handleGetTreeData() {
      // 获取人才库岗位
      const res = await talentPoolTemplateTree();
      if (res.success == false) {
        this.$message.error(res.message || '获取人才库岗位失败');
        return;
      }
      this.treeData = res.object;
    },

    handleExport() {
      this.loading = true;
      let pageNo = this.$refs.table.pageNo || 1,
        pageSize = this.$refs.table.pageSize || 15,
        data = {
          pageNo,
          pageSize,
          ...this.searchForm,
          sord: 'desc',
          sidx: 'a.create_date'
        };

      data.interviewResultStatusList = (
        data.interviewResultStatusList || []
      ).join(',');
      data.personnelCategoryList = (data.personnelCategoryList || []).join(',');

      let queryData = Object.keys(data)
        .map(key => key + '=' + data[key])
        .join('&');
      let a = document.createElement('a');
      a.href = '/ts-hrms/api/zpglInterviewMessage/exportEntryList?' + queryData;
      a.click();
      this.loading = false;
    },

    //检索
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    async handleRefreshTable() {
      this.loading = true;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          zpglEmployeeStatus: this.zpglEmployeeStatus,
          sidx: 't1.create_date',
          sord: 'desc'
        };

      searchForm.interviewResultStatusList = (
        searchForm.interviewResultStatusList || []
      ).join(',');

      searchForm.personnelCategoryList = (
        searchForm.personnelCategoryList || []
      ).join(',');

      let res = await getZpglInterviewMessageList(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
      this.loading = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.trasen-container {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  @import url('../styles/recruitment-table.css');
  ::v-deep {
    .form-table {
      .el-table__body-wrapper {
        margin-top: 31px !important;
      }
    }
  }
}
.trasen-container /deep/ .el-tabs__nav {
  height: unset !important;
}
</style>
