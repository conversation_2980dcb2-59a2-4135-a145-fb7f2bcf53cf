<template>
  <div v-show="visible" class="customization" :style="windowStyle">
    <div class="float-content" @mousedown="startDrag">
      <div class="item" @click="handleShowAnonyMousBox(1)">
        <img src="@/assets/img/home/<USER>" />
        <span class="name">意见反馈</span>
      </div>
      <div class="item" @click="handleShowAnonyMousBox(2)">
        <img src="@/assets/img/home/<USER>" />
        <span class="name">心理关爱</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'customization',
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    initialX: {
      type: Number,
      default: window.innerWidth - 60
    },
    initialY: {
      type: Number,
      default: window.innerHeight / 2 - 75
    }
  },
  data() {
    return {
      dragging: false,
      posX: this.initialX,
      posY: this.initialY,
      startX: 0,
      startY: 0,
      originalX: this.initialX,
      originalY: this.initialY
    };
  },
  mounted() {
    // 确保初始位置正确
    this.posX = Math.min(this.posX, window.innerWidth - this.$el.offsetWidth);
    this.posY = Math.min(this.posY, window.innerHeight - this.$el.offsetHeight);

    this.originalX = this.posX;
    this.originalY = this.posY;
  },
  computed: {
    windowStyle() {
      return {
        left: `${this.posX}px`,
        top: `${this.posY}px`
      };
    }
  },
  methods: {
    startDrag(event) {
      event.preventDefault();
      this.dragging = true;

      // 获取初始位置
      this.startX = event.clientX - this.posX;
      this.startY = event.clientY - this.posY;

      // 绑定移动事件
      document.addEventListener('mousemove', this.onDrag);
      document.addEventListener('mouseup', this.stopDrag);
    },
    handleShowAnonyMousBox(index) {
      this.$emit('handleShowAnonyMousBox', index);
    },
    onDrag(event) {
      if (this.dragging) {
        // 计算新位置
        const newX = event.clientX - this.startX;
        const newY = event.clientY - this.startY;

        // 边界限制
        const maxX = window.innerWidth - this.$el.offsetWidth;
        const maxY = window.innerHeight - this.$el.offsetHeight;
        this.posX = Math.max(0, Math.min(newX, maxX));
        this.posY = Math.max(0, Math.min(newY, maxY));
      }
    },
    stopDrag() {
      this.dragging = false;
      document.removeEventListener('mousemove', this.onDrag);
      document.removeEventListener('mouseup', this.stopDrag);
    }
  }
};
</script>

<style lang="scss" scoped>
.customization {
  position: fixed;
  right: 10px;
  border: 1px solid #ddd;
  border-radius: 14px;
  box-shadow: -8px 10px 12px rgba(0, 0, 0, 0.15);
  width: 60px;
  height: 130px;
  z-index: 9900;
  user-select: none;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', system-ui;
  line-height: 1.6;
  margin: 0;
  background-color: #f8f9fa;
  transition: margin-left 0.3s ease;
  font-family: 'Segoe UI', system-ui;
  font-size: 16px;
  line-height: 1.6;
  border-radius: 8px 0 0 8px;
  * {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
  }
  .float-content {
    padding: 10px 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
    .item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      img {
        width: 30px;
        height: 30px;
      }
      .name {
        font-weight: bold;
        font-size: 14px;
        line-height: 20px;
      }
    }
  }
}
</style>
