import { $api } from '@/api/ajax';
import { service } from '@/api/config';

export default {
  emailInternalSelectMainEmailList() {
    return $api({
      url: `${service.tsInformation()}/optimize/emailInternal/selectMainEmailList`,
      method: 'post'
    });
  },

  emailInternalList(params) {
    return $api({
      url: `${service.tsInformation()}/optimize/emailInternal/list`,
      method: 'get',
      params
    });
  },

  emailInternalOperateMail(params) {
    return $api({
      url: `${service.tsInformation()}/optimize/emailInternal/operateMail`,
      method: 'post',
      params
    });
  },

  employeeGetMyEmployeeDetail() {
    return $api({
      url: `${service.tsBasics()}/employee/getMyEmployeeDetail`,
      method: 'post'
    });
  },

  employeeUpdateUserConfig(data) {
    return $api({
      url: `${service.tsBasics()}/employee/updateUserConfig`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },

  selectEmailContactByUsercode() {
    return $api({
      url: `${service.tsInformation()}/api/emailContact/selectEmailContactByUsercode`,
      method: 'get'
    });
  },

  getEmployeeSelectEmpSignimg() {
    return $api({
      url: `${service.tsOa()}/employee/selectEmpSignimg`,
      method: 'get'
    });
  },

  optimizeEmailInternalUpdate(data) {
    return $api({
      url: `${service.tsInformation()}/optimize/emailInternal/update`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },

  optimizeEmailInternalSendEmailInternal(data, isDraft = '') {
    return $api({
      url:
        `${service.tsInformation()}/optimize/emailInternal/sendEmailInternal` +
        isDraft,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },

  // 查看邮件详情
  emailInternalGetEmailDetails(params) {
    return $api({
      url: `${service.tsInformation()}/optimize/emailInternal/getEmailDetails`,
      method: 'get',
      params
    });
  },

  emailInternalGetReadDetails(params) {
    return $api({
      url: `${service.tsInformation()}/optimize/emailInternal/getReadDetails`,
      method: 'get',
      params
    });
  },

  emailInternalReceiptOpt(data) {
    return $api({
      url: `${service.tsInformation()}/optimize/emailInternal/receiptOpt`,
      method: 'post',
      data
    });
  },

  emailInternalCancelEmailInternal(data) {
    return $api({
      url: `${service.tsInformation()}/emailInternal/cancelEmailInternal`,
      method: 'post',
      data
    });
  }
};
