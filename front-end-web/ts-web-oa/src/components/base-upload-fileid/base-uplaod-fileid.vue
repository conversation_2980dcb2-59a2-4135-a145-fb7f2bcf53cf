<template>
  <div class="flex-grow">
    <ts-upload
      v-if="!onlyRead && fileList.length < limit"
      ref="tsUpload"
      action="/ts-basics-bottom/fileAttachment/upload?moduleName=oa"
      :drag="drag"
      :limit="limit"
      :fileList.sync="fileList"
      :show-file-list="false"
      :on-exceed="masterFileMax"
      :http-request="handleUploadFile"
    >
      <slot>
        <div v-if="drag" class="file-drag-container flex-center flex-column">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        </div>
        <ts-button v-else>{{ buttonName }}</ts-button>
      </slot>
      <div slot="tip" class="el-upload__tip">
        {{ tips }}
      </div>
    </ts-upload>
    <slot name="uploadFiles" :fileList="fileList">
      <ts-upload-file-list
        :fileList.sync="fileList"
        :on-remove="onRemove"
        :on-abort="onAbort"
        :on-upload="onUpload"
        :onPreview="onPreview"
        :showPreview="showPreview"
        :showUpload="showUpload"
        :showRemove="showRemove && !onlyRead"
        :type="fileListType"
      ></ts-upload-file-list>
    </slot>

    <el-image
      ref="preview"
      style="display: none;"
      :src="previewFile"
      :preview-src-list="previewFileList"
      :z-index="3000"
    >
    </el-image>
  </div>
</template>

<script>
import commonUtils from '@/unit/file.js';

export default {
  model: {
    prop: 'businessId',
    event: 'input'
  },
  props: {
    businessId: {
      type: String,
      default: () => ''
    },
    tips: {
      type: String,
      default: () => ''
    },
    onlyRead: {
      type: Boolean,
      default: () => false
    },
    drag: {
      type: Boolean,
      default: () => false
    },
    limit: {
      type: Number,
      default: 99
    },
    actions: {
      type: [Array, String],
      default: () => ['preview', 'downLoad', 'remove']
    },
    fileListType: {
      type: String,
      default: () => 'mixture'
    },
    staticData: {
      type: Array,
      default: () => []
    },
    buttonName: {
      type: String,
      default: () => '文件上传'
    },
    moduleName: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      fileList: [],
      innerBusinessId: [],
      previewFile: '',
      previewFileList: []
    };
  },
  computed: {
    showPreview() {
      let actions = this.actions;
      if (this.actions instanceof String) {
        actions = [this.actions];
      }
      return actions.includes('preview');
    },
    showUpload() {
      let actions = this.actions;
      if (this.actions instanceof String) {
        actions = [this.actions];
      }
      return actions.includes('downLoad');
    },
    showRemove() {
      let actions = this.actions;
      if (this.actions instanceof String) {
        actions = [this.actions];
      }
      return actions.includes('remove');
    }
  },
  watch: {
    businessId: {
      handler(val) {
        this.innerBusinessId = val.split(',');
        this.fileList = [];
        if (val) {
          this.getFileList();
        }
        if (!val && this.staticData.length) {
          this.fileList = this.staticData;
        }
      },
      immediate: true
    }
  },
  methods: {
    /**@desc 获取文件列表 */
    getFileList() {
      this.fileList = [];
      this.ajax
        .getFileAttachmentSelectByIds({
          idsStr: this.innerBusinessId.join(',')
        })
        .then(res => {
          if (res.success == false) {
            return;
          }
          this.fileList = res.object.map(item => ({
            ...item,
            uid: item.id,
            url: item.realPath,
            name: item.originalName,
            status: 'success'
          }));

          if (!this.fileList.length) {
            this.$emit('input', null);
          }
        });
    },
    /**@desc 替换原生上传 */
    handleUploadFile(params) {
      let data = new FormData();
      data.append('file', params.file);
      this.ajax
        .uploadFileDocument(data, this.moduleName)
        .then(res => {
          if (res.success) {
            this.fileList.push({
              ...res.object[0],
              realPath: res.object[0].filePath,
              url: res.object[0].filePath,
              uid: res.object[0].fileId,
              name: res.object[0].fileRealName
            });
            this.innerBusinessId.push(res.object[0].fileId);
            this.$emit('input', this.innerBusinessId.join(','));
            this.$emit('action-input', this.innerBusinessId.join(','));
          } else {
            this.$message.error(res.message || '上传失败');
          }
        })
        .catch(err => {
          this.$message.error('上传失败，请重试!');
        });
    },

    /**@desc 删除 */
    onRemove(file) {
      let idx = this.fileList.findIndex(e => {
          return e.uid === file.uid;
        }),
        deleteFile = this.fileList[idx] || {};
      this.ajax.deleteFileId({ fileid: deleteFile.uid }).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '删除失败');
          return;
        }
        this.$message.success('删除成功');
        this.fileList.splice(idx, 1);
        this.innerBusinessId.splice(idx, 1);
        this.$emit('input', this.innerBusinessId.join(','));
        this.$emit('action-input', this.innerBusinessId.join(','));
        // if (!this.fileList.length) {
        //   this.$emit('input', '');
        //   this.$emit('action-input', '');
        // }
      });
    },
    onAbort(file) {
      this.$refs.tsUpload.abort(file);
      let idx = this.fileList.findIndex(e => {
        return e.uid === file.uid;
      });
      this.fileList.splice(idx, 1);
    },
    /**@desc 图片点击下载 */
    onUpload(file) {
      let a = document.createElement('a');
      a.href = '/ts-document/attachment/downloadFile/' + file.uid;
      a.click();
    },
    /**@desc 图片点击预览 */
    onPreview(file) {
      if (commonUtils.isDoc(file.fileName)) {
        commonUtils.viewerDocBase2(file.uid, file.fileName);
      } else {
        this.previewFile = location.origin + file.url;
        this.previewFileList = this.fileList
          .filter(item => commonUtils.isImg(item.name))
          .map(item => location.origin + item.url);
        this.$nextTick(() => {
          this.$refs.preview.clickHandler();
        });
      }
    },
    masterFileMax() {
      this.$message.warning(`最多上传 ${this.limit} 个文件。`);
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-upload-dragger {
    width: 210px !important;
    height: 80px !important;
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover {
      border-color: $theme-color-80 !important;
    }

    &:active {
      border-color: $theme-color-80 !important;
      color: transparent !important;
    }

    &.is-dragover {
      border: 2px dashed $theme-color-80 !important;
      background-color: $list-hover-color !important;
    }

    .file-drag-container {
      width: 100%;
      height: 100%;
      .el-icon-upload {
        margin: 0px;
        padding: 0px;
        font-size: 40px;
        line-height: 44px;
      }

      .el-upload__text {
        em {
          color: $theme-color-80;
        }
      }
    }
  }
}
</style>
