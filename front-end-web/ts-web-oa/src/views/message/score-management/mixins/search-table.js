export default {
  data() {
    return {
      loading: false,
      searchForm: {},
      searchList: [
        {
          label: '姓名',
          value: 'username',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入姓名'
          }
        },
        {
          label: '科室',
          value: 'deptname',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入科室'
          }
        }
      ],
      actions: [
        {
          label: '导入',
          click: this.handleImportScore,
          prop: { type: 'primary' }
        },
        {
          label: '批量发送',
          click: this.handleBatchSendMessage,
          prop: { type: 'primary' }
        }
      ],
      columns: [
        {
          type: 'selection',
          align: 'center',
          width: 55
        },
        {
          label: '序号',
          type: 'index',
          align: 'center',
          fixed: 'left'
        },
        {
          label: '姓名',
          prop: 'username',
          align: 'center',
          width: 115
        },
        {
          label: '工号',
          prop: 'usercode',
          align: 'center',
          width: 75
        },
        {
          label: '科室',
          prop: 'deptname',
          align: 'center',
          width: 115
        },
        {
          label: '电话',
          prop: 'phone',
          align: 'center',
          width: 115
        },
        {
          label: '分数',
          prop: 'score',
          align: 'center',
          width: 75
        },
        {
          label: '备注',
          prop: 'remark'
        },
        {
          label: '操作',
          align: 'center',
          width: 120,
          fixed: 'right',
          headerSlots: 'action',
          formatter: row => {
            let arr = [
              {
                label: '发送短信',
                event: this.handleSendMessage
              },
              {
                label: '编辑',
                event: this.handleEditScore
              }
            ];
            return (
              <BaseActionCell
                actions={arr}
                on={{ 'action-select': e => e(row) }}
              />
            );
          }
        }
      ]
    };
  }
};
