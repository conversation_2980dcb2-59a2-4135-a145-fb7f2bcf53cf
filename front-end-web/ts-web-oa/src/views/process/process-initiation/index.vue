<template>
  <div class="process-initiation flex-column">
    <ts-tabs v-model="activeTab" class="tabs-container">
      <ts-tab-pane name="0" label="全部流程"></ts-tab-pane>
      <ts-tab-pane name="1" label="我的收藏"></ts-tab-pane>
      <ts-tab-pane name="2" label="常用流程"></ts-tab-pane>
    </ts-tabs>
    <el-popover placement="bottom-start" trigger="click" ref="set">
      <div class="likeSet">
        <p class="setTitle">设置默认选中页签</p>
        <p
          class="set"
          :class="defaultTab == 1 ? 'active' : ''"
          @click="preferenceSave(1)"
        >
          全部流程
        </p>
        <p
          class="set"
          :class="defaultTab == 2 ? 'active' : ''"
          @click="preferenceSave(2)"
        >
          我的收藏
        </p>
        <p
          class="set"
          :class="defaultTab == 3 ? 'active' : ''"
          @click="preferenceSave(3)"
        >
          常用流程
        </p>
      </div>
      <i class="oaicon oa-icon-shezhi1" slot="reference" title="偏好设置"></i>
    </el-popover>
    <div>
      <ts-search-bar
        v-model="searchForm"
        :actions="actions"
        :formList="searchList"
        :elementCol="14"
        @search="search"
        :resetData="{}"
      >
      </ts-search-bar>
      <all-flow ref="allFlow" @search="search" v-if="activeTab == 0" />
      <flow ref="flow" @search="search" v-if="activeTab == 1" />
      <used-flow ref="usedFlow" @search="search" v-if="activeTab == 2" />
    </div>
  </div>
</template>

<script>
import allFlow from './components/allFlow.vue';
import flow from './components/flow.vue';
import usedFlow from './components/usedFlow.vue';
export default {
  components: { allFlow, flow, usedFlow },
  data() {
    return {
      searchForm: {},
      actions: [],
      searchList: [
        {
          label: '流程名称',
          value: 'condition',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入流程名称'
          }
        }
      ],
      defaultTab: -1,
      activeTab: '-1',
      apiList: ['getWfClassifyTree', 'getCollectTree', 'getCommonlyUsed'],
      refList: ['allFlow', 'flow', 'usedFlow'],
      options: {
        btn: false,
        edit: false,
        del: false,
        stop: false,
        start: false,
        copy: false,
        collect: false,
        collectN: false
      }
    };
  },
  watch: {
    activeTab: {
      handler(val) {
        if (val == 0) {
          this.options.btn = true;
          this.options.collect = true;
          this.options.collectN = true;
        }
        if (val >= 0) {
          this.search();
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.preferenceFindByUser();
  },
  computed: {
    userCode() {
      return this.$store.state.common.userInfo.employeeNo;
    }
  },
  methods: {
    preferenceFindByUser() {
      this.ajax.preferenceFindByUser({ userCode: this.userCode }).then(res => {
        if (res.success && res.object) {
          this.activeTab = Number(res.object.dictId) - 1 + '';
          this.defaultTab = Number(res.object.dictId);
        } else {
          this.activeTab = 0;
        }
      });
    },
    preferenceSave(activeTab) {
      this.ajax
        .preferenceSave({
          dictId: activeTab,
          dictName:
            activeTab == 1
              ? '全部流程'
              : activeTab == 2
              ? '收藏流程'
              : '常用流程',
          userCode: this.userCode
        })
        .then(res => {
          if (res.success) {
            this.defaultTab = activeTab;
            this.$message.success('设置成功');
            this.$refs.set.doClose();
          } else {
            this.$message.error(res.message || '设置失败');
          }
        });
    },
    search() {
      this.handleRefreh();
    },
    handleRefreh() {
      this.ajax[this.apiList[this.activeTab]](this.searchForm).then(res => {
        this.$nextTick(() => {
          this.$refs[this.refList[this.activeTab]].init(
            this.options,
            res.object || res.rows || []
          );
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.process-initiation {
  --color: #5640ff;
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  .oaicon {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 20px;
    color: #5460ff;
    cursor: pointer;
  }
}
.likeSet {
  .setTitle {
    margin: 0 0 5px 0;
    font-weight: bold;
  }
  .set {
    margin: 0 0 5px 0;
    &.active {
      color: #5460ff;
    }
    &:hover {
      color: #5460ff;
      cursor: pointer;
    }
  }
}
</style>
