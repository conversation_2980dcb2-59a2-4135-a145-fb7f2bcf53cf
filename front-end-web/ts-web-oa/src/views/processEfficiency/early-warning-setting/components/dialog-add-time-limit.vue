<template>
  <ts-dialog
    custom-class="dialog-add-time-limit"
    append-to-body
    :visible.sync="visible"
    :title="title"
    @close="close"
  >
    <ts-form ref="form" :model="form" labelWidth="120px">
      <ts-form-item label="流程" :rules="rules.required">
        <base-select
          style="width: 100%"
          v-model="form.workflowId"
          :inputText.sync="form.workflowName"
          :loadMethod="handleGetWorkflowList"
          label="workflowName"
          value="workflowId"
          searchInputName="workflowName"
          :clearable="false"
          @select="handleWorkflowSelect"
        />
      </ts-form-item>
      <ts-form-item label="流程时效" :rules="rules.required">
        <ts-input
          v-model="form.workflowTime"
          @input="validateInputFourDecimal($event, 'workflowTime')"
        >
          <template slot="append">天</template>
        </ts-input>
      </ts-form-item>
      <ts-form-item label="节点时效" :rules="rules.required">
        <ts-input
          v-model="form.nodeTime"
          @input="validateInputFourDecimal($event, 'nodeTime')"
        >
          <template slot="append">时</template>
        </ts-input>
      </ts-form-item>
    </ts-form>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" @click="submit">提 交</ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      title: '',
      type: 'add',
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    show(opt = {}) {
      let { title, type, row } = opt;
      this.title = title;
      this.type = type;
      this.initData();

      if (this.type === 'edit') {
        this.$set(this, 'form', deepClone(row));
      }
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });

      this.visible = true;
      this.$forceUpdate();
    },
    handleGetWorkflowList() {},
    handleWorkflowSelect(item) {
      this.$set(this.form, 'workflowId', item.workflowId);
      this.$set(this.form, 'workflowName', item.workflowName);
    },
    /**@desc 校验输入2位小数 */
    validateInputFourDecimal(value, field) {
      let matchList = value.match(/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2}|\.{1})?/);
      let newVal = matchList && matchList[0];
      this.$set(this.form, field, newVal);
    },
    async submit() {
      let validate = await this.$refs.ruleForm.validate().catch(res => res);
      if (!validate) {
        return;
      }
      const data = Object.assign({}, this.form);
      let API = null;
      if (this.type === 'add') {
        API = this.ajax.partyBuildingTeamSave;
      } else {
        API = this.ajax.partyBuildingTeamUpdate;
      }
      const res = await API(data);

      if (!res.success) {
        this.$message.error(res.message || '操作失败');
        return;
      }
      this.close();
      this.$message.success('操作成功!');
      this.$emit('submit');
    },
    close() {
      this.form = {};
      this.visible = false;
    }
  }
};
</script>
