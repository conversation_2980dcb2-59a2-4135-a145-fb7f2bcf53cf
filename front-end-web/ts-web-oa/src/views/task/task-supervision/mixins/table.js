import {
  urgencyLevelList,
  overdueList,
  progressList,
  urgentList,
  registerStatus
} from '../../config.js';

export default {
  data() {
    return {
      searchForm: {},

      actions: [],

      searchList: [
        {
          label: '任务名称',
          value: 'registerMatter',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入督办事项'
          }
        },
        {
          label: '任务类型',
          value: 'registerType'
        },
        {
          label: '紧急程度',
          value: 'registerUrgency',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: urgencyLevelList
        },
        {
          label: '是否超期',
          value: 'isOverdue',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: overdueList
        },
        {
          label: '当前进度',
          value: 'registerStatus',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: progressList
        },
        {
          label: '计划完成时间',
          value: 'completeDateList',
          element: 'ts-range-picker',
          elementProp: {
            valueFormat: 'YYYY-MM-DD'
          }
        },
        {
          label: '是否催办',
          value: 'isUrge',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: urgentList
        }
      ],
      columns: [
        {
          label: '序号',
          type: 'index',
          align: 'center'
        },
        {
          label: '督办事项',
          prop: 'registerMatter',
          align: 'center',
          minWidth: 240,
          formatter: row => {
            let registerUrgency =
              row.registerUrgency === '1' ? (
                ''
              ) : row.registerUrgency === '2' ? (
                <span class="registerUrgency yellow">紧急</span>
              ) : (
                <span class="registerUrgency red">非常紧急</span>
              );
            return (
              <div class="registerMatter">
                {registerUrgency}
                <div
                  class="details-span over"
                  onClick={() => {
                    this.handleDetails(row);
                  }}>
                  {row.registerMatter}
                </div>
              </div>
            );
          }
        },
        {
          label: '类型',
          prop: 'registerTypeName',
          width: 130,
          align: 'center'
        },
        // {
        //   label: '紧急程度',
        //   prop: 'registerUrgency',
        //   align: 'center',
        //   width: 100,
        //   sortable: true,
        //   formatter: row => {
        //     return row.registerUrgency === '1' ? (
        //       <span>普通</span>
        //     ) : row.registerUrgency === '2' ? (
        //       <span style="color:#fd9d53">紧急</span>
        //     ) : (
        //       <span style="color:#f70808">非常紧急</span>
        //     );
        //   }
        // },
        {
          label: '计划完成时间',
          prop: 'completeDate',
          align: 'center',
          sortable: true,
          width: 120
        },
        {
          label: '是否超期',
          prop: 'isOverdue',
          align: 'center',
          width: 100,
          formatter: row => {
            return row.isOverdue === '0' ? (
              <span>未超期</span>
            ) : row.isOverdue === '1' ? (
              <span style="color:#fd9d53">即将超期</span>
            ) : (
              <span style="color:#f70808">已超期</span>
            );
          }
        },
        {
          label: '累计催办',
          prop: 'urgeCount',
          align: 'center',
          width: 100,
          formatter: row => {
            return <span class="details-span">{row.urgeCount}</span>;
          }
        },
        {
          label: '当前进度',
          prop: 'registerStatus',
          align: 'center',
          sortable: true,
          width: 100,
          formatter: row => {
            let label = registerStatus.find(
              (item, index) => index == row.registerStatus
            );
            return <span>{label}</span>;
          }
        },
        {
          label: '当前未处理人',
          prop: 'currentHandleUser',
          align: 'center',
          width: 120
        },
        {
          label: '办结日期',
          prop: 'finishDate',
          align: 'center',
          sortable: true,
          width: 120
        },
        {
          label: '创建人',
          prop: 'createUserName',
          align: 'center',
          width: 100
        },
        {
          label: '创建日期',
          prop: 'createDate',
          align: 'center',
          sortable: true,
          width: 180
        },
        {
          label: '操作',
          align: 'center',
          width: 200,
          fixed: 'right',
          headerSlots: 'action',
          formatter: row => {
            let arr = [
              {
                label: '编辑',
                event: this.handleEdit,
                isStatus: '0'
              },
              {
                label: '指派',
                event: this.handleAssign,
                isStatus: '1'
              },
              {
                label: '催办',
                event: this.handleUrgent,
                isStatus: '2,3,4'
              },
              {
                label: '延期',
                event: this.handleDelay,
                isStatus: '1,2'
              },
              {
                label: '终止',
                event: this.handleClose,
                isStatus: '1,2'
              }
              // {
              //   label: '反馈',
              //   event: this.handleFeedBack,
              //   isStatus: ''
              // }
            ];
            if (
              this.$store.state.common.userInfo.employeeNo == row.createUser
            ) {
              arr.push({
                label: '删除',
                event: this.handleDelete,
                isStatus: '0,6,7'
              });
            }
            let newArr = arr.filter(e => {
              return (
                e.isStatus == '' || e.isStatus.indexOf(row.registerStatus) > -1
              );
            });
            return (
              <BaseActionCell
                actions={newArr}
                on={{ 'action-select': e => e(row) }}
              />
            );
          }
        }
      ]
    };
  }
};
