<template>
  <ts-dialog
    class="dialog-violation-records"
    :title="title"
    :visible.sync="visible"
    :append-to-body="true"
    width="800"
    @close="close"
  >
    <div class="content">
      <ts-form ref="ruleForm" :model="form" labelWidth="120px">
        <ts-row>
          <ts-col :span="12">
            <ts-form-item
              label="违章日期"
              prop="violationDate"
              :rules="rules.required"
            >
              <ts-date-picker
                style="width: 100%"
                v-model="form.violationDate"
                valueFormat="YYYY-MM-DD"
                placeholder="请选择"
              />
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item
              label="违章人"
              prop="violationName"
              :rules="rules.required"
            >
              <ts-input
                v-model="form.violationName"
                :maxlength="10"
                placeholder="请输入"
              />
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-row>
          <ts-col :span="12">
            <ts-form-item
              label="违章类型"
              prop="violationType"
              :rules="rules.required"
            >
              <ts-select
                style="width: 100%"
                v-model="form.violationType"
                clearable
                placeholder="请选择"
              >
                <ts-option
                  v-for="item of violationsTypeList"
                  :key="item.id"
                  :label="item.itemName"
                  :value="item.itemNameValue"
                ></ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item
              label="违章车辆"
              prop="vehicleId"
              :rules="rules.required"
            >
              <base-select
                style="width: 100%"
                v-model="form.vehicleId"
                :inputText.sync="form.violationVehicle"
                :loadMethod="handleGetVehicleList"
                label="vehicleNo"
                value="id"
                searchInputName="violationVehicle"
                :clearable="false"
              ></base-select>
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-row>
          <ts-form-item
            label="违章地点"
            prop="violationAddress"
            :rules="rules.required"
          >
            <city-select-cascader
              :inpText.sync="form.violationAddress"
              :inpVal.sync="form.violationAddressCode"
            />
          </ts-form-item>
        </ts-row>

        <ts-row>
          <ts-form-item label="详细地址: ">
            <ts-input
              v-model="form.violationDetailAddress"
              :maxlength="50"
              placeholder="请输入"
            />
          </ts-form-item>
        </ts-row>

        <ts-row>
          <ts-col :span="12">
            <ts-form-item label="扣分: ">
              <ts-input
                v-model="form.violationDeduct"
                :maxlength="10"
                placeholder="请输入"
                @blur="inputBlur($event, 'form', 'violationDeduct')"
                @input="
                  value =>
                    (form.violationDeduct = (value.match(/\d+/g) || [''])[0])
                "
              >
                <template slot="append">元</template>
              </ts-input>
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item label="罚款: ">
              <ts-input
                v-model="form.violationFine"
                :maxlength="10"
                placeholder="请输入"
                @input="
                  validateTowDecimalPlaces($event, 'form', 'violationFine')
                "
                @blur="inputBlur($event, 'form', 'violationFine')"
              >
                <template slot="append">元</template>
              </ts-input>
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-form-item label="备注">
          <ts-input
            v-model="form.violationRemark"
            type="textarea"
            class="textarea"
            maxlength="500"
            placeholder="请输入"
            show-word-limit
          />
        </ts-form-item>

        <ts-form-item label="附件">
          <base-upload ref="violationFiles" v-model="form.violationFiles" />
        </ts-form-item>
      </ts-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" @click="submit">提 交</ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import BaseSelect from '@/components/base-select/index.vue';
import { vehicleInfoList } from '@/api/ajax/vehicle/vehicle-information.js';
import {
  vehicleDriverViolationSave,
  vehicleDriverViolationUpdate
} from '@/api/ajax/driver/index.js';
import { inputTowDecimalPlaces } from '@/unit/commonHandle.js';

export default {
  model: {
    event: 'change',
    prop: 'show'
  },
  components: {
    BaseSelect
  },
  props: {
    show: {
      type: Boolean
    },
    eachData: {
      type: Object
    },
    openType: {
      type: String
    }
  },
  data() {
    return {
      visible: false,
      rules: {
        required: { required: true, message: '必填' }
      },
      title: '',
      form: {
        violationDate: '',
        violationName: '',
        violationType: '',
        violationVehicle: '',
        vehicleId: '',
        violationAddress: '',
        violationAddressCode: '',
        violationDetailAddress: '',
        violationDeduct: undefined,
        violationFine: undefined,
        violationRemark: '',
        violationFiles: ''
      },

      violationsTypeList: [
        {
          itemName: '闯红灯',
          itemNameValue: '闯红灯'
        },
        {
          itemName: '无证驾驶',
          itemNameValue: '无证驾驶'
        },
        {
          itemName: '超载',
          itemNameValue: '超载'
        },
        {
          itemName: '酒后驾驶',
          itemNameValue: '酒后驾驶'
        },
        {
          itemName: '超速行驶',
          itemNameValue: '超速行驶'
        },
        {
          itemName: '其他',
          itemNameValue: '其他'
        }
      ]
    };
  },
  watch: {
    show: {
      async handler(val) {
        this.$set(this, 'form', {});
        if (val) {
          this.$set(this, 'form', this.eachData);
          if (this.openType === 'add') {
            this.title = '违章记录新增';
          } else {
            this.title = '违章记录编辑';
          }

          this.$nextTick(() => {
            this.$refs.ruleForm?.clearValidate();
          });
        }
        this.visible = val;
      }
    }
  },
  methods: {
    /**@desc 校验输入两位小数 */
    validateTowDecimalPlaces(value, obj, attr) {
      let newVal = inputTowDecimalPlaces(value);
      this.$set(this[obj], attr, newVal);
    },

    inputBlur(event, formName, setKey) {
      let value = parseFloat(event.target.value);
      this.$set(this[formName], setKey, isNaN(value) ? '' : value);
    },

    async handleGetVehicleList(data) {
      let res = await vehicleInfoList({
        pageSize: 15,
        status: 1,
        sidx: 'create_date',
        sord: 'desc',
        ...data
      });

      if (res.success == false) {
        this.$message.error(res.message || '车辆数据获取失败');
        return false;
      }
      return res.rows;
    },

    async submit() {
      let validate = await this.$refs.ruleForm.validate().catch(res => res);
      if (!validate) {
        return;
      }

      const data = Object.assign({}, this.form);

      let API = null;
      let titleType = '';
      if (this.openType === 'add') {
        API = vehicleDriverViolationSave;
        titleType = '添加';
      } else {
        API = vehicleDriverViolationUpdate;
        titleType = '编辑';
      }
      const submitres = await API(data);

      if (!submitres.success) {
        this.$message.error(submitres.message || '操作失败');
        return;
      }
      this.$message.success(`违章记录${titleType}成功!`);
      this.$emit('refreshTable', data);
      this.close();
    },
    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-violation-records {
  .content {
    ::v-deep {
      .person-icon {
        margin-top: 3px;
        width: 24px;
        height: 24px;
        cursor: pointer;
      }
      .textarea {
        .el-textarea__inner {
          min-height: 110px !important;
          max-height: 200px !important;
        }
      }
    }
  }
}
</style>
