<template>
  <div ref="bodydiv">
    <el-popover
      ref="popover"
      placement="bottom-start"
      :visible-arrow="false"
      :width="poppverWidth"
      popper-class="worksheet-input-select"
      @show="handlePopoverShow"
      :disabled="disabled"
    >
      <div class="selection-list-content">
        <ul v-infinite-scroll="loadList" infinite-scroll-disabled="finished">
          <li
            v-for="(item, index) of toastList"
            :key="index"
            :value="item[valueName]"
            @click="handleSelect"
            class="toast-item"
            :class="item[valueName] == value ? 'active-toast-item' : ''"
          >
            <slot :data="item">
              {{ filterOptionLabel(labelName, item) }}
            </slot>
          </li>
        </ul>
        <p class="finished" v-if="this.finished">
          {{ this.toastList.length ? finishedText : emptyText }}
        </p>
      </div>

      <el-input
        slot="reference"
        v-model="selectOptionName"
        @input="handleInputChange"
        @blur="handleInputBlur"
        :placeholder="oldValOptionName || placeholder"
        class="addform-input"
        :disabled="disabled"
      >
        <i
          slot="suffix"
          class="el-input__icon el-icon-circle-close el-input__clear input-clear-icon"
          :class="{ 'has-value-input': selectOptionName }"
          @click.stop="handleInputClear"
        ></i>
      </el-input>
    </el-popover>
  </div>
</template>

<script>
export default {
  model: {
    prop: 'value',
    event: 'valuechange'
  },
  props: {
    value: {
      default: () => {
        return null;
      }
    },
    inputText: String,
    valueName: {
      //选中后值的字段名称
      type: String,
      default: () => {
        return 'value';
      }
    },
    labelName: {
      //展示的字段的名称
      type: String,
      default: () => {
        return 'name';
      }
    },
    delayTime: {
      type: Number,
      default: () => {
        return 500;
      }
    },
    emptyText: {
      //首次加载无数据提示文本
      type: String,
      default: () => {
        return '暂无数据';
      }
    },
    finishedText: {
      //加载完成提示文本
      type: String,
      default: () => {
        return '到底啦';
      }
    },
    loadOnce: {
      //数据加载是否是一次完成
      type: Boolean,
      default: () => {
        return false;
      }
    },
    placeholder: {
      type: String,
      default: () => '请输入'
    },
    inputUsefull: {
      //输入框内的内容是否可以作为值
      type: Boolean,
      default: () => false
    },
    disabled: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      selectOptionName: '', //选中值的名称
      oldValOptionName: '', //之前选中的值

      poppverWidth: null, //气泡框的宽度
      pageNo: 1, //当前加载后的页码

      finished: false, //是否加载完成
      loadTimer: null, //加载防抖定时器

      toastList: [] //提示内容列表
    };
  },
  created() {
    this.loadList();
  },
  mounted() {
    this.poppverWidth = this.$refs.bodydiv.clientWidth;
    this.poppverWidth > 300 ? null : (this.poppverWidth = 300);
  },
  watch: {
    value: {
      handler: function(val) {
        if (!val) {
          this.selectOptionName = '';
          this.oldValOptionName = '';
          this.pageNo = 1;
          this.finished = false;
          return;
        }
      },
      immediate: true
    },
    inputText: {
      handler: function(val) {
        this.selectOptionName = this.oldValOptionName = val;
      },
      immediate: true
    }
  },
  methods: {
    //首次渲染检查是否有数据
    firstRender(list) {
      list.forEach((item, index) => {
        if (item[this.valueName] == this.value) {
          this.selectOptionName = item[this.labelName];
          this.oldValOptionName = item[this.labelName];
          this.pageNo = 1;
          this.finished = false;
          // this.loadList();
        }
      });
    },
    //加载下拉选择内容
    loadList() {
      let data = {
        text:
          !this.inputUsefull && this.selectOptionName == this.oldValOptionName
            ? ''
            : this.selectOptionName,
        pageNo: this.pageNo
      };
      this.$emit('load', data, res => {
        if (!res || res == 'finished' || !res.length) {
          if (this.loadOnce) {
            this.toastList = res || [];
          }
          this.finished = true;
          if (this.pageNo == 1) {
            this.toastList = [];
          }
          return;
        }

        if (this.loadOnce) {
          this.toastList = res || [];
          this.finished = true;
        } else {
          //根据页码确认数据连接方式
          this.pageNo == 1
            ? (this.toastList = res || [])
            : this.toastList.push(...(res || []));
        }

        //如果加载的数据没有，则表示已经加载完成
        if (!res.length) {
          this.finished = true;
        }

        //当输入框没有值，但是v-model又有值时， 即首次渲染组件时选中v-model绑定的值
        if (!this.selectOptionName && this.value && !this.oldValOptionName) {
          this.firstRender(res);
        }
        this.pageNo++;
      });
    },
    //处理输入框文本内容改变，远程搜索数据
    handleInputChange() {
      this.loadTimer && clearTimeout(this.loadTimer);
      this.loadTimer = setTimeout(() => {
        this.pageNo = 1;
        this.finished = false;

        if (this.inputUsefull) {
          this.$emit('valuechange', this.selectOptionName);
          this.oldValOptionName = this.selectOptionName;
        }
        this.$emit('select', null, null, null);
        this.$refs.popover.showPopper && this.loadList();
        //如果搜索后popover并没有展示，则主动显示
        if (!this.$refs.popover.showPopper) {
          this.$refs.popover.doShow();
        }
      }, this.delayTime);
    },
    //处理选择事件
    handleSelect(e) {
      let node = e.target,
        value = node.getAttribute('value'),
        label = node.innerText;
      //实现选中相同数据则取消选中， 先注释掉
      //     oldCheckNode = document.getElementsByClassName('toast-item-checked')[0];
      //     oldCheckNode ? oldCheckNode.className = 'toast-item' : null;
      // if(value == this.value){
      //     this.selectOptionName = '';
      //     this.$emit('valuechange', null);
      //     this.$emit('select', null, null, e);
      // }
      // else{
      //     node.className += ' toast-item-checked';
      //     this.selectOptionName = label;
      //     this.$emit('valuechange', value);
      //     this.$emit('select', value, label, e );
      // }
      this.selectOptionName = label;
      this.oldValOptionName = label;
      this.$emit('update:inputText', label);
      this.$emit('valuechange', value);
      this.$emit('select', value, label, e);
      this.$refs.popover.doClose();
    },
    handleInputBlur() {
      if (this.selectOptionName != this.oldValOptionName) {
        this.finished = false;
        this.pageNo = 1;
      }
      this.selectOptionName = this.oldValOptionName;
    },
    handleInputClear() {
      this.selectOptionName = '';
      this.oldValOptionName = '';
      this.$emit('valuechange', '');
    },
    handlePopoverShow() {
      this.pageNo == 1 ? this.loadList() : null;
      if (!this.toastList.length && this.finished) {
        setTimeout(() => {
          this.$refs.popover.doClose();
        }, 1000);
      }
    },
    //过滤选项label显示内容
    filterOptionLabel(labelName, optionItem) {
      let valueList = labelName.match(/\$\{[^$,{,}]+\}/g) || [];
      if (!valueList.length) {
        return optionItem[labelName];
      }

      // valueList = valueList.map(item => item.replace(/[$,{,}]/g, ''));
      valueList.forEach(item => {
        let valueName = item.replace(/[$,{,}]/g, ''),
          value = optionItem[valueName];

        labelName = labelName.replaceAll(item, value || '');
      });
      return labelName;
    }
  }
};
</script>

<style lang="scss" scoped>
.selection-list-content {
  max-height: 200px;
  overflow: auto;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 6px;
    height: 50px;
    background: rgba(153, 153, 153, 0.4);
    &:hover {
      background-color: rgba(153, 153, 153, 0.8);
    }
  }
  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    background: #fff;
  }
}
.toast-item {
  line-height: 32px;
  padding: 0 8px;
  &:hover,
  &:focus {
    background-color: #f5f7fa;
  }
}
.active-toast-item {
  font-weight: 600;
  color: $theme-color;
}
.toast-item-checked {
  background-color: #f5f7fa;
}
.finished {
  line-height: 32px;
  padding: 0 8px;
  text-align: center;
}
/deep/ {
  .addform-input .input-clear-icon {
    display: none;
  }
  .addform-input:hover .has-value-input {
    display: inline-block;
  }
}
</style>
<style>
.worksheet-input-select {
  padding: 12px 0;
  margin-top: 8px !important;
}
</style>
