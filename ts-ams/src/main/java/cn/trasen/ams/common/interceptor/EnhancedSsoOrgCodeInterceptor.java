package cn.trasen.ams.common.interceptor;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.BinaryExpression;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.SetOperationList;
import net.sf.jsqlparser.statement.select.ParenthesedSelect;
import net.sf.jsqlparser.statement.select.FromItem;
import net.sf.jsqlparser.statement.select.Join;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.plugin.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.*;
import java.util.List;
import java.util.ArrayList;

/**
 * 增强版多机构拦截器 - 基于JSQLParser实现
 * 提供更安全、严谨的SQL解析和修改功能
 *
 * 主要改进：
 * 1. 使用JSQLParser进行严格的SQL解析，避免正则表达式的不准确性
 * 2. 支持复杂SQL语句（子查询、JOIN、UNION等）
 * 3. 更好的错误处理和日志记录
 * 4. 模块化设计，便于维护和扩展
 * 5. 配置化管理，支持动态调整
 * 6. SQL注入防护和安全性增强
 *
 * <AUTHOR>
 * @date 2025/9/4
 */
@Intercepts({
        @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
public class EnhancedSsoOrgCodeInterceptor implements Interceptor {

    private static final Logger log = LoggerFactory.getLogger(EnhancedSsoOrgCodeInterceptor.class);

    // 机构代码字段名
    private static final String ORG_CODE_COLUMN = "sso_org_code";

    // 需要排除的表
    private static final Set<String> EXCLUDE_TABLES = new HashSet<>(Arrays.asList(
            "d_category22"
    ));

    // 需要添加机构代码条件的表前缀
    private static final Set<String> INCLUDE_TABLE_PREFIXES = new HashSet<>(Arrays.asList(
            "d_", "m_", "call_", "civil_", "comm_", "cust_", "dept_", "device_", "di_", "dp_",
            "emp_", "gov_", "hr_", "hrms_", "importdata_", "jc_", "kq_", "med_", "new_",
            "political_", "satisfaction_", "scheduling_", "sms_", "t_", "tbl_", "thr_",
            "toa_", "user_", "wf_", "ws_", "zdy_", "zp_", "zt_", "ts_", "c_", "thps_"
    ));

    // 配置属性
    private boolean enableSqlModification = true;
    private boolean enableLogging = true;
    private boolean enableStrictMode = true; // 严格模式，解析失败时抛出异常

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long startTime = System.currentTimeMillis();

        if (enableLogging) {
            log.info("=== 增强版多机构拦截器开始执行 ===");
        }

        try {
            StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
            String originalSql = statementHandler.getBoundSql().getSql();

            if (enableLogging) {
                log.debug("原始SQL: {}", originalSql);
            }

            // 获取用户信息
            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            if (user == null || isBlankString(user.getCorpcode())) {
                if (enableLogging) {
                    log.warn("无法获取用户信息或机构代码为空，跳过SQL修改");
                }
                return invocation.proceed();
            }

            // 处理SQL
            String modifiedSql = processSqlWithJSQLParser(originalSql, user.getCorpcode());

            // 应用SQL修改
            if (!originalSql.equals(modifiedSql) && enableSqlModification) {
                applySqlModification(statementHandler, modifiedSql);
            }

            Object result = invocation.proceed();

            if (enableLogging) {
                long executionTime = System.currentTimeMillis() - startTime;
                log.info("=== 增强版多机构拦截器执行完成，耗时: {}ms ===", executionTime);
            }

            return result;

        } catch (Exception e) {
            log.error("增强版多机构拦截器执行异常", e);
            if (enableStrictMode) {
                throw e;
            }
            return invocation.proceed();
        }
    }

    /**
     * 使用JSQLParser处理SQL语句
     */
    private String processSqlWithJSQLParser(String sql, String orgCode) {
        if (isBlankString(sql)) {
            return sql;
        }

        try {
            // 解析SQL语句
            Statement statement = CCJSqlParserUtil.parse(sql);

            // 只处理SELECT语句
            if (!(statement instanceof Select)) {
                if (enableLogging) {
                    log.debug("非SELECT语句，跳过处理: {}", statement.getClass().getSimpleName());
                }
                return sql;
            }

            Select selectStatement = (Select) statement;

            // 处理不同类型的SELECT语句
            boolean modified = processSelect(selectStatement, orgCode);

            if (modified) {
                String modifiedSql = selectStatement.toString();

                if (enableLogging) {
                    log.info("SQL修改完成，机构代码: {}", orgCode);
                    log.debug("修改后SQL: {}", modifiedSql);
                }

                return modifiedSql;
            }

            return sql;

        } catch (JSQLParserException e) {
            log.error("SQL解析失败: {}", e.getMessage());
            if (enableStrictMode) {
                throw new RuntimeException("SQL解析失败", e);
            }
            return sql;
        } catch (Exception e) {
            log.error("处理SQL时发生异常", e);
            if (enableStrictMode) {
                throw new RuntimeException("处理SQL时发生异常", e);
            }
            return sql;
        }
    }

    /**
     * 处理Select语句
     */
    private boolean processSelect(Select select, String orgCode) {
        boolean modified = false;

        try {
            // 在JSQLParser 4.9中，直接处理Select对象
            // 检查是否是PlainSelect
            if (select.getSelectBody() != null) {
                Object selectBody = select.getSelectBody();
                if (selectBody instanceof PlainSelect) {
                    modified = processPlainSelect((PlainSelect) selectBody, orgCode);
                } else if (selectBody instanceof SetOperationList) {
                    modified = processSetOperationList((SetOperationList) selectBody, orgCode);
                } else {
                    if (enableLogging) {
                        log.debug("不支持的SELECT类型: {}", selectBody.getClass().getSimpleName());
                    }
                }
            }
        } catch (Exception e) {
            if (enableLogging) {
                log.warn("处理Select语句时发生异常: {}", e.getMessage());
            }
        }

        return modified;
    }

    /**
     * 处理普通SELECT语句
     */
    private boolean processPlainSelect(PlainSelect plainSelect, String orgCode) {
        boolean modified = false;
        List<Table> tablesToAddCondition = new ArrayList<>();

        // 收集主表
        FromItem fromItem = plainSelect.getFromItem();
        if (fromItem instanceof Table) {
            Table table = (Table) fromItem;
            if (shouldAddOrgCodeCondition(table.getName())) {
                tablesToAddCondition.add(table);
            }
        } else if (fromItem instanceof ParenthesedSelect) {
            // 递归处理子查询
            ParenthesedSelect parenthesedSelect = (ParenthesedSelect) fromItem;
            Select subSelectStatement = parenthesedSelect.getSelect();
            if (processSelect(subSelectStatement, orgCode)) {
                modified = true;
            }
        }

        // 收集JOIN中的表
        List<Join> joins = plainSelect.getJoins();
        if (joins != null) {
            for (Join join : joins) {
                FromItem rightItem = join.getRightItem();
                if (rightItem instanceof Table) {
                    Table joinTable = (Table) rightItem;
                    if (shouldAddOrgCodeCondition(joinTable.getName())) {
                        tablesToAddCondition.add(joinTable);
                    }
                } else if (rightItem instanceof ParenthesedSelect) {
                    // 递归处理JOIN中的子查询
                    ParenthesedSelect parenthesedSelect = (ParenthesedSelect) rightItem;
                    Select subSelectStatement = parenthesedSelect.getSelect();
                    if (processSelect(subSelectStatement, orgCode)) {
                        modified = true;
                    }
                }
            }
        }

        // 统一在WHERE子句中添加所有表的机构代码条件
        if (!tablesToAddCondition.isEmpty()) {
            if (addOrgCodeConditionsToWhere(plainSelect, tablesToAddCondition, orgCode)) {
                modified = true;
            }
        }

        return modified;
    }

    /**
     * 处理UNION等集合操作
     */
    private boolean processSetOperationList(SetOperationList setOperationList, String orgCode) {
        boolean modified = false;

        try {
            // 在JSQLParser 4.9中，尝试获取Select列表
            List<?> selects = setOperationList.getSelects();
            if (selects != null) {
                for (Object selectObj : selects) {
                    if (selectObj instanceof PlainSelect) {
                        if (processPlainSelect((PlainSelect) selectObj, orgCode)) {
                            modified = true;
                        }
                    } else if (selectObj instanceof Select) {
                        if (processSelect((Select) selectObj, orgCode)) {
                            modified = true;
                        }
                    }
                }
            }
        } catch (Exception e) {
            if (enableLogging) {
                log.warn("处理SetOperationList时发生异常: {}", e.getMessage());
            }
        }

        return modified;
    }

    /**
     * 统一在WHERE子句中为所有表添加机构代码条件
     */
    private boolean addOrgCodeConditionsToWhere(PlainSelect plainSelect, List<Table> tables, String orgCode) {
        if (tables.isEmpty()) {
            return false;
        }

        // 检查是否已经存在机构代码条件
        if (hasOrgCodeCondition(plainSelect.getWhere())) {
            if (enableLogging) {
                log.debug("WHERE子句中已包含{}条件，跳过修改", ORG_CODE_COLUMN);
            }
            return false;
        }

        // 为每个表创建机构代码条件
        Expression combinedOrgCondition = null;
        for (Table table : tables) {
            Expression orgCodeCondition = createOrgCodeCondition(table, orgCode);

            if (combinedOrgCondition == null) {
                combinedOrgCondition = orgCodeCondition;
            } else {
                combinedOrgCondition = new AndExpression(combinedOrgCondition, orgCodeCondition);
            }

            if (enableLogging) {
                log.debug("为表 {} 添加机构代码条件: {}", table.getName(), orgCode);
            }
        }

        // 添加到WHERE子句
        Expression existingWhere = plainSelect.getWhere();
        if (existingWhere == null) {
            plainSelect.setWhere(combinedOrgCondition);
        } else {
            AndExpression andExpression = new AndExpression(combinedOrgCondition, existingWhere);
            plainSelect.setWhere(andExpression);
        }

        return true;
    }

    /**
     * 为JOIN表添加机构代码条件
     * 修复：不在JOIN的ON条件中添加机构代码条件，而是在主WHERE子句中添加
     */
    private boolean addOrgCodeConditionToJoin(Join join, Table table, String orgCode) {
        // 对于JOIN表，我们不在ON条件中添加机构代码条件
        // 而是返回false，让调用方在主WHERE子句中统一处理
        if (enableLogging) {
            log.debug("JOIN表 {} 的机构代码条件将在主WHERE子句中处理", table.getName());
        }
        return false;
    }

    /**
     * 创建机构代码条件表达式
     */
    private Expression createOrgCodeCondition(Table table, String orgCode) {
        // 构建列引用
        Column orgCodeColumn = new Column();

        // 在JSQLParser 4.9中，处理别名的方式有所变化
        if (table.getAlias() != null) {
            // 使用别名名称创建表引用
            Table aliasTable = new Table();
            aliasTable.setName(table.getAlias().getName());
            orgCodeColumn.setTable(aliasTable);
        } else {
            orgCodeColumn.setTable(table);
        }
        orgCodeColumn.setColumnName(ORG_CODE_COLUMN);

        // 构建等值条件
        EqualsTo equalsTo = new EqualsTo();
        equalsTo.setLeftExpression(orgCodeColumn);
        equalsTo.setRightExpression(new StringValue(orgCode));

        return equalsTo;
    }

    /**
     * 检查表达式中是否已包含机构代码条件
     */
    private boolean hasOrgCodeCondition(Expression expression) {
        if (expression == null) {
            return false;
        }

        return containsOrgCodeColumn(expression);
    }

    /**
     * 递归检查表达式中是否包含机构代码列
     */
    private boolean containsOrgCodeColumn(Expression expression) {
        if (expression instanceof Column) {
            Column column = (Column) expression;
            return ORG_CODE_COLUMN.equalsIgnoreCase(column.getColumnName());
        } else if (expression instanceof BinaryExpression) {
            BinaryExpression binaryExpression = (BinaryExpression) expression;
            return containsOrgCodeColumn(binaryExpression.getLeftExpression()) ||
                    containsOrgCodeColumn(binaryExpression.getRightExpression());
        }

        return false;
    }

    /**
     * 判断是否需要添加机构代码条件
     */
    private boolean shouldAddOrgCodeCondition(String tableName) {
        if (isBlankString(tableName)) {
            return false;
        }

        String pureTableName = getPureTableName(tableName);

        // 检查排除列表
        if (EXCLUDE_TABLES.contains(pureTableName.toLowerCase())) {
            return false;
        }

        // 检查包含前缀
        return INCLUDE_TABLE_PREFIXES.stream()
                .anyMatch(prefix -> pureTableName.toLowerCase().startsWith(prefix.toLowerCase()));
    }

    /**
     * 获取纯表名（去除schema和引号）
     */
    private String getPureTableName(String tableName) {
        if (isBlankString(tableName)) {
            return tableName;
        }

        String pureTableName = tableName;

        // 去除schema前缀
        if (tableName.contains(".")) {
            String[] parts = tableName.split("\\.");
            pureTableName = parts[parts.length - 1];
        }

        // 去除引号
        if (pureTableName.startsWith("`") && pureTableName.endsWith("`")) {
            pureTableName = pureTableName.substring(1, pureTableName.length() - 1);
        }
        if (pureTableName.startsWith("\"") && pureTableName.endsWith("\"")) {
            pureTableName = pureTableName.substring(1, pureTableName.length() - 1);
        }

        return pureTableName;
    }

    /**
     * 应用SQL修改
     */
    private void applySqlModification(StatementHandler statementHandler, String modifiedSql) {
        try {
            Object boundSql = statementHandler.getBoundSql();

            // 使用反射修改SQL
            java.lang.reflect.Field sqlField = boundSql.getClass().getDeclaredField("sql");
            sqlField.setAccessible(true);
            sqlField.set(boundSql, modifiedSql);

            if (enableLogging) {
                log.debug("SQL修改成功");
            }

        } catch (Exception e) {
            log.error("应用SQL修改失败", e);
            if (enableStrictMode) {
                throw new RuntimeException("应用SQL修改失败", e);
            }
        }
    }

    /**
     * 检查字符串是否为空或空白
     */
    private boolean isBlankString(String str) {
        return str == null || str.trim().isEmpty();
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        if (properties != null) {
            String sqlModificationProp = properties.getProperty("enableSqlModification");
            if (sqlModificationProp != null) {
                this.enableSqlModification = Boolean.parseBoolean(sqlModificationProp);
            }

            String loggingProp = properties.getProperty("enableLogging");
            if (loggingProp != null) {
                this.enableLogging = Boolean.parseBoolean(loggingProp);
            }

            String strictModeProp = properties.getProperty("enableStrictMode");
            if (strictModeProp != null) {
                this.enableStrictMode = Boolean.parseBoolean(strictModeProp);
            }

            log.info("增强版多机构拦截器配置 - 启用SQL修改: {}, 启用日志: {}, 严格模式: {}",
                    enableSqlModification, enableLogging, enableStrictMode);
        }
    }
}