package cn.trasen.ams.device.bean.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.device
 * @className: DeviceImport
 * @author: chenbin
 * @description: 设备导入
 * @date: 2024/9/14 14:03
 * @version: 1.0
 */
@Data

public class DeviceImport extends DeviceExtResp {


    @ApiModelProperty(value = "资产名称")
    @Excel(name = "资产名称")
    private String skuName;

    @ApiModelProperty(value = "设备字典编码")
    @Excel(name = "字典编码")
    private String skuCode;


}
