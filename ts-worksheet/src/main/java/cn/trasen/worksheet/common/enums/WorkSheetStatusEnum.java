package cn.trasen.worksheet.common.enums;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date: 2021/6/18 9:56
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */

/**
 * 工单状态
 */
@Getter
public enum WorkSheetStatusEnum {
    /**
     * 待派单
     */
    SNET("1","待派单"),

    /**
     * 待接单
     */
    WAITING("2","待接单"),

    /**
     * 处理中
     */
    PROCESSING("3","处理中"),

    /**
     * 待验收
     */
    ACCEPTANCE("4","待验收"),

    /**
     * 待评价
     */
    EVALUATE("5","待评价"),

    /**
     * 已完成
     */
    COMPLETED("6","已完成"),

    /**
     * 已暂停
     */
    SUSPENDED("7","已暂停"),

    /**
     * 已终止
     */
    TERMINATED("8","已终止"),

    /**
     * 完成集合
     */
    STATISTICS_TO_COMPLETE("5,6,8"),

    /**
     * 移动端在办集合
     */
    MOBILE_PROCESS("3,7"),


    /**
     * 处理中集合
     */
    STATISTICS_TO_UNFINISHED("1,2,3,4,7");

    /**
     * 显示名称
     */
    private String value;

    private String name;

    WorkSheetStatusEnum(String value) {
        this.value = value;
    }

    WorkSheetStatusEnum(String value, String name) {
        this.name = name;
        this.value = value;
    }


    public void setValue(String value) {
        this.value = value;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static WorkSheetStatusEnum getByValue(String value) {
        WorkSheetStatusEnum workSheetStatusEnum;
        for (int i = 0; i < WorkSheetStatusEnum.values().length; i++) {
            workSheetStatusEnum = WorkSheetStatusEnum.values()[i];
            if (workSheetStatusEnum.getValue().equals(value)) {
                return workSheetStatusEnum;
            }
        }
        return null;
    }

    /**
     * 填充工单状态，业务数据量（无工单状态业务，补0）
     * @param map
     */
    public static void fillStatusInfo(Map<String,Object> map) {
        for (int i = 0; i < WorkSheetStatusEnum.values().length; i++) {
            if(null == map.get(WorkSheetStatusEnum.values()[i].value)){
                if(null != WorkSheetStatusEnum.values()[i].name){
                    map.put(WorkSheetStatusEnum.values()[i].value, "0");
                }
            }
        }
    }

    public static void fillStatusInfo(List<Map<String, Object>> list,String keyName,String valueName) {
        for (int i = 0; i < WorkSheetStatusEnum.values().length; i++) {
            int j = i;
            if(CollectionUtil.isEmpty(list.stream().filter(temp -> WorkSheetStatusEnum.values()[j].value.equals(temp.get(keyName))).collect(Collectors.toList()))){
                if(null != WorkSheetStatusEnum.values()[i].name) {
                    Map<String, Object> map = Maps.newHashMap();
                    map.put(keyName, WorkSheetStatusEnum.values()[i].value);
                    map.put(valueName, 0);
                    list.add(map);
                }
            }
        }
    }
}
