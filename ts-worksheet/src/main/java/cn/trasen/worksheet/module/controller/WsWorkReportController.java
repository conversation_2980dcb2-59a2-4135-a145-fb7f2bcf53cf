package cn.trasen.worksheet.module.controller;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.common.util.ExportUtil;
import cn.trasen.worksheet.module.dto.outVo.WsWorkReportOutVo;
import cn.trasen.worksheet.module.service.WsWorkReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

@Api(tags = "工单日报管理")
@RestController
@Slf4j
public class WsWorkReportController {

    @Autowired
    private WsWorkReportService wsWorkReportService;

    @ControllerLog(description="移动端查看工单日报详情")
    @ApiOperation(value = "移动端查看工单日报详情", notes = "移动端查看工单日报详情")
    @GetMapping("/workReport/workReportInfo/{pkWorkReportId}")
    public PlatformResult<WsWorkReportOutVo> workReportInfo(@PathVariable @ApiParam(value = "工单日志id") String pkWorkReportId){
        return PlatformResult.success(wsWorkReportService.workReportInfoById(pkWorkReportId));
    }

    @ApiOperation(value = "导出", notes = "导出")
    @RequestMapping(value = "/test/export", method = {RequestMethod.POST, RequestMethod.GET})
    public void export( HttpServletResponse response, HttpServletRequest request) throws Exception {

        String excelName = "组织机构";
        List<String> headList = new ArrayList<>();
        headList.add("组织名称");
        headList.add("上级组织");
        headList.add("组织类型");
        headList.add("人数");
        headList.add("领导");
        headList.add("状态");

        headList.add("组织描述");

        List<String> fieldList = new ArrayList<>();
        fieldList.add("组织名称");
        fieldList.add("上级组织");
        fieldList.add("组织类型");
        fieldList.add("人数");
        fieldList.add("领导");
        fieldList.add("状态");
        fieldList.add("组织描述");
        List<Map<String, Object>> dataList = new ArrayList<>();
        fieldList.forEach(temp ->{
            Map map = new HashMap<>();
            map.put("组织名称", "组织名称组织名称组织名称组织名称组织名称");
            map.put("上级组织", "组织名称组织名称组");
            map.put("组织类型", "组织名称组织名称组织名称");
            map.put("人数", "组织名称123");
            map.put("领导", "组织名称321");
            map.put("状态", "组织名称1");
            map.put("组织描述", "组织名称2");
            dataList.add(map);
        });
        ExportUtil.createExcel(excelName, headList, fieldList, dataList, response, request);
    }

}
