//地址栏参数
var uriParams = getParmas();
var params = {
    baseData: null,
    database: null,
    formData: null,
};
var userInfo;
var wfParams;
//业务数据
var formData = {};
var cancelLeave = {};
var clDpFieldRelation = {};
var ksWorkorderSetting = {}
var lxWorkorderSetting = {}
var layForm = null;
var layDate = null;
var layTrasen = null;
var zTreeSearch = null;
var layUpload = null;
var wfData = null;
var copyUsers = [];

var getLocationSearchParams = function () {
    let data = {};
    let src = window.location.href;
    let index = src.indexOf("?");
    if (index === -1) {
      return data;
    }
    let dataStr = src.substring(src.indexOf("?") + 1);
    let dataArray = dataStr.split("&"); 
    dataArray.forEach((str)=>{
      let param =str.split("=");
      data[param[0]] = param[1];
    })
    return data;
}
var initObj = {
    init: function init() {
        layui.use(['form', 'laydate', 'trasen', 'zTreeSearch', 'upload'], function () {
            layForm = layui.form;
            layDate = layui.laydate;
            layTrasen = layui.trasen;
            zTreeSearch = layui.zTreeSearch;
            layUpload = layui.upload;
            const searchParams = getLocationSearchParams()

            if (searchParams.showWorkflowInfo) {
                const wfInstanceId = searchParams.wfInstanceId;
                $.quoteFun('../view-new/processView/modules/taskList/index', {
                    node: $('#taskBox'),
                    wfInstanceId,
                });
            }

            $.quoteFun('/commonPage/wfPic/index', {
                node: $('#wfBox'),
                wfDefinitionId: searchParams.wfDefinitionId,
                workflowName: searchParams.workflowName,
                workflowNo: searchParams.workflowNo,
                currentStepNo: searchParams.currentStepNo || 'start',
            });

        });
    },
};
initObj.init();
