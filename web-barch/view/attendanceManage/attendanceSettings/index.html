<style>
    #processStart {
        height: 100%;
        width: 100%;
        overflow: hidden;
        overflow-y: auto;
        background: #fff;
    }
    
    #processStart .tab {
        height: 40px;
        line-height: 40px;
        color: #333;
        border-bottom: 1px solid #ccc;
    }
    
    #processStart .tab span {
        padding: 0 10px;
        display: inline-block;
        margin-right: 10px;
        cursor: pointer;
        position: relative;
        user-select: none;
    }
    
    #processStart .tab .check {
        color: #37b2ff;
    }
    
    #processStart .tab .check:after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: 2px;
        background: #37b2ff;
    }
    
    #processStart .tab-content {
        padding: 10px 20px 20px 20px;
        display: none;
    }
    
    #processStart .tab-content.show {
        display: block;
    }
    
    #processStart p.title {
        font-size: 14px;
        line-height: 30px;
    }
    
    #processStart .dailyProcess li {
        margin: 5px 0;
        line-height: 25px;
    }
    
    #processStart .dailyProcess .No {
        display: inline-block;
        height: 25px;
        width: 25px;
        line-height: 25px;
        text-align: center;
        margin-right: 10px;
        background: #ccc;
        color: #fff;
        border-radius: 50%;
    }
    
    #processStart .oftenProcess {
        cursor: pointer;
    }
    
    .layui-input,
    .layui-textarea {
        /* display: block; */
        width: 100%!important;
        /* padding-left: 10px; */
    }
    
    #processStart .layui-input-block {
        width: 80%;
        margin-left: 160px;
    }
    
    #processStart .layui-form-label {
        width: 140px!important;
    }
    
    #processStart .splitLine {
        float: left;
        margin-left: 15px;
        margin-right: 15px;
    }
    
    #processStart .splitLine i {
        height: 28px;
        line-height: 28px;
        font-size: 16px;
        color: rgba(0, 0, 0, .5);
    }
    
    #processStart .layer_btnBox {
        width: 100%;
        text-align: center;
    }
    
    .addShiftBtn {
        border: 1px solid #37b2ff;
        width: 83px;
        height: 30px;
        border-radius: 5px;
        line-height: 30px;
        text-align: center;
        margin-left: 43px;
        background: white;
        display: block;
    }
    
    .attendanceSettingsShiftInfoDel i {
        font-size: 22px;
        color: #d9001b;
        position: absolute;
        right: 15px;
        top: 50%;
    }
    
    .shiftInformation {
        background: #f2f2f2;
        position: relative;
        margin-top: 10px;
        padding-top: 10px;
        margin-bottom: 10px;
        padding-bottom: 10px;
    }
</style>
<div  id="processStart">
    <form action="" class="layui-form">
        <div class="tab">
            <span class="check">固定考勤规则</span>
            <span>排班考勤规则</span>
        </div>
        <div class="tab-content show">
            <div class="layui-col-md11">
                <label class="shell-layui-form-label"><span style="color: #ff0000;">*</span>所属部门</label>
                <div class="shell-layer-input-box layui-form-select" id="">
                    <input type="text" autocomplete="off" name="deptname" class="layui-input" placeholder="请选择" lay-verify="required" id="systemmanageOrgDepUserPrevDep" zTreeLick="click">
                    <i class="layui-edge"></i>
                </div>
            </div>


            <div class="layui-form-item">
                <div class="layer-bom">
                    <label class="layui-form-label"><span class="required">*</span>上下班时段：</label>
                    <div class="layui-input-inline">
                        <input type="text" lay-verify="required" class="layui-input" id="workTime" name="workTime" placeholder=" ">
                    </div>
                    <div class="splitLine">
                        <i class="fa fa-minus"></i>
                    </div>

                    <div class="layui-input-inline">
                        <input type="text" lay-verify="required" class="layui-input" id="offWorkTime" name="offWorkTime" placeholder=" ">
                    </div>


                </div>

            </div>
            <div class="layui-form-item">
                <div class="layer-bom">
                    <label class="layui-form-label"><span class="required">*</span>午休时段：</label>
                    <div class="layui-input-inline">
                        <input type="text" lay-verify="required" class="layui-input" id="lunchBreakStartTime" name="lunchBreakStartTime" placeholder=" ">
                    </div>
                    <div class="splitLine">
                        <i class="fa fa-minus"></i>
                    </div>

                    <div class="layui-input-inline">
                        <input type="text" lay-verify="required" class="layui-input" id="lunchBreakEndTime" name="lunchBreakEndTime" placeholder=" ">
                    </div>


                </div>

            </div>
            <div class="layer-bom">
                <label class="layui-form-label"><span class="required">*</span>午休是否需要打卡：</label>
                <div class="layui-input-block">
                    <input type="checkbox" checked="" name="open" lay-verify="required" lay-skin="switch" lay-filter="switchTest" lay-text=" ">

                </div>

            </div>
            <div class="layer-bom">
                <label class="layui-form-label"><span class="required">*</span> 考勤地点：</label>
                <div class="layui-input-block">
                    <input type="tex" name="postName" value="" lay-verify="required" autocomplete="off" class="layui-input">
                </div>

            </div>
            <div class="layer-bom">
                <label class="layui-form-label"><span class="required">*</span> 允许偏差范围：</label>
                <div class="layui-input-block">
                    <input type="tex" name="postName" value="" lay-verify="required" autocomplete="off" class="layui-input">
                </div>

            </div>
            <div class="layer_btnBox">

                <button type="button" class="layer_btn" lay-submit="" lay-filter="workExperienceAddFormSaveFilter">保存</button>

            </div>
        </div>
        <div class="tab-content">
            <div class="layer-bom">
                <label class="layui-form-label"><span class="required">*</span> 考勤适用科室：</label>
                <div class="layui-input-block">
                    <select id=" " lay-verify="required" lay-search="" name=" " lay-filter="" lay-search>
                    <option value=""></option>
                </select>
                </div>

            </div>
            <span class="addShiftBtn" id="addShiftButton">
                添加班次 

            </span>
            <div class="layui-form-item shiftInformation">
                <div class="attendanceSettingsShiftInfoDel" id="attendanceSettingsShiftInfoDel">
                    <i class="icon fa fa-minus-square"></i>
                </div>
                <div class="layer-bom">
                    <label class="layui-form-label"><span class="required">*</span> 班次名称：</label>
                    <div class="layui-input-block">
                        <input type="text" name="postName" readonly value="" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>

                </div>
                <div class="layer-bom">
                    <label class="layui-form-label"><span class="required">*</span>考勤时段：</label>
                    <div class="layui-input-inline">
                        <input type="text" readonly lay-verify="required" class="layui-input" id=" " name=" " placeholder=" ">
                    </div>
                    <div class="splitLine">
                        <i class="fa fa-minus"></i>
                    </div>

                    <div class="layui-input-inline">
                        <input type="text" readonly lay-verify="required" class="layui-input" id=" " name=" " placeholder=" ">
                    </div>


                </div>

            </div>
            <div class="layer-bom">
                <label class="layui-form-label"><span class="required">*</span> 考勤地点：</label>
                <div class="layui-input-block">
                    <input type="tex" name="postName" value="" lay-verify="required" autocomplete="off" class="layui-input">
                </div>

            </div>
            <div class="layui-form-item">
                <div class="layer-bom">
                    <label class="layui-form-label"><span class="required">*</span> 经度：</label>
                    <div class="layui-input-inline">
                        <input type="tex" name="postName" value="" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>

                </div>
                <div class="layer-bom">
                    <label class="layui-form-label"><span class="required">*</span> 纬度：</label>
                    <div class="layui-input-inline">
                        <input type="tex" name="postName" value="" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>

                </div>

            </div>
            <div class="layer-bom">
                <label class="layui-form-label"><span class="required">*</span> 允许偏差范围：</label>
                <div class="layui-input-block">
                    <input type="tex" name="postName" value="" lay-verify="required" autocomplete="off" class="layui-input">
                </div>

            </div>
            <div class="layer_btnBox">

                <button type="button" class="layer_btn" lay-submit="" lay-filter="workExperienceAddFormSaveFilter">保存</button>

            </div>
        </div>
    </form>
</div>